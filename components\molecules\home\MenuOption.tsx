import {
  StyleSheet,
  View,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import Entypo from '@expo/vector-icons/Entypo';
import { ThemedText } from '@/components/ui/ThemedText';

type Props = {
  label: string;
  icon?: React.ReactNode;
  onPress: () => void;
  disabled?: boolean;
};

const MenuOption = ({ label, icon, onPress, disabled = false }: Props) => {
  return (
    <TouchableOpacity
      style={[styles.wrapper]}
      onPress={onPress}
      disabled={disabled}
    >
      <View style={styles.leftContent}>
        {icon && icon}
        <ThemedText
          type='defaultSemiBold'
          lightColor='#232429'
        >
          {label}
        </ThemedText>
      </View>
      <Entypo
        name='chevron-right'
        size={25}
        color='black'
        style={{ width: 24, height: 24 }}
      />
    </TouchableOpacity>
  );
};

export default MenuOption;

const styles = StyleSheet.create({
  wrapper: {
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    height: 56,
  },
  leftContent: {
    gap: 16,
    alignItems: 'center',
    flexDirection: 'row',
  },
  image: {
    width: 24,
    height: 24,
  },
  disabled: {
    opacity: 1,
  },
});
