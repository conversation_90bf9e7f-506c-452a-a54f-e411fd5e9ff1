import * as React from "react"
import Svg, {
    SvgProps,
    Path,
    Defs,
    LinearGradient,
    Stop,
} from "react-native-svg"
const GoldF = (props: SvgProps) => (
    <Svg
        width={24}
        height={24}
        fill="none"
        {...props}
    >
        <Path
            fill="url(#a)"
            d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2Zm0 2a8 8 0 1 0 0 16 8 8 0 0 0 0-16Zm0 12a1 1 0 1 1 0 2 1 1 0 0 1 0-2Zm0-9.5a3.625 3.625 0 0 1 1.348 6.99.8.8 0 0 0-.305.201c-.044.05-.051.114-.05.18L13 14a1 1 0 0 1-1.993.117L11 14v-.25c0-1.153.93-1.845 1.604-2.116a1.626 1.626 0 1 0-2.229-1.509 1 1 0 0 1-2 0A3.625 3.625 0 0 1 12 6.5Z"
        />
        <Defs>
            <LinearGradient
                id="a"
                x1={2}
                x2={22}
                y1={12.001}
                y2={12.001}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
        </Defs>
    </Svg>
)
export default GoldF
