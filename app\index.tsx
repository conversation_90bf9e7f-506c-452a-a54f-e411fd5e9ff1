import { View, StyleSheet, Text } from 'react-native';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import { router } from 'expo-router';
import useIsTablet from '@/hooks/useIsTablet';
import MainLogoConvenia from '@/components/icons/MainLogoConvenia';
import * as SecureStore from 'expo-secure-store';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import useBiometricsAuthentication from '@/hooks/useBiometricsAuthentication';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import LoginBiometricsButton from '@/components/atoms/LoginBiometricsButton';
import { usePreventDoubleClick } from '@/hooks/usePreventDoubleClick';
import * as Notifications from 'expo-notifications';

const Main = () => {
  const [loading, setLoading] = useState(true);
  const isTablet = useIsTablet();
  const [showBtnAuthBiometrics, setShowBtnAuthBiometrics] = useState(false);
  const { loading: biometricLoading, getBiometricsSetting, canAuthenticateWithBiometrics } = useBiometricsAuthentication();
  const { handlePress } = usePreventDoubleClick();

  // mientras se implementa la peristencia de sesion, se comenta el return y borramos el userToken del secure store

  useEffect(() => {
    const fetchData = async () => {
      try {
        await SecureStore.deleteItemAsync('userToken');
        setLoading(false);
      } catch (error) {
        console.error('Error al eliminar el token:', error);
        setLoading(false);
      }
    }
    fetchData();
  }, [])

  // use layout para borrar el Badge de notificaciones
  useLayoutEffect(() => {
    const clearBadge = async () => {
      try {
        await Notifications.setBadgeCountAsync(0);
      } catch (error) {
        console.error('Error al limpiar el badge de notificaciones:', error);
      }
    };
    clearBadge();
  }, []);


  const handlePressFingerprint = async () => {
    handlePress(() => {
      getBiometricsSetting();
    });
  };

  const showBiometricAtuhBtn = React.useCallback(
    async () => canAuthenticateWithBiometrics(),
    [canAuthenticateWithBiometrics]
  );

  useEffect(() => {
    const checkBiometricSupport = async () => {
      const canAuthenticate = await showBiometricAtuhBtn();
      if (canAuthenticate) {
        setShowBtnAuthBiometrics(true);
      } else {
        setShowBtnAuthBiometrics(false);
      }
    };

    checkBiometricSupport();
  }, [showBiometricAtuhBtn]);

  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: isTablet ? 0 : 113,
          gap: isTablet ? 50 : 113,
        },
      ]}
    >
      <MainLogoConvenia style={styles.logo} />
      <View
        style={[
          styles.content,
          {
            flexGrow: isTablet ? 0 : 1,
          },
        ]}
      >
        <Text style={styles.title}>¡Bienvenido!</Text>
        <View style={styles.btnContainer}>
          {showBtnAuthBiometrics && (
            <LoginBiometricsButton
              title='Ingresar con biométricos'
              disable={loading || biometricLoading}
              onPress={handlePressFingerprint}
            />
          )}
          <PrimaryButton
            title={showBtnAuthBiometrics ? 'Ingresar con email' : 'Iniciar sesión'}
            disable={loading || biometricLoading}
            onPress={() => handlePress(() => router.push('/auth/login'))}
          />
          <SecondaryButton
            title='Nuevo usuario'
            disabled={loading || biometricLoading}
            onPress={() => handlePress(() => router.push('/auth/register'))}
          />
        </View>
      </View>
      {
        loading || biometricLoading ? <LoadingScreen /> : null
      }
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    objectFit: 'contain',
    marginBottom: 20,
  },
  title: {
    fontSize: 36,
    color: 'white',
    fontWeight: '400',
    lineHeight: 43.57,
    fontFamily: 'Inter',
  },
  content: {
    width: '88%',
    maxWidth: 400,
    alignItems: 'center',
    gap: 24,
  },
  btnContainer: {
    gap: 24,
    width: '100%',
    alignItems: 'center',
  },
  welcome: {
    color: 'white',
    fontSize: 24,
    marginBottom: 20,
  },
});

export default Main;
