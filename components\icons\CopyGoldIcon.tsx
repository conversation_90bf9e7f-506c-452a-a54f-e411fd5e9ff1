import * as React from "react"
import Svg, {
    SvgProps,
    Path,
    Defs,
    LinearGradient,
    Stop,
} from "react-native-svg"
const CopyGoldIcon = (props: SvgProps) => (
    <Svg
        width={20}
        height={20}
        fill="none"
        {...props}
    >
        <Path
            stroke="url(#a)"
            strokeWidth={1.5}
            d="M5 9.167c0-2.357 0-3.536.732-4.268.732-.732 1.911-.732 4.268-.732h2.5c2.357 0 3.536 0 4.267.732.733.732.733 1.911.733 4.268v4.167c0 2.356 0 3.535-.733 4.267-.731.733-1.91.733-4.267.733H10c-2.357 0-3.536 0-4.268-.733C5 16.87 5 15.691 5 13.334V9.167Z"
        />
        <Path
            stroke="url(#b)"
            strokeWidth={1.5}
            d="M5 15.834a2.5 2.5 0 0 1-2.5-2.5v-5c0-3.143 0-4.715.977-5.69.976-.976 2.547-.977 5.69-.977H12.5a2.5 2.5 0 0 1 2.5 2.5"
            opacity={0.5}
        />
        <Defs>
            <LinearGradient
                id="a"
                x1={5}
                x2={17.5}
                y1={11.251}
                y2={11.251}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
            <LinearGradient
                id="b"
                x1={2.5}
                x2={15}
                y1={8.751}
                y2={8.751}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
        </Defs>
    </Svg>
)
export default CopyGoldIcon
