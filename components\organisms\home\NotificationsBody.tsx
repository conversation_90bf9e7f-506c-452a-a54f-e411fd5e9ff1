// componente que muestra el cuerpo de la pantalla de notificaciones
import { StyleSheet, View } from 'react-native';
import React from 'react';
import { ThemedText } from '@/components/ui/ThemedText';
import NotificationsOptionItem from '@/components/molecules/home/<USER>';

type Props = {
  isEnabledTransactions: boolean;
  isEnabledReminders: boolean;
  onToggleTransactions: () => void;
  onToggleReminders: () => void;
};

const NotificationsBody = ({
  isEnabledReminders,
  isEnabledTransactions,
  onToggleReminders,
  onToggleTransactions,
}: Props) => {
  return (
    <View style={styles.container}>
      <NotificationsOptionItem
        title='Transacciones'
        description='Notificaciones sobre compras con tu tarjeta'
        isEnabled={isEnabledTransactions}
        onToggle={onToggleTransactions}
      />
      <View style={styles.separator} />
      <NotificationsOptionItem
        title='Recordatorios'
        description='Mensajes que te ayudarán a saber promociones y nuevos lanzamientos'
        isEnabled={isEnabledReminders}
        onToggle={onToggleReminders}
      />
      <View style={styles.separator} />
      <ThemedText type='subtitle' lightColor='#ACAFC2'>
        Continuarás recibiendo correos obligatorios, como notificaciones de
        seguimiento a aclaraciones, avisos de seguridad y confirmación de
        movimientos realizados.
      </ThemedText>
    </View>
  );
};

export default NotificationsBody;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: '#E6E8F4',
  },

  title: {
    fontSize: 16,
    fontWeight: 'bold',
  },

  separator: {
    height: 1,
    backgroundColor: '#ACAFC2',
    marginVertical: 24,
  },
});
