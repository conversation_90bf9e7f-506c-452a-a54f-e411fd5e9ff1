import * as React from 'react';
import Svg, {
  SvgProps,
  Path,
  Defs,
  LinearGradient,
  Stop,
} from 'react-native-svg';
const ArrowDown = (props: SvgProps) => (
  <Svg width={24} height={24} fill='none' {...props}>
    <Path
      fill='url(#a)'
      fillRule='evenodd'
      d='M18.707 8.627a1 1 0 0 0-1.414 0l-4.863 4.859a.614.614 0 0 1-.864 0L6.707 8.627a1 1 0 1 0-1.414 1.414l4.86 4.86.002.002a2.612 2.612 0 0 0 3.686 0l4.866-4.862a1 1 0 0 0 0-1.414Z'
      clipRule='evenodd'
    />
    <Defs>
      <LinearGradient
        id='a'
        x1={11.999}
        x2={11.999}
        y1={15.664}
        y2={8.334}
        gradientUnits='userSpaceOnUse'
      >
        <Stop offset={0.004} stopColor='#75532F' />
        <Stop offset={0.23} stopColor='#8B6539' />
        <Stop offset={0.395} stopColor='#9F7A49' />
        <Stop offset={0.506} stopColor='#AF8B55' />
        <Stop offset={0.741} stopColor='#DCB992' />
        <Stop offset={0.869} stopColor='#DAB890' />
        <Stop offset={0.916} stopColor='#D5B289' />
        <Stop offset={0.949} stopColor='#CCA87C' />
        <Stop offset={0.975} stopColor='#BF9B6B' />
        <Stop offset={0.998} stopColor='#AF8B56' />
        <Stop offset={0.999} stopColor='#AF8B55' />
        <Stop offset={1} stopColor='#E9D6B8' />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default ArrowDown;
