import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import BottomModal from '../BottomModal'
import PrimaryButton from '@/components/atoms/PrimaryButton'
import SecondaryButton from '@/components/atoms/SecondaryButton'
import AlertYellow from '@/components/icons/AlertYellow'

type Props = {
    showModal: boolean;
    setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
    handleConfirmDeleteCard: () => void;
};

const DeleteCardBottomModal: React.FC<Props> = ({
    showModal,
    setShowModal,
    handleConfirmDeleteCard,
}) => {
    return (
        <BottomModal isVisible={showModal} onClose={() => setShowModal(false)}>
            <View style={styles.body}>
                <View style={styles.rowContainer}>
                    <View style={styles.textContent}>
                        <AlertYellow />
                        <Text style={styles.title}>
                            ¿Estas seguro de que deseas eliminar la tarjeta?
                        </Text>
                    </View>
                    <Text style={styles.text}>
                        Esta tarjeta no aparecera más en tu aplicación, esta acción no es
                        reversible
                    </Text>
                </View>
                <PrimaryButton
                    title={'Aceptar'}
                    onPress={() => handleConfirmDeleteCard()}
                />
                <SecondaryButton
                    title='Cancelar'
                    onPress={() => setShowModal(false)}
                />
            </View>
        </BottomModal>
    )
}

export default DeleteCardBottomModal

const styles = StyleSheet.create({
    body: {
        width: '100%',
        paddingVertical: 24,
        paddingHorizontal: 32,
        gap: 24,
        marginBottom: 24,
    },
    title: {
        fontSize: 24,
        fontFamily: 'Inter',
        lineHeight: 29.05,
        fontWeight: '400',
        color: '#ffffff',
    },
    text: {
        fontSize: 14,
        fontFamily: 'Inter',
        lineHeight: 16.94,
        fontWeight: '400',
        color: '#ffffff',
        paddingLeft: 21,
    },
    rowContainer: {
        gap: 24,
    },
    textContent: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 11,
    },
})