import { StyleSheet, Image, Text, View, StatusBar } from 'react-native';
import React, { useEffect } from 'react';
import { router } from 'expo-router';
import UnlockPass from '@/components/icons/UnlockPass';

const SuccessRecoveryPassword = () => {
  useEffect(() => {
    // Establece un temporizador para redirigir a la página de inicio de sesión después de 2 segundos
    const timer = setTimeout(() => {
      router.dismissTo('/auth/login');
    }, 2000);

    // Limpia el temporizador si el componente se desmonta antes de que el temporizador se complete
    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={styles.wrapper}>
      <StatusBar backgroundColor='#000000' barStyle='light-content' translucent={false} />
      {/* Imagen de desbloqueo */}
      <UnlockPass />
      <View style={styles.content}>
        {/* Imagen de éxito */}
        <Image source={require('@/assets/images/checkSuccessPassword.png')} style={{ width: 60, height: 60 }} />
        {/* Texto de confirmación de restablecimiento de contraseña */}
        <Text style={styles.firstText}>Contraseña restablecida</Text>
        <Text style={styles.secondText}>
          Su contraseña se ha restablecido correctamente.
        </Text>
      </View>
    </View>
  );
};

export default SuccessRecoveryPassword;

const styles = StyleSheet.create({
  // Estilo para el contenedor principal
  wrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
    gap: 40,
    paddingHorizontal: 24,
  },
  // Estilo para el contenido
  content: {
    gap: 8,
    alignItems: 'center',
  },
  // Estilo para el primer texto
  firstText: {
    color: '#F4F5FB',
    fontWeight: '500',
    fontSize: 32,
    lineHeight: 38,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
  // Estilo para el segundo texto
  secondText: {
    color: '#F4F5FB',
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 19,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
});
