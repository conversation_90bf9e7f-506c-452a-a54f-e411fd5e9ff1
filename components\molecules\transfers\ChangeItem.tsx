import { StyleSheet, TouchableOpacity, View } from 'react-native';
import React from 'react';
import GoldGradientText from '@/components/atoms/GoldGradientText';
import { ViewStyle } from 'react-native/Libraries/StyleSheet/StyleSheetTypes';

type Props = {
  children: React.ReactNode;
  containerStyle?: ViewStyle;
  onBack: () => void;
};
const ChangeItem = ({ children, containerStyle, onBack }: Props) => {
  return (
    <View style={[styles.content, containerStyle]}>
      <View style={styles.leftContent}>{children}</View>
      <View style={styles.rightContent}>
        <TouchableOpacity onPress={onBack}>
          <GoldGradientText text='Cambiar' />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ChangeItem;

const styles = StyleSheet.create({
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  leftContent: {
    gap: 6,
  },
  rightContent: {},
});
