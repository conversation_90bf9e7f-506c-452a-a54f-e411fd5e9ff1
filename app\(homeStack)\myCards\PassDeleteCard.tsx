import Input from '@/components/atoms/Input';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import PageBaseWithBackButton from '@/components/templates/PageBaseWithBackButton';
import { ThemedText } from '@/components/ui/ThemedText';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import { Keyboard, Pressable, StatusBar, StyleSheet, View } from 'react-native';
import validatePasswordLength from '@/shared/validatePasswordLength';
import validatePasswordComplexity from '@/shared/validatePasswordComplexity';
import { useInactivity } from '@/context/InactivityContext';
import { validatePasswordToTransfer } from '@/services/transfersService';
import { useUserContext } from '@/context/UserContext';
import deleteCard from '@/services/cards/deleteCard';
import LoadingScreen from '@/components/molecules/LoadingScreen';

const PassDeleteCard = () => {
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [messageError, setMessageError] = useState('');
  const [isLoading, setIsLoading] = useState(false); // Estado de carga para el botón de envío
  const router = useRouter(); // Inicialización de router
  const { resetInactivityTimer } = useInactivity();
  const { user } = useUserContext();
  const { cardId } = useLocalSearchParams<{
    cardId: string;
  }>();

  // Validación de la contraseña
  const isPasswordValid =
    validatePasswordLength(password) && validatePasswordComplexity(password);

  // Función para manejar el clic en el botón
  const handleConfirmDeleteCard = async () => {
    setIsLoading(true); // Establecer el estado de carga en verdadero
    const isValid = await validatePasswordToTransfer(user?.email!, password);

    if (!isValid) {
      setMessageError('Contraseña incorrecta, intenta de nuevo')
      setPasswordError(true); // Mostrar error si la contraseña no es válida
      setIsLoading(false); // Establecer el estado de carga en falso
      return;
    }
    deleteCard(cardId).then((response) => {
      setPasswordError(false);
      setIsLoading(false); // Establecer el estado de carga en falso
      router.replace('/(homeStack)/myCards/SuccessDeleteCard');
    }).catch(() => {
      setIsLoading(false); // Establecer el estado de carga en falso
      setMessageError('Error al eliminar la tarjeta')
    });
  };

  return (
    <>
      <PageBaseWithBackButton onGoBack={() => router.back()}>
        <StatusBar backgroundColor='#ffffff' barStyle='dark-content' translucent={false} />
        <Pressable onPress={() => Keyboard.dismiss()} style={styles.wrapper}>
          <View style={styles.content}>
            <View style={styles.contentHeader}>
              <ThemedText type='title' lightColor='#232429'>
                Ingresa tu contraseña para eliminar la tarjeta seleccionada
              </ThemedText>
              <Input
                label='Contraseña'
                placeholder='Ingresa Contraseña'
                secureTextEntry={!showPassword}
                isError={passwordError}
                icon={
                  passwordError
                    ? 'alert-circle-outline'
                    : showPassword
                      ? 'eye-outline'
                      : 'eye-off-outline'
                }
                returnKeyType='done'
                onPressIcon={() => setShowPassword(!showPassword)}
                onChangeText={text => {
                  setPassword(text);
                  setPasswordError(false); // Limpiar el error mientras escribe
                  resetInactivityTimer(); // Reiniciar el temporizador de inactividad
                }}
                errorText={messageError}
              />
            </View>
          </View>
          <View style={styles.buttons}>
            <PrimaryButton
              title='Continuar'
              disable={!isPasswordValid} // Deshabilitar el botón si la contraseña no es válida
              onPress={handleConfirmDeleteCard} // Llamar a la función handleSave
            />
            <SecondaryButton
              title='Cancelar'
              onPress={() => router.back()} // Regresar a la pantalla anterior
              inverted
            />
          </View>
        </Pressable>
      </PageBaseWithBackButton>
      {isLoading && <LoadingScreen />}
    </>
  );
};

export default PassDeleteCard;

// Estilos para el componente
const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    justifyContent: 'space-between',
    paddingBottom: 10,
    paddingHorizontal: 24,
  },
  contentHeader: {
    gap: 8,
  },
  rememberContainer: {
    gap: 8,
  },
  rememberItme: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  rememberText: {
    color: '#606572',
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
  content: {
    gap: 24,
  },
  buttons: {
    flexDirection: 'column', // Cambiar a disposición vertical
    justifyContent: 'center', // Centrar los botones verticalmente
    alignItems: 'center', // Centrar horizontalmente (opcional)
    gap: 16, // Espaciado entre botones (alternativa a marginBottom)
  },
});
