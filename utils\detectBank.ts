export const BANK_TYPES = {
  CONVENIA: 'CONVENI<PERSON>',
  TRANSFER: 'Transfer',
  UNKNOWN: 'UNKNOWN',
} as const;

const BANK_NAMES = {
  CONVENIA: 'Convenia',
  TRANSFER: 'Transfer / Convenia',
  UNKNOWN: 'Banco no identificado',
} as const;

const DEFAULT_DETECT: BankDetectionResult = {
  type: BANK_TYPES.UNKNOWN,
  name: BANK_NAMES.UNKNOWN,
  legalCode: null,
};

interface BankDetectionResult {
  type: (typeof BANK_TYPES)[keyof typeof BANK_TYPES];
  name: string;
  legalCode: string | null;
}

export function detectBankName(
  accountNumber: string,
  externalBanks?: { legalCode: string; name: string }[],
): BankDetectionResult {
  if (accountNumber.startsWith('527') || accountNumber.startsWith('221')) {
    return {
      type: BANK_TYPES.CONVENIA,
      name: BANK_NAMES.CONVENIA,
      legalCode: accountNumber.slice(0, 3),
    };
  }

  if (accountNumber.startsWith('*********')) {
    return {
      type: BANK_TYPES.TRANSFER,
      name: BANK_NAMES.TRANSFER,
      legalCode: '684',
    };
  }

  if (!externalBanks || !externalBanks.length || accountNumber.length < 3) {
    return DEFAULT_DETECT;
  }

  const prefix = accountNumber.slice(0, 3);
  const externalBank = externalBanks.find(b => b.legalCode === prefix);

  if (externalBank) {
    return {
      type: BANK_TYPES.UNKNOWN,
      name: externalBank.name,
      legalCode: externalBank.legalCode,
    };
  }

  return DEFAULT_DETECT;
}
