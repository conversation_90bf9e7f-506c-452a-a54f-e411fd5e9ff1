import api from '../api/api';

// Servicio para obtener las preferencias biométricas
export const getBiometricPreferences = async (userId: string) => {
  try {
    const response = await api.get(`/biometric-preferences/${userId}`);
    return response.data; // { fingerprint_enabled: boolean, face_id_enabled: boolean }
  } catch (error: any) {
    console.error(
      'Error fetching biometric preferences:',
      error?.response?.data || error,
    );
    throw new Error('Failed to fetch biometric preferences');
  }
};

// Servicio para actualizar las preferencias biométricas
export const updateBiometricPreferences = async (
  userId: string,
  preferences: { fingerprint_enabled?: boolean; face_id_enabled?: boolean },
) => {
  try {
    const response = await api.put(
      `/biometric-preferences/${userId}`,
      preferences,
    );
    return response.data; // { fingerprint_enabled: boolean, face_id_enabled: boolean }
  } catch (error: any) {
    console.error(
      'Error updating biometric preferences:',
      error?.response?.data || error,
    );
    throw new Error('Failed to update biometric preferences');
  }
};

// Servicio para autenticación biométrica
export const activateAuthenticateWithBiometrics = async (
  userId: string,
  enableBiometric: boolean,
): Promise<string> => {
  try {
    const response = await api.post('/auth/biometric', {
      userId,
      enableBiometric,
    });
    return response.data.biometricToken; // { biometricToken: "generated-token-or-null" }
  } catch (error: any) {
    console.error(
      'Error during biometric authentication:',
      error?.response?.data || error,
    );
    throw new Error('Failed to authenticate with biometrics');
  }
};

// Servicio para iniciar sesión con token biométrico
export const loginWithBiometricToken = async (
  biometricToken: string,
): Promise<{
  token: string;
  user: {
    id: string;
    name: string;
    email: string;
    biometricToken: string;
    enabled: boolean;
  };
}> => {
  try {
    const response = await api.post('/auth/biometric-login', {
      biometricToken,
    });
    return response.data; // { data: { token: "jwt_token", user: { id, name, email, ... } } }
  } catch (error: any) {
    console.error(
      'Error during biometric login:',
      error?.response?.data || error,
    );
    throw new Error('Failed to login with biometric token');
  }
};
