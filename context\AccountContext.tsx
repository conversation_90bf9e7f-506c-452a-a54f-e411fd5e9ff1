// AccountProvider.tsx
import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
  useCallback,
  Dispatch,
  SetStateAction,
} from 'react';
import { useUserContext } from './UserContext';
import { getAccountDetails } from '@/services/accountService';

interface AccountData {
  availableResource: number;
  idAccount: string;
  clabe: string;
}

interface TransferCalculate {
  amount: number;
  commission: number;
  total: number;
  isValid: boolean
}

interface AccountContextType {
  accountData: AccountData | null;
  fetchAccountData: () => Promise<void>;
  isLoading: boolean;
  transferCalculate: TransferCalculate | null;
  setTransferCalculate: Dispatch<SetStateAction<TransferCalculate | null>>;
}

const AccountContext = createContext<AccountContextType | undefined>(undefined);

export const AccountProvider = ({ children }: { children: ReactNode }) => {
  const { user, isLoading: isLoadingUser } = useUserContext();
  const [accountData, setAccountData] = useState<AccountData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [transferCalculate, setTransferCalculate] = useState<TransferCalculate | null>(null);

  const fetchAccountData = useCallback(async () => {
    if (!user || !user.enabled) return;
    if (!user?.enabled) return;

    setIsLoading(true);

    try {
      const accountDetails = await getAccountDetails(user.email);
      setAccountData({
        idAccount: accountDetails.account_id,
        clabe: accountDetails.clabe,
        availableResource: accountDetails.available_resource,
      });
      setIsLoading(false);
    } catch (error) {
      console.error('❌ Error fetching account data:', error);
    }
  }, [user]);

  useEffect(() => {
    if (!isLoadingUser && user?.enabled) {
      fetchAccountData();
    }
  }, [isLoadingUser, user?.email, fetchAccountData, user?.enabled]);

  return (
    <AccountContext.Provider value={{ accountData, isLoading, fetchAccountData, transferCalculate, setTransferCalculate }}>
      {children}
    </AccountContext.Provider>
  );
};

export const useAccountContext = () => {
  const context = useContext(AccountContext);
  if (!context) {
    throw new Error('useAccountContext must be used within an AccountProvider');
  }
  return context;
};
