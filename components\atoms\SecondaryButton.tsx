import {
  DimensionValue,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import React from 'react';

type Props = {
  title: string;
  width?: DimensionValue;
  height?: DimensionValue;
  inverted?: boolean;
  disabled?: boolean;
  onPress: () => void;
};

const SecondaryButton = ({
  title,
  width,
  height,
  inverted,
  disabled,
  onPress,
}: Props) => {
  return (
    <TouchableOpacity
      disabled={disabled}
      style={[
        styles.wrapper,
        {
          width: width || '100%',
          height: height || 48,
          borderColor: inverted ? '#000000' : '#FFFFFF',
        },
      ]}
      onPress={disabled ? undefined : onPress}
    >
      <Text
        style={[
          styles.text,
          {
            color: inverted ? '#000000' : '#FFFFFF',
          },
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default SecondaryButton;

const styles = StyleSheet.create({
  wrapper: {
    // padding: 15,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
    borderWidth: 1,
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Inter',
  },
});
