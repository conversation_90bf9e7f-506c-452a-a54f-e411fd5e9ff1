import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import LoadingScreen from '../../molecules/LoadingScreen';
import PageBaseWithBackButton from '../../templates/PageBaseWithBackButton';
import PrimaryButton from '../../atoms/PrimaryButton';
import { ThemedText } from '../../ui/ThemedText';
import validatePasswordLength from '@/shared/validatePasswordLength';
import validatePasswordComplexity from '@/shared/validatePasswordComplexity';
import InputPassword from '@/components/atoms/InputPassword';
import { setFieldError } from '@/shared/setFieldError';
import GoldCheckMini from '@/components/icons/GoldCheckMini';

type Props = {
  title: string;
  subTitle: string;
  loading?: boolean;
  error: string | null;
  textButton: string;
  onPress: (password: string, confirmPassword: string) => void;
  setError: (error: string | null) => void;
};

type ErrorProps = {
  error: boolean;
  message?: string;
};

type ErrorsProps = {
  password: ErrorProps;
  confirmPassword: ErrorProps;
};

const initialErrorsState = {
  password: {
    error: false,
    message: undefined,
  },
  confirmPassword: {
    error: false,
    message: undefined,
  },
};

const CreatePassword = ({
  title,
  subTitle,
  loading,
  error,
  textButton,
  onPress,
  setError,
}: Props) => {
  const [errors, setErrors] = useState<ErrorsProps>(initialErrorsState);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const updateError = (
    error: boolean,
    field: keyof ErrorsProps,
    message: string | undefined,
  ) => {
    setFieldError<ErrorsProps>(error, field, message, setErrors);
  };

  const isValidNewPassword =
    validatePasswordComplexity(password) && validatePasswordLength(password);

  const isValidConfirmNewPassword =
    validatePasswordComplexity(confirmPassword) &&
    validatePasswordLength(confirmPassword);

  const isMatchingPasswords = password === confirmPassword;

  useEffect(() => {
    if (!isMatchingPasswords && confirmPassword !== '') {
      // setConfirmPasswordError('Las contraseñas no coinciden');
      updateError(true, 'password', undefined);
      updateError(true, 'confirmPassword', 'Las contraseñas no coinciden');

      return;
    }

    if (!isValidNewPassword && password !== '' && confirmPassword !== '') {
      updateError(true, 'password', undefined);
    } else {
      updateError(false, 'password', undefined);
    }

    if (!isValidConfirmNewPassword && confirmPassword !== '') {
      updateError(true, 'confirmPassword', 'Tu contraseña no es valida');
    } else {
      updateError(false, 'confirmPassword', undefined);
    }

    if (error) {
      updateError(true, 'confirmPassword', error);
    } else {
      updateError(false, 'confirmPassword', undefined);
    }
  }, [
    confirmPassword,
    isMatchingPasswords,
    isValidConfirmNewPassword,
    isValidNewPassword,
    password,
    error,
  ]);

  const handleSave = () => {
    if (isMatchingPasswords) {
      onPress(password, confirmPassword);
    }
  };

  const enableButton =
    isValidNewPassword && isValidConfirmNewPassword && isMatchingPasswords;
  // !error;

  return (
    <>
      <PageBaseWithBackButton>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={[
            {
              flex: 1,
            },
          ]}
        >
          <ScrollView
            showsVerticalScrollIndicator={false}
            bounces={false}
            keyboardShouldPersistTaps='handled'
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'space-between',
              paddingHorizontal: 24,
            }}
          >
            <View style={styles.wrapper}>
              <View style={styles.content}>
                <View style={styles.contentHeader}>
                  <ThemedText type='title' lightColor='#232429'>
                    {title}
                  </ThemedText>
                  <ThemedText type='subtitle' lightColor='#4A4B55'>
                    {subTitle}
                  </ThemedText>
                  <View style={styles.rememberContainer}>
                    <View style={styles.rememberItme}>
                      <GoldCheckMini />
                      <Text style={styles.rememberText}>
                        Debe tener al menos 8 caracteres
                      </Text>
                    </View>
                    <View style={styles.rememberItme}>
                      <GoldCheckMini />
                      <Text style={styles.rememberText}>
                        Debe tener números y letras
                      </Text>
                    </View>
                    <View style={styles.rememberItme}>
                      <GoldCheckMini />
                      <Text style={styles.rememberText}>
                        Debe tener al menos una letra mayúscula
                      </Text>
                    </View>
                  </View>
                </View>
                <View style={{ gap: 16 }}>
                  <InputPassword
                    label='Nueva contraseña'
                    placeholder='Ingresa tu nueva contraseña'
                    value={password}
                    isError={errors.password.error}
                    errorText={errors.password.message}
                    returnKeyType='done'
                    onChangeText={text => {
                      setError(null);
                      setPassword(text);
                    }}
                  />
                  <InputPassword
                    label='Confirmar contraseña'
                    placeholder='Confirma tu nueva contraseña'
                    value={confirmPassword}
                    isError={errors.confirmPassword.error}
                    errorText={error ? error : errors.confirmPassword.message}
                    returnKeyType='done'
                    onChangeText={text => {
                      setError(null);
                      setConfirmPassword(text);
                    }}
                  />
                </View>
              </View>
              <PrimaryButton
                title={textButton}
                disable={!enableButton}
                onPress={handleSave}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </PageBaseWithBackButton>
      {loading && <LoadingScreen />}
    </>
  );
};

export default CreatePassword;

const styles = StyleSheet.create({
  wrapper: {
    // flex: 1,
    height: '100%',
    justifyContent: 'space-between',
    gap: 24,
    paddingBottom: 10,
  },
  contentHeader: {
    gap: 8,
  },
  rememberContainer: {
    gap: 8,
  },
  rememberItme: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  rememberText: {
    color: '#606572',
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
  content: {
    gap: 24,
    marginBottom: 100,
  },
});
