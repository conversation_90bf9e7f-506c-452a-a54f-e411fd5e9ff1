import { ScrollView, StyleSheet, View } from 'react-native';
import React from 'react';
import { ThemedText } from '@/components/ui/ThemedText';
import UserInfo from '@/components/molecules/home/<USER>';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import MenuOptionsList from '@/components/molecules/home/<USER>';

type Props = {
  onCloseSession: () => void;
  onToggleBiometricts: () => void;
};

const MenuBody = ({ onCloseSession, onToggleBiometricts }: Props) => {
  return (
    <View style={styles.wrapper}>
      <View style={styles.wrapper}>
        <ScrollView
          contentContainerStyle={styles.scrollViewContent}
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <View
            style={{
              gap: 24,
              marginBottom: 40,
            }}
          >
            <ThemedText type='text24' lightColor='#232429' style={styles.title}>
              Perfil
            </ThemedText>
            <UserInfo />
            <MenuOptionsList onToggleBiometricts={onToggleBiometricts} />
          </View>
          <View>
            <PrimaryButton title='Cerrar sesión' onPress={onCloseSession} />
          </View>
        </ScrollView>
      </View>

    </View>
  );
};

export default MenuBody;

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: '#E6E8F4',
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 24,
  },
  title: {
    textAlign: 'center',
  },
});
