import { StyleSheet, Text, TouchableOpacity, DimensionValue, View } from 'react-native';
import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import AntDesign from '@expo/vector-icons/AntDesign';

type Props = {
  title: string;
  width?: DimensionValue;
  height?: DimensionValue;
  disable?: boolean;
  onPress: () => void;
};

const LoginBiometricsButton = ({ title, width, height, disable, onPress }: Props) => {
  const gradientColor: readonly [string, string, ...string[]] = disable
    ? ['#ACAFC2', '#ACAFC2']
    : [
      '#75532F',
      '#8B6539',
      '#9F7A49',
      '#AF8B55',
      '#DCB992',
      '#DAB890',
      '#D5B289',
      '#CCA87C',
      '#BF9B6B',
      '#AF8B56',
      '#AF8B55',
      '#E9D6B8',
    ];

  const gradietColorLocations: readonly [number, number, ...number[]] = disable
    ? [0.0, 0.0]
    : [
      0.0041, 0.2303, 0.3945, 0.5056, 0.7408, 0.8693, 0.9156, 0.9486, 0.9751,
      0.9977, 0.9985, 1.0,
    ];

  return (
    <TouchableOpacity
      style={[
        styles.wrapper,
        {
          width: width || '100%',
          height: height || 48,
        },
      ]}
      disabled={disable}
      onPress={disable ? undefined : onPress}
    >
      <LinearGradient
        colors={gradientColor}
        locations={gradietColorLocations}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[
          styles.gradientContent,
          {
            width: width || '100%',
            height: height || 48,
          },
        ]}
      >
        <View
          style={{
            flexDirection: 'row',
            gap: 8,
          }}
        >
          <AntDesign name="scan1" size={24} color="white" />
          <Text style={styles.loginButtonText}>{title}</Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default LoginBiometricsButton;

const styles = StyleSheet.create({
  wrapper: {
    alignItems: 'center',
    borderRadius: 6,
  },
  gradientContent: {
    width: '100%',
    paddingHorizontal: 15,
    alignItems: 'center',
    borderRadius: 6,
    justifyContent: 'center',
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Inter',
  },
});
