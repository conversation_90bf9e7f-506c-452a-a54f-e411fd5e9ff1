import {
  StyleSheet,
  Text,
  View,
  Image,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import React, { useRef } from 'react';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import ViewShot from 'react-native-view-shot';
import RedErrorCard from '@/components/icons/RedErrorCard';

type Props = {
  onFinished: () => void;
};

const ErrorTransferLayer = ({
  onFinished,
}: Props) => {
  const viewShotRef = useRef<ViewShot>(null);

  return (
    <SafeAreaView style={styles.safe}>
      <StatusBar backgroundColor='#000000' barStyle='light-content' translucent={false} />
      <View style={styles.wrapper}>
        <ViewShot ref={viewShotRef} style={styles.captureArea}>
          <View style={styles.content}>
            <Image
              style={styles.errorCardsImage}
              source={require('@/assets/images/errorCard.png')}
            />
            <View style={{
              alignItems: 'center',
            }}>
              <RedErrorCard
                style={styles.errorImage}
              />
              <Text style={styles.firstText}>Error en la transferencia</Text>
              <Text style={styles.secondText}>
                Revisa los datos ingresados y vuelve a intentarlo
              </Text>
            </View>
          </View>
        </ViewShot>
        <SecondaryButton title="Ir al inicio" onPress={onFinished} />
      </View>
    </SafeAreaView>
  );
};

export default ErrorTransferLayer;

const styles = StyleSheet.create({
  safe: {
    flex: 1,
    backgroundColor: '#000000',
  },
  wrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
    paddingHorizontal: 32,
    gap: 40,
  },
  content: {
    gap: 36,
    alignItems: 'center',
  },
  firstText: {
    color: '#F4F5FB',
    fontWeight: '500',
    fontSize: 32,
    lineHeight: 38,
    textAlign: 'center',
    fontFamily: 'Inter',
    width: 326,
    marginTop: 12,
    marginBottom: 8,
  },
  secondText: {
    color: '#ffffff',
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 19.36,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
  errorImage: {
    marginTop: 50,
    backgroundColor: '#000000',
  },
  errorCardsImage: {
    width: 150,
    height: 120,
  },
  captureArea: {
    width: '100%',
  },
});
