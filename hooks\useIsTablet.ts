import { useState, useEffect } from 'react';
import * as Device from 'expo-device';

// Custom hook to determine if the device is a tablet
const useIsTablet = () => {
  // State to store whether the device is a tablet or not
  const [isTablet, setIsTablet] = useState(false);

  useEffect(() => {
    // Function to check the device type
    const checkDeviceType = async () => {
      // Get the device type asynchronously
      const deviceType = await Device.getDeviceTypeAsync();
      // Update the state if the device is a tablet
      setIsTablet(deviceType === Device.DeviceType.TABLET);
    };

    // Call the function to check the device type
    checkDeviceType();
  }, []); // Empty dependency array ensures this effect runs only once

  // Return the state indicating if the device is a tablet
  return isTablet;
};

export default useIsTablet;
