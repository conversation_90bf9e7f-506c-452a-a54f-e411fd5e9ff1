import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import React, { useState, useCallback } from 'react';
import GoldGradientText from '@/components/atoms/GoldGradientText';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import Plus from '@/components/icons/Plus';

type Props = {
  haveCards: boolean;
  loading: boolean;
};

const SeeMyCardsHeader = ({ haveCards, loading }: Props) => {
  const { email } = useLocalSearchParams();
  const [buttonDisabled, setButtonDisabled] = useState(false);

  // Restablece el botón al volver a la pantalla
  useFocusEffect(
    useCallback(() => {
      setButtonDisabled(false);
      return () => { };
    }, [])
  );

  const handleAddCardPress = () => {
    setButtonDisabled(true);
    router.push({
      pathname: '/(homeStack)/myCards/addCard',
      params: { email },
    });
  };

  return (
    <View style={{ gap: 18, padding: 24 }}>
      <View style={{ gap: 24 }}>
        <Text style={styles.title}>Mis tarjetas</Text>
        {!haveCards && !loading && (
          <Text style={styles.title}>¡Aun no tienes tarjetas!</Text>
        )}
      </View>
      <View style={styles.addCardContainer}>
        <GoldGradientText text='Agregar tarjeta' style={styles.addCardText} />
        <TouchableOpacity
          disabled={loading || buttonDisabled}
          onPress={handleAddCardPress}
        >
          <Plus />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default SeeMyCardsHeader;

// Estilos para el componente
const styles = StyleSheet.create({
  title: {
    fontFamily: 'Inter',
    fontWeight: '400',
    fontSize: 24,
    lineHeight: 29,
    color: '#F4F5FB',
  },
  subtitle: {
    fontFamily: 'Inter',
    fontWeight: '600',
    fontSize: 22,
    lineHeight: 26.63,
    color: '#F4F5FB',
    textRendering: 'geometricPrecision',
  },
  addCardText: {
    textAlign: 'left',
    width: '100%',
  },
  addCardContainer: {
    flexDirection: 'row',
    maxWidth: 144,
  },
});
