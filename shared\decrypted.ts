import { AES } from "@stablelib/aes";
import { GCM } from "@stablelib/gcm";
import { decode as base64Decode } from "@stablelib/base64";
import Constants from 'expo-constants';

// Get the key from Expo Constants or use a fallback approach
const key = Constants.expoConfig?.extra?.DECRYPTION_KEY;

const Decrypt = (data: string): string => {
    if (data === '' || data.length === 0) {
        throw new Error("Datos vacíos proporcionados")
    }
    if (!key) {
        throw new Error("No se ha encontrado la clave de encriptación");
    }
    // Decode the key directly - no need for double encoding
    const keyBytes = base64Decode(key);

    // Decode the encrypted data
    const encryptedBytes = base64Decode(data);

    // Extract nonce, ciphertext and tag
    const NONCE_LENGTH = 12;
    const TAG_LENGTH = 16;

    // Check if the encrypted data is long enough
    if (encryptedBytes.length < NONCE_LENGTH + TAG_LENGTH) {
        throw new Error("Datos encriptados demasiado cortos");
    }

    const nonce = encryptedBytes.subarray(0, NONCE_LENGTH);
    const tag = encryptedBytes.subarray(encryptedBytes.length - TAG_LENGTH);
    const ciphertext = encryptedBytes.subarray(NONCE_LENGTH, encryptedBytes.length - TAG_LENGTH);

    // Initialize cipher and GCM
    const cipher = new AES(keyBytes);
    const gcm = new GCM(cipher);

    // Decrypt the data
    // We need to reconstruct the sealed data in the format expected by open()
    const sealed = new Uint8Array(ciphertext.length + TAG_LENGTH);
    sealed.set(ciphertext, 0);
    sealed.set(tag, ciphertext.length);

    const decryptedBytes = gcm.open(nonce, sealed);

    if (decryptedBytes === null) {
        throw new Error("Desencriptación fallida: datos inválidos o clave incorrecta");
    }

    // Convert to plain text
    const decryptedText = new TextDecoder().decode(decryptedBytes);

    return decryptedText;
}

const DecryptAsync = async (data: string): Promise<string> => {
    return new Promise((resolve, reject) => {
        try {
            const result = Decrypt(data);
            resolve(result);
        } catch (error) {
            reject(error);
        }
    });
}

export { Decrypt, DecryptAsync };