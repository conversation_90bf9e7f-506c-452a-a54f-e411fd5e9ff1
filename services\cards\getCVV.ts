import api from "../api/api";

const getCVV = async (cardId: string, cardType: string, expirationDate: string, timeZone: string) => {
    try {
        const response = await api.post('/dock-cards/query-cvv', {
            card_dock_id: cardId,
            card_type: cardType,
            expiration_date: expirationDate,
            time_zone: timeZone,
        });
        const responseStatus = response.data.statusCode;
        if (responseStatus!== 200) {
            throw new Error(response.data.message);
        }
        return response.data.data;
    }
    catch (error: unknown) {
        if (error instanceof Error) {
            throw error; // Si ya es un Error, lo lanzamos directamente
        } else {
            // Si es otro tipo (objeto, string, etc.), lo convertimos a string apropiadamente
            throw new Error(String(error));
        }
    }
}

export default getCVV;