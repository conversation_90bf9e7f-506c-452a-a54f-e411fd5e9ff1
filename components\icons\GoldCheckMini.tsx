import * as React from "react"
import Svg, {
    SvgProps,
    Path,
    Defs,
    LinearGradient,
    Stop,
} from "react-native-svg"
const GoldCheckMini = (props: SvgProps) => (
    <Svg
        width={16}
        height={16}
        fill="none"
        {...props}
    >
        <Path
            fill="url(#a)"
            fillRule="evenodd"
            d="M12.997 2.089a.667.667 0 0 1 .901.206l.66 1.006a.667.667 0 0 1-.103.853l-.002.003-.01.008-.038.036-.15.143a55.96 55.96 0 0 0-2.413 2.49c-1.465 1.611-3.204 3.72-4.375 5.765a1.007 1.007 0 0 1-1.598.2L1.546 8.307a.667.667 0 0 1 .034-.957L2.887 6.17a.667.667 0 0 1 .846-.038L5.94 7.787c3.446-3.398 5.4-4.702 7.058-5.698Z"
            clipRule="evenodd"
        />
        <Defs>
            <LinearGradient
                id="a"
                x1={1.36}
                x2={14.667}
                y1={7.55}
                y2={7.55}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.019} stopColor="#DCB992" />
                <Stop offset={0.249} stopColor="#DAB890" />
                <Stop offset={0.459} stopColor="#D5B289" />
                <Stop offset={0.684} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
        </Defs>
    </Svg>
)
export default GoldCheckMini
