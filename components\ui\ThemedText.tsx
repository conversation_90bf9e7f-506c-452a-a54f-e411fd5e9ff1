import { Text, type TextProps, StyleSheet } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?:
  | 'default'
  | 'title'
  | 'defaultSemiBold'
  | 'subtitle'
  | 'link'
  | 'caption'
  | 'text22'
  | 'text24';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  // Get the color from the theme,
  // POR EL MOMENTO SOLO SE ESTA USANDO EL COLOR LIGHT PARA AMBOS TEMAS
  const color = useThemeColor({ light: lightColor, dark: lightColor }, 'text');

  return (
    <Text
      style={[
        { color },
        type === 'default' ? styles.default : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'link' ? styles.link : undefined,
        type === 'caption' ? styles.caption : undefined,
        type === 'text22' ? styles.text22 : undefined,
        type === 'text24' ? styles.text24 : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: 16,
    lineHeight: 24,
    fontFamily: 'Inter',
  },
  defaultSemiBold: {
    fontSize: 16,
    lineHeight: 19.36,
    fontWeight: '600',
    fontFamily: 'InterSemiBold',
  },
  title: {
    fontSize: 32,
    fontWeight: '500',
    lineHeight: 38.73,
    fontFamily: 'Inter',
  },
  subtitle: {
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 19.36,
    fontFamily: 'Inter',
  },
  link: {
    lineHeight: 30,
    fontSize: 16,
    color: '#0a7ea4',
    fontFamily: 'Inter',
  },
  caption: {
    fontSize: 14,
    lineHeight: 16.94,
    fontFamily: 'InterSemiBold',
    fontWeight: '600',
    textRendering: 'geometricPrecision',
  },
  text22: {
    fontSize: 22,
    fontFamily: 'InterSemiBold',
    lineHeight: 26.63,
    textRendering: 'geometricPrecision',
    fontWeight: '600',
  },
  text24: {
    fontFamily: 'Inter',
    fontWeight: '400',
    fontSize: 24,
    lineHeight: 29,
  },
});
