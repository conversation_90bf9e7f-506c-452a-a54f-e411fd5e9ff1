import * as React from "react"
import Svg, {
  SvgProps,
  Path,
  Defs,
  LinearGradient,
  Stop,
} from "react-native-svg"
const HandsTwice = (props: SvgProps) => (
  <Svg
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <Path
      fill="url(#a)"
      d="M21.71 8.71c1.25-1.25.68-2.71 0-3.42l-3-3c-1.26-1.25-2.71-.68-3.42 0L13.59 4H11C9.1 4 8 5 7.44 6.15L3 10.59v4l-.71.7c-1.25 1.26-.68 2.71 0 3.42l3 3c.54.54 1.12.74 1.67.74.71 0 1.36-.35 1.75-.74l2.7-2.71H15c1.7 0 2.56-1.06 2.87-2.1 1.13-.3 1.75-1.16 2-2C21.42 14.5 22 13.03 22 12V9h-.59l.3-.29ZM20 12c0 .45-.19 1-1 1h-1v1c0 .45-.19 1-1 1h-1v1c0 .45-.19 1-1 1h-4.41l-3.28 3.28c-.31.29-.49.12-.6.01l-2.99-2.98c-.29-.31-.12-.49-.01-.6L5 15.41v-4l2-2V11c0 1.21.8 3 3 3s3-1.79 3-3h7v1Zm.29-4.71L18.59 9H11v2c0 .45-.19 1-1 1s-1-.55-1-1V8c0-.46.17-2 2-2h3.41l2.28-2.28c.31-.29.49-.12.6-.01l2.99 2.98c.29.31.12.49.01.6Z"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={1.551}
        x2={22.449}
        y1={12.001}
        y2={12.001}
        gradientUnits="userSpaceOnUse"
      >
        <Stop offset={0.004} stopColor="#75532F" />
        <Stop offset={0.23} stopColor="#8B6539" />
        <Stop offset={0.395} stopColor="#9F7A49" />
        <Stop offset={0.506} stopColor="#AF8B55" />
        <Stop offset={0.741} stopColor="#DCB992" />
        <Stop offset={0.869} stopColor="#DAB890" />
        <Stop offset={0.916} stopColor="#D5B289" />
        <Stop offset={0.949} stopColor="#CCA87C" />
        <Stop offset={0.975} stopColor="#BF9B6B" />
        <Stop offset={0.998} stopColor="#AF8B56" />
        <Stop offset={0.999} stopColor="#AF8B55" />
        <Stop offset={1} stopColor="#E9D6B8" />
      </LinearGradient>
    </Defs>
  </Svg>
)
export default HandsTwice
