import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import { ThemedText } from '@/components/ui/ThemedText';
import { useTransferContext } from '@/context/TransferContext';

const formatNumberWithCommas = (number: number): string => {
  return number.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

type Props = {
  amount: string;
  onConfirm: () => void;
  onCancel: () => void;
};

const ReviewTransferBottom = ({ amount, onConfirm, onCancel }: Props) => {
  const { shouldShowCommission, transferData } = useTransferContext()
  const { commission } = transferData || {};
  const cleanAmount = amount.replace(/,/g, '');
  const totalAmount = Number(cleanAmount) + (shouldShowCommission ? Number(commission!) : 0)

  return (
    <View style={styles.wrapper}>
      <View style={styles.content}>
        <ThemedText type='text22' lightColor='#000000'>
          Total de la operación
        </ThemedText>
        <Text style={styles.amount}>${formatNumberWithCommas(totalAmount)}</Text>
        {!shouldShowCommission && (
          <Text style={styles.text}>
            Este envío no genera ningún tipo de comisión
          </Text>
        )}
      </View>
      <View style={styles.bottom}>
        <PrimaryButton onPress={onConfirm} title='Siguiente' />
        <SecondaryButton onPress={onCancel} title='Cancelar' inverted />
      </View>
    </View>
  );
};

export default ReviewTransferBottom;

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: '#E6E8F4',
    paddingTop: 24,
    paddingHorizontal: 24,
    flex: 1,
    justifyContent: 'space-between',
    gap: 24,
  },
  content: {
    flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
    gap: 24,
    paddingTop: 65,
  },
  bottom: {
    gap: 24,
  },
  amount: {
    fontSize: 28,
    fontFamily: 'Inter',
    lineHeight: 33.89,
    textRendering: 'geometricPrecision',
    fontWeight: '400',
  },
  text: {
    fontSize: 14,
    lineHeight: 16.94,
    fontWeight: '400',
    fontFamily: 'Inter',
  },
});
