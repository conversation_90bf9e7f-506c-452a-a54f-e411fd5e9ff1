import * as React from "react"
import Svg, { SvgProps, Path } from "react-native-svg"
const BtnCardSearch = (props: SvgProps) => (
    <Svg
        width={28}
        height={29}
        fill="none"
        {...props}
    >
        <Path
            stroke="#F4F5FB"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="m25.667 26.166-1.75-1.75m1.75-12.25H2.333M25.668 14.5v-4.434c0-1.306 0-1.96-.255-2.459a2.333 2.333 0 0 0-1.02-1.02c-.498-.254-1.152-.254-2.459-.254H6.067c-1.307 0-1.96 0-2.46.254-.439.224-.795.581-1.02 1.02-.254.5-.254 1.153-.254 2.46v8.866c0 1.307 0 1.96.255 2.46.224.438.58.795 1.02 1.019.499.254 1.152.254 2.459.254h6.183M25.084 21.5a4.083 4.083 0 1 1-8.167 0 4.083 4.083 0 0 1 8.166 0Z"
        />
    </Svg>
)
export default BtnCardSearch
