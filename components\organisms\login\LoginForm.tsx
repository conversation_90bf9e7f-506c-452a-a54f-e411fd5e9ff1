import { TouchableOpacity, StyleSheet, Text, View } from 'react-native';
import React, { useState } from 'react';
import Input from '@/components/atoms/Input';
import useIsTablet from '@/hooks/useIsTablet';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import { router } from 'expo-router';
import InputPassword from '@/components/atoms/InputPassword';

type Props = {
  disableBtn: boolean;
  onSetEmail: (email: string) => void;
  onSetPassword: (password: string) => void;
  onHandlePressLogin: () => void;
  errorEmail: boolean;
  errorPass: boolean;
  errorPasswordText: string;
};

const LoginForm = ({
  disableBtn,
  onSetEmail,
  onSetPassword,
  onHandlePressLogin,
  errorPasswordText,
  errorEmail,
  errorPass,
}: Props) => {
  const [isNavigating, setIsNavigating] = useState(false); // Add this new state
  const isTablet = useIsTablet();

  const handleNavigateToReset = () => {
    if (isNavigating) return; // Prevent multiple navigations
    setIsNavigating(true);
    router.push('/auth/resetPassword');
    setTimeout(() => setIsNavigating(false), 1000);

  };

  return (
    <View
      style={[
        styles.body,
        {
          flexGrow: isTablet ? 0 : 1,
        },
      ]}
    >
      <View style={styles.content}>
        <Text style={styles.title}>Iniciar sesión</Text>
        <Input
          label='Correo eléctronico'
          lightLabel
          keyboardType='email-address'
          placeholder='Ingresa correo eléctronico'
          isError={errorEmail}
          errorText='Correo electrónico incorrecto'
          numberOfLines={1}
          multiline={false}
          onChangeText={onSetEmail}
        />
        <InputPassword
          label='Ingresa tu contraseña'
          lightLabel
          placeholder='Contraseña'
          isError={errorPass}
          onChangeText={text => {
            onSetPassword(text);
          }}
          errorText={errorPasswordText}
        />
        <TouchableOpacity
          onPress={handleNavigateToReset}
          disabled={isNavigating}
          style={{
            alignSelf: 'center',
          }}
        >
          <Text
            style={styles.link}
          >
            ¿Olvidaste tu contraseña?
          </Text>
        </TouchableOpacity>
      </View>
      <PrimaryButton
        title='Iniciar sesión'
        disable={disableBtn}
        onPress={onHandlePressLogin}
      />
    </View>
  );
};

export default LoginForm;

const styles = StyleSheet.create({
  content: {
    gap: 24,
  },
  title: {
    fontSize: 36,
    color: 'white',
    fontWeight: '400',
    lineHeight: 43.57,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
  body: {
    maxWidth: 400,
    alignItems: 'center',
    gap: 100,
    justifyContent: 'space-between',
    width: '100%',
  },
  link: {
    color: 'white',
    textDecorationLine: 'underline',
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 19.36,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
});
