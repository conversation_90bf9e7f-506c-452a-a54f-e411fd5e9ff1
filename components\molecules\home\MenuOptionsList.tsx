import { StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import MenuOption from './MenuOption';
import { router } from 'expo-router';
import HandsTwice from '@/components/icons/HandsTwice';
import GoldFinger from '@/components/icons/GoldFinger';
import MCodeIcon from '@/components/icons/MCodeIcon';
import GoldF from '@/components/icons/GoldF';
import MContact from '@/components/icons/MContact';
import MTyc from '@/components/icons/MTyc';

type Props = {
  onToggleBiometricts: () => void;
};

const MenuOptionsList = ({ onToggleBiometricts }: Props) => {
  const [isNavigating, setIsNavigating] = useState(false);

  const handleNavigation = (route: Readonly<string>) => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.push(route as any); // Using type assertion since we know these routes are valid
    setTimeout(() => setIsNavigating(false), 100);
  };

  const handleBiometrics = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    onToggleBiometricts();
    setTimeout(() => setIsNavigating(false), 1000);
  };

  return (
    <View style={styles.wrapper}>
      <MenuOption
        label='Activar biométricos'
        icon={<GoldFinger />}
        onPress={handleBiometrics}
        disabled={isNavigating}
      />
      <MenuOption
        label='Cambiar contraseña'
        icon={<MCodeIcon />}
        onPress={() => handleNavigation('/(homeStack)/home/<USER>/ChangePassword')}
        disabled={isNavigating}
      />
      <MenuOption
        label='Preguntas frecuentes'
        icon={<GoldF />}
        onPress={() => handleNavigation('/(homeStack)/home/<USER>/Faq')}
        disabled={isNavigating}
      />
      <MenuOption
        label='Contacto'
        icon={<MContact />}
        onPress={() => handleNavigation('/(homeStack)/movements/Contact')}
        disabled={isNavigating}
      />
      <MenuOption
        label='Términos y condiciones'
        icon={<MTyc />}
        onPress={() => handleNavigation('/(homeStack)/home/<USER>/Tyc')}
        disabled={isNavigating}
      />
      <MenuOption
        label='Aclaraciones'
        icon={<HandsTwice />}
        onPress={() => handleNavigation('/(homeStack)/home/<USER>/Clarifications')}
        disabled={isNavigating}
      />
    </View>
  );
};

export default MenuOptionsList;

const styles = StyleSheet.create({
  wrapper: {
    borderColor: '#C3C6D8',
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 8,
    gap: 2,
  },
});
