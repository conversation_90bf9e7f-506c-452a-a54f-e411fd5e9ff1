import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';

export const copyTemplateToDocuments = async () => {
  try {
    // Crear directorio templates si no existe
    const templatesDir = FileSystem.documentDirectory + 'templates/';
    const templateInfo = await FileSystem.getInfoAsync(templatesDir);

    if (!templateInfo.exists) {
      await FileSystem.makeDirectoryAsync(templatesDir, {
        intermediates: true,
      });
    }

    // Copiar templates desde assets
    const mainAsset = Asset.fromModule(
      require('../assets/templates/statementTemplate.html'),
    );
    const continuationAsset = Asset.fromModule(
      require('../assets/templates/statementTemplateContinuation.html'),
    );

    await Promise.all([
      mainAsset.downloadAsync(),
      continuationAsset.downloadAsync(),
    ]);

    if (mainAsset.localUri) {
      const mainTemplatePath = templatesDir + 'statementTemplate.html';
      await FileSystem.copyAsync({
        from: mainAsset.localUri,
        to: mainTemplatePath,
      });
    }

    if (continuationAsset.localUri) {
      const continuationTemplatePath =
        templatesDir + 'statementTemplateContinuation.html';
      await FileSystem.copyAsync({
        from: continuationAsset.localUri,
        to: continuationTemplatePath,
      });
    }
  } catch (error) {
    console.error('Error copying template:', error);
  }
};
