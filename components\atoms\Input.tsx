import React from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TextInputProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Country, CountryCodeSelector } from './CountrySelecter';

// Definición de las propiedades personalizadas para el componente Input
interface CustomInputProps extends TextInputProps {
  label: string; // Etiqueta del campo de entrada
  errorText?: string; // Texto de error opcional
  isError: boolean; // Indica si hay un error
  icon?: keyof typeof Ionicons.glyphMap; // Icono opcional
  lightLabel?: boolean; // Indica si la etiqueta debe ser clara
  onRenderRight?: () => React.ReactNode; // Función opcional para renderizar contenido en el lado derecho
  onPressIcon?: () => void; // Función opcional para manejar el evento de presionar el icono
  onChangeText: (text: string) => void; // Función para manejar el cambio de texto
  disabled?: boolean; // Indica si el campo de entrada está deshabilitado
  setCountryCodes?: (country: Country) => void; // Función opcional para establecer el código del país
  countryCodes?: Country; // Código del país para el selector de país
  isPhone?: boolean; // Indica si el campo es para un número de teléfono
}

// Componente funcional Input
const Input: React.FC<CustomInputProps> = ({
  label,
  lightLabel,
  errorText,
  icon,
  isError,
  style,
  onRenderRight,
  onPressIcon,
  onChangeText,
  disabled,
  setCountryCodes,
  countryCodes, // Código del país para el selector de país
  isPhone = false, // Indica si el campo es para un número de teléfono
  ...props
}) => {
  return (
    <View style={styles.container}>
      {/* Etiqueta del campo de entrada */}
      <Text
        style={[
          styles.label,
          {
            color: lightLabel ? '#FFFFFF' : '#000000', // Color de la etiqueta basado en lightLabel
          },
        ]}
      >
        {label}
      </Text>
      {/* Contenedor del campo de entrada y el icono */}
      <View style={[
        styles.inputWrapper,
        isError && styles.inputWrapperError
      ]
      }
      >
        {isPhone && <CountryCodeSelector setCountryCodes={setCountryCodes} countryCodes={countryCodes} />}
        <TextInput
          style={[styles.input, isError && styles.inputError, style]} // Estilo del campo de entrada
          onChangeText={onChangeText} // Maneja el cambio de texto
          {...props} // Otras propiedades del TextInput
          placeholderTextColor='#9093A5'
          editable={!disabled} // Habilita o deshabilita el campo de entrada
        />
        {/* Icono opcional */}
        {((isError || icon) && !onRenderRight) && (
          <Ionicons
            name={icon || 'alert-circle-outline'} // Nombre del icono basado en icon
            size={16}
            color={isError ? '#F04438' : 'black'} // Color del icono basado en isError
            style={styles.icon}
            onPress={onPressIcon} // Maneja el evento de presionar el icono
          />
        )}
        {/* Contenido adicional en el lado derecho opcional */}
        {onRenderRight && onRenderRight()}
      </View>
      {/* Texto de error opcional */}
      {isError && errorText && (
        <Text style={styles.errorText}>{errorText}</Text>
      )}
    </View>
  );
};

// Estilos del componente
const styles = StyleSheet.create({
  container: {
    width: '100%',
    gap: 6,
  },
  label: {
    fontFamily: 'Inter',
    fontStyle: 'normal',
    fontWeight: '600',
    fontSize: 14,
    lineHeight: 17,
    color: '#000000',
    alignSelf: 'stretch',
    flexGrow: 0,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    boxSizing: 'border-box',
    gap: 8,
    width: '100%',
    height: 48,
    backgroundColor: '#FFFFFF',
    borderColor: '#C3C6D8',
    borderWidth: 1,
    shadowColor: 'rgba(16, 24, 40, 0.05)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 1,
    shadowRadius: 2,
    borderRadius: 10,
    // overflow: 'hidden',
  },
  inputWrapperError: {
    borderColor: 'red', // Color del borde en caso de error
  },
  input: {
    flex: 1,
    fontSize: 17,
    fontWeight: '400',
    lineHeight: 19,
    // height: 48,
    fontFamily: 'PublicSansRegular',
    paddingVertical: 5,
    paddingHorizontal: 12,
    borderRadius: 10,
    color: '#000000',
  },
  inputError: {
    borderColor: '#F04438', // Color del borde del campo de entrada en caso de error
  },
  icon: {
    marginRight: 12,
  },
  errorText: {
    color: '#D92D20', // Color del texto de error
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 16.94,
    fontFamily: 'Roboto',
  },
});

export default Input;
