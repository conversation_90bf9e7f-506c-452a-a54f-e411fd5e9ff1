import axios from 'axios';
import BASE_URL from './config';

// Instancia de Axios con configuración base
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const signin = async (email: string, password: string) => {
  try {
    const response = await api.post(`/auth/signin`, {
      email,
      password,
      origin: 'mobile',
    });
    return response.data;
  } catch (error: any) {
    return error.response.data;
  }
};
