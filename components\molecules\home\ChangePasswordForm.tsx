import { View } from 'react-native';
import React from 'react';
import InputPassword from '@/components/atoms/InputPassword';
import { ErrorsProps } from '@/app/(homeStack)/home/<USER>/ChangePassword';

type DataProps = {
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
};

type Props = {
  data: DataProps;
  errors: ErrorsProps;
  handleChangeInput: (key: string, value: string) => void;
};

const ChangePasswordForm = ({ data, errors, handleChangeInput }: Props) => {
  return (
    <View style={{ gap: 8 }}>
      <InputPassword
        label='Escribe la contraseña actual'
        placeholder='Contraseña actual'
        value={data.currentPassword}
        isError={errors.currentPassword.error}
        errorText={errors.currentPassword.message}
        onChangeText={text => handleChangeInput('currentPassword', text)}
      />

      <InputPassword
        label='Escribe tu nueva contraseña'
        placeholder='Nueva contraseña'
        value={data.newPassword}
        isError={errors.newPassword.error}
        errorText={errors.newPassword.message}
        onChangeText={text => handleChangeInput('newPassword', text)}
      />

      <InputPassword
        label='Confirmar nueva contraseña'
        placeholder='Confirma tu nueva contraseña'
        value={data.confirmNewPassword}
        isError={errors.confirmNewPassword.error}
        errorText={errors.confirmNewPassword.message}
        onChangeText={text => handleChangeInput('confirmNewPassword', text)}
      />
    </View>
  );
};

export default ChangePasswordForm;
