import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import BottomModal from '../BottomModal';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import AlertYellow from '@/components/icons/AlertYellow';

interface AccountActivationModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const ChangeNipBottomModal: React.FC<AccountActivationModalProps> = ({
  isVisible,
  onClose,
}) => {
  return (
    <BottomModal isVisible={isVisible} onClose={onClose}>
      <View style={styles.body}>
        <View style={styles.rowContainer}>
          <View style={styles.textContent}>
            <AlertYellow />
            <Text style={styles.title}>
              Indicaciones para completar el cambio de NIP
            </Text>
          </View>
          <Text style={styles.text}>
            Para finalizar el cambio de NIP requieres realizar una consulta de saldo en algún cajero automático.
          </Text>
          <Text style={styles.text}>
            Si no finaliza el proceso en un cajero realizando una consulta de saldo, no podrá utilizar la tarjeta.
          </Text>
        </View>
        <PrimaryButton title='Aceptar' onPress={onClose} />
      </View>
    </BottomModal>
  );
};

export default ChangeNipBottomModal;

const styles = StyleSheet.create({
  body: {
    width: '100%',
    paddingVertical: 24,
    paddingHorizontal: 32,
    gap: 24,
    marginBottom: 24,
  },
  rowContainer: {
    gap: 24,
  },
  textContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 11,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter',
    lineHeight: 29.05,
    fontWeight: '400',
    color: '#ffffff',
  },
  text: {
    fontSize: 14,
    fontFamily: 'Inter',
    lineHeight: 16.94,
    fontWeight: '400',
    color: '#ffffff',
    paddingLeft: 21,
  },
});
