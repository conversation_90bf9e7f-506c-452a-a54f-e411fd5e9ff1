import { View, TextInput, StyleSheet, ScrollView } from 'react-native';
import Dropdown from '@/components/atoms/DropDown';
import { FormDataClarification, CLARIFICATION_TYPES } from "@/types/clarificationsTypes";
import { ThemedText } from '@/components/ui/ThemedText';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import ImageUploadInput from '@/components/molecules/home/<USER>';
import { useInactivity } from '@/context/InactivityContext';

type Props = {
  formData: FormDataClarification;
  onSetFormData: (data: FormDataClarification) => void;
  handleImageChange: (index: number) => void
  handleRemoveImage: (index: number) => void
  handleSubmit: () => void
};

const clarificationOptions = Object.values(CLARIFICATION_TYPES).map(value => ({
  label: value,
  value,
}));


const ClarificationsBody = ({
  formData,
  onSetFormData,
  handleImageChange,
  handleRemoveImage,
  handleSubmit
}: Props) => {
  const { resetInactivityTimer } = useInactivity();
  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollViewContent}
        bounces={false}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <View style={styles.item}>
            <ThemedText type='caption' lightColor='#000000'>
              Elige el tipo de aclaración
            </ThemedText>
            <Dropdown
              data={clarificationOptions}
              placeholder='Aclaración'
              onChange={item => onSetFormData({ ...formData, type: item.value as CLARIFICATION_TYPES })}
            />
          </View>
          <View style={styles.item}>
            <ThemedText type='caption' lightColor='#000000'>
              Describe lo ocurrido
            </ThemedText>
            <TextInput
              style={styles.textAreaInput}
              multiline
              numberOfLines={4}
              placeholder='Descripción'
              placeholderTextColor='#9093A5'
              returnKeyType='done'
              onChangeText={text => {
                onSetFormData({ ...formData, description: text })
                resetInactivityTimer();
              }}
            />
          </View>
          <View style={[styles.item, { gap: 16 }]}>
            <ThemedText type='caption' lightColor='#000000'>
              Agrega evidencia para acelerar tu aclaración
            </ThemedText>
            <View style={styles.imageInputsContainer}>
              {[0, 1, 2].map(index => (
                <ImageUploadInput
                  key={index}
                  imageName={formData.images?.[index]?.fileName}
                  onPress={() => handleImageChange(index)} // manejar desde el padre
                  removeImage={() => handleRemoveImage(index)}
                />
              ))}
            </View>
          </View>
        </View>
        <PrimaryButton
          title='Enviar'
          disable={!formData.type || !formData.description}
          onPress={handleSubmit}
        />
      </ScrollView>
    </View>
  );
};
export default ClarificationsBody;

// styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 24,
    backgroundColor: '#E6E8F4',
    gap: 24,
  },
  content: {
    flex: 1,
    gap: 24,
  },
  item: {
    gap: 6,
    width: '100%',
  },
  textAreaInput: {
    maxHeight: 180,
    // height: '100%',
    // height: 180,
    minHeight: 150,
    width: '100%',
    maxWidth: '100%',
    minWidth: '100%',
    textAlignVertical: 'top',
    borderColor: 'gray',
    borderWidth: 1,
    borderRadius: 8,
    padding: 10,
    backgroundColor: '#FFFFFF',
    fontSize: 16,
    color: '#000000',
  },
  imageInputsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: 650,
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
    paddingVertical: 24,
    gap: 24,
  },
});
