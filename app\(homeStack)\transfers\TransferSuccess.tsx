import React, { useEffect } from 'react';
import { router, useLocalSearchParams } from 'expo-router';
import SuccesTranferLayer from '@/components/organisms/tranfers/SuccesTranferLayer';
import { useAccountContext } from '@/context/AccountContext';
import { useBlockAndroidBack } from '@/hooks/useBlockAndroidBack';
import { useTransferContext } from '@/context/TransferContext';
import { useUserContext } from '@/context/UserContext';

const TransferSuccess = () => {
  useBlockAndroidBack();
  const { user } = useUserContext()
  const { accountData, setTransferCalculate } = useAccountContext();
  const { transferData, clearTransferData } = useTransferContext();
  const { cep } = useLocalSearchParams();

  useEffect(() => {
    if (!transferData) {
      router.replace('/(homeStack)/transfers');
    }
  }, [transferData]);

  if (!transferData) {
    return null;
  }

  const {
    amount,
    concept,
    name,
    number,
    bank,
    reference = '',
    folio_operation = '',
    trackingKey = '',
    commission = '0',
  } = transferData;

  const formattedAccountNumber =
    bank === 'CONVENIA'
      ? user?.convenia_account?.slice(-4) ?? ''
      : accountData?.clabe.slice(-4) || '****';

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    };
    return date.toLocaleDateString('es-ES', options);
  };

  return (
    <SuccesTranferLayer
      amount={amount}
      concept={concept}
      name={name}
      number={number}
      account_out={formattedAccountNumber}
      formatDate={formatDate}
      isConvenia={bank === 'CONVENIA'}
      onFinished={() => {
        setTransferCalculate(null);
        clearTransferData();
        router.dismissTo('/(homeStack)/transfers');
      }}
      reference={reference}
      operation={folio_operation}
      operationType={bank === 'CONVENIA' ? 'Transferencia' : 'Transferencia a tercero'}
      trackingKey={trackingKey}
      commission={Number(commission)}
      cep={cep as string || undefined}
    />
  );
};

export default TransferSuccess;
