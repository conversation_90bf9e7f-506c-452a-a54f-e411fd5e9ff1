import * as React from "react"
import Svg, { SvgProps, Path } from "react-native-svg"
const BtnCardTransfer = (props: SvgProps) => (
    <Svg
        width={29}
        height={29}
        fill="none"
        {...props}
    >
        <Path
            stroke="#F4F5FB"
            strokeLinecap="round"
            strokeWidth={1.5}
            d="M12.333 5.167c-4.4 0-6.6 0-7.966 1.367C3.001 7.902 3 10.101 3 14.5c0 4.4 0 6.6 1.367 7.966 1.367 1.366 3.567 1.368 7.966 1.368h1.75M17 5.167c4.4 0 6.6 0 7.966 1.367 1.241 1.24 1.355 3.167 1.367 6.8"
        />
        <Path
            stroke="#F4F5FB"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M18.75 16.834v7m0 0 2.333-2.333m-2.333 2.333-2.334-2.333M24 23.834v-7m0 0 2.333 2.333M24 16.834l-2.334 2.333"
        />
        <Path
            stroke="#F4F5FB"
            strokeLinecap="round"
            strokeWidth={1.5}
            d="M12.333 19.167H7.666m-4.666-7h5.833m17.5 0H13.5"
        />
    </Svg>
)
export default BtnCardTransfer
