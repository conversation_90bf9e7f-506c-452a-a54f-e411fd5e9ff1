import React, { useState } from 'react';
import { useLocalSearchParams, router } from 'expo-router';
import { generateOTPCode, verifyOTPCode } from '@/services/userService';
import VerifyEmailNumberToTransfer from '@/components/organisms/tranfers/VerifyEmailNumberToTransfer';
import { TransferContact, transferDock } from '@/services/transfersService';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import { useTransferContext } from '@/context/TransferContext';

// Componente funcional VerifyEmail
const VerifyEmail = () => {
  const { transferData, setTransferData } = useTransferContext();
  // Obtiene el parámetro de búsqueda local 'email'
  const { number, email, token, amount, concept, bank, name, beneficiaryBank } =
    useLocalSearchParams();
  // Estado para almacenar el número de verificación
  const [numberToken, setNumberToken] = useState('');
  // Estado para almacenar el nuevo token
  const [newToken, setNewToken] = useState(Array.isArray(token) ? token[0] : token);

  const [hasError, setHasError] = useState(false);
  const [loading, setLoading] = useState(false);

  const hanldeSetCode = (number: string) => {
    // Función para establecer el código
    setNumberToken(number);
    setHasError(false);
  }

  const parseAmount = (amountStr: string): number => {
    // Elimina comas (ej: 500,000.00 => 500000.00)
    const cleaned = amountStr.replace(/,/g, '');
    return parseFloat(cleaned);
  };

  const transferToAccount = async () => {
    const dataToTransfer: TransferContact = {
      num_clabe: Array.isArray(number) ? number[0] : number,
      amount: parseAmount(Array.isArray(amount) ? amount[0] : amount),
      description: Array.isArray(concept) ? concept[0] : concept,
      email: Array.isArray(email) ? email[0] : email,
      beneficiaryName: Array.isArray(name) ? name[0] : name,
    }
    if (!dataToTransfer.num_clabe.startsWith('527') || !dataToTransfer.num_clabe.startsWith('221')) {
      dataToTransfer.legalBank = Array.isArray(beneficiaryBank) ? beneficiaryBank[0] : beneficiaryBank;
      dataToTransfer.NameBank = Array.isArray(bank) ? bank[0] : bank;
    }
    const response = await transferDock(dataToTransfer);
    return response.statusCode === 201 ? response : false;
  }

  // Función para manejar el evento de continuar
  const handleContinue = async () => {
    setLoading(true);
    // Verificar si el número de verificación es válido
    const response = await verifyOTPCode({
      email: Array.isArray(email) ? email[0] : email,
      code: numberToken,
      access: newToken,
    });
    // Si el código de estado es 200
    if (response.statusCode === 200) {
      // Navega a la ruta para confirmar la transferencia
      const res = await transferToAccount()
      setLoading(false);
      if (!res) {
        router.push({
          pathname: '/(homeStack)/transfers/TransferError',
        });
      } else {
        setTransferData({
          ...transferData,
          number: `****${(Array.isArray(number) ? number[0] : number).slice(-4)}`,
          reference: res.data?.reference ?? '',
          folio_operation: res.data?.external_transaction_id ?? '',
          trackingKey: res.data?.transaction_id ?? '',
          commission: String(res.data?.commission),
          amount: Array.isArray(amount) ? amount[0] : amount ?? '',
          concept: Array.isArray(concept) ? concept[0] : concept ?? '',
          name: Array.isArray(name) ? name[0] : name ?? '',
          bank: Array.isArray(bank) ? bank[0] : bank ?? '',
          beneficiaryBank: Array.isArray(beneficiaryBank) ? beneficiaryBank[0] : beneficiaryBank ?? '',
        });
        router.push({
          pathname: '/(homeStack)/transfers/TransferSuccess',
          params: {
            cep: res.data?.cep || null,
          }
        });
      }
    } else {
      // Actualiza el estado de error
      setHasError(true);
    }
  };

  const handleResendCode = async () => {
    // Función para reenviar el código OTP
    const response = await generateOTPCode(Array.isArray(email) ? email[0] : email);
    setNewToken(response.data.token)
  }

  return (
    // Renderiza el componente VerifyEmailNumber con las propiedades necesarias
    <>
      <VerifyEmailNumberToTransfer
        email={email}
        hasError={hasError}
        number={numberToken}
        onSetNumber={hanldeSetCode}
        onContinue={handleContinue}
        onResendCode={handleResendCode} // Función para reenviar el código
      />
      {loading && <LoadingScreen />}
    </>
  );
};

export default VerifyEmail;
