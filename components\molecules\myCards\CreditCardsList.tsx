import type { Card } from '@/types/services/cards';
import { Dimensions, FlatList, StyleSheet, View } from 'react-native';
import { CARD_TYPES } from '@/constants/typeCard';
import { CARD_STATUS } from '@/constants/CardStatus';
import React, { useMemo, useState } from 'react';
import CreditCard from './CreditCard';
import { useFocusEffect } from 'expo-router';

type Props = {
  cards: Card[];
};

const calculateHeightCardresponsive = () => {
  const screenWidth = Dimensions.get('window').width;
  const width = screenWidth / 2 - 48;
  const height = (width * 250) / 145;
  return height;
};

const CreditCardsList = ({ cards }: Props) => {
  // Usar useMemo para evitar recreaciones innecesarias de la tarjeta auxiliar
  const auxEmptyCard: Card = useMemo(() => ({
    id: '0-empty-aux',
    pan: '123',
    expiration_date: '12321',
    card_type: CARD_TYPES.PHYSICAL,
    card_last_4: '22',
    format_expiration_date: '22',
    status: CARD_STATUS.NORMAL,
  }), []);

  // Usar useMemo para la lista extendida para evitar recreaciones innecesarias
  const extendCards = useMemo(() => {
    return cards.length % 2 === 0 ? cards : [...cards, auxEmptyCard];
  }, [cards, auxEmptyCard]);

  // Estado para bloquear interacción durante la navegación
  const [isNavigating, setIsNavigating] = useState(false);

  // Resetear isNavigating cuando la pantalla obtiene el foco
  useFocusEffect(
    React.useCallback(() => {
      setIsNavigating(false);
    }, [])
  );

  // Función para manejar el click en una tarjeta
  const handleCardPress = async (card: Card, onNavigate: (card: Card) => Promise<void> | void) => {
    if (isNavigating) return;
    setIsNavigating(true);
    try {
      await onNavigate(card);
    } finally {
      // Si quieres desbloquear después de navegar, puedes poner un timeout o usar un callback de navegación
      // setIsNavigating(false);
    }
  };

  return (
    <FlatList
      data={extendCards}
      renderItem={({ item }) =>
        item.id === '0-empty-aux' ? (
          <View style={styles.emptyCard} />
        ) : (
          <CreditCard
            card={item}
            disabled={isNavigating}
            onPress={handleCardPress}
          />
        )
      }
      keyExtractor={item => item.id.toString()}
      numColumns={2}
      columnWrapperStyle={styles.row}
      contentContainerStyle={{
        gap: 24,
        padding: 24,
      }}
      style={{
        backgroundColor: '#E6E8F4',
      }}
      removeClippedSubviews={false}
      extraData={cards.length.toString() + isNavigating.toString()}
    />
  );
};

const styles = StyleSheet.create({
  row: {
    flex: 1,
    justifyContent: 'space-between',
    gap: 40,
  },
  emptyCard: {
    flex: 0.5,
    height: calculateHeightCardresponsive(),
  },
});

export default CreditCardsList;
