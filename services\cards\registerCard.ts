import api from "../api/api";
import type { RegisterCardPayload } from "@/types/services/cards";

const registerCard = async (data: RegisterCardPayload) => {
    try {
        const response = await api.post('/card-assignment/single', data);
        const responseStatus = response.data.code

        if (responseStatus !== 200) {
            throw new Error(response.data.message);
        }

        return response.data;
    } catch (error: unknown) {
        if (error instanceof Error) {
            throw error; // Si ya es un Error, lo lanzamos directamente
        } else {
            // Si es otro tipo (objeto, string, etc.), lo convertimos a string apropiadamente
            throw new Error(String(error));
        }
    }
}

export default registerCard;