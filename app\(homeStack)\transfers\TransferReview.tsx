import React, { useEffect } from 'react';
import { router } from 'expo-router';
import PageBaseTwiceBlackWith from '@/components/templates/PageBaseTwiceBlackWhite';
import ReviewTransferHeader from '@/components/organisms/tranfers/ReviewTransferHeader';
import ReviewTransferBottom from '@/components/organisms/tranfers/ReviewTransferBottom';
import { ScrollView, View } from 'react-native';
import { useTransferContext } from '@/context/TransferContext';
import { useAccountContext } from '@/context/AccountContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const TransferReview = () => {
  const { transferData, clearTransferData, currentContact } = useTransferContext();
  const { setTransferCalculate } = useAccountContext();
  const insets = useSafeAreaInsets();

  useEffect(() => {
    if (!transferData) {
      router.replace('/(homeStack)/transfers');
    }
  }, [transferData]);

  if (!transferData) {
    // Puedes redirigir o mostrar mensaje de error si no hay datos
    return null;
  }

  const { amount } = transferData;

  const goBackDestinationInfo = () => router.dismiss(2);
  const goBackAmountInfo = () => router.back();

  const handleOnConfirm = () => {
    router.replace('/(homeStack)/transfers/ConfirmTransfer');
  };

  const handleCancel = () => {
    setTransferCalculate(null);
    clearTransferData()
    if (currentContact) {
      router.dismiss(2);
    } else {
      router.dismiss(3);
    }
  };

  return (
    <View style={{
      flex: 1,
      backgroundColor: '#000000',
      paddingTop: insets.top, // Ajusta el margen superior para que no se solape con la barra de notificaciones en Android
    }}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{
          flexGrow: 1,
          justifyContent: 'space-between',
          backgroundColor: '#E6E8F4',
          paddingBottom: insets.bottom, // Ajusta el margen inferior para que no se solape con el teclado en Android
        }}
      >
        <PageBaseTwiceBlackWith
          topChildren={
            <ReviewTransferHeader
              goBackDestinationInfo={goBackDestinationInfo}
              goBackAmountInfo={goBackAmountInfo}
            />
          }
          bottomChildren={
            <ReviewTransferBottom
              amount={amount}
              onConfirm={handleOnConfirm}
              onCancel={handleCancel}
            />
          }
        />
      </ScrollView>
    </View>
  );
};

export default TransferReview;
