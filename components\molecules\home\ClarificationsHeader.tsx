import { View, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { ThemedText } from '@/components/ui/ThemedText';

const ClarificationsHeader = () => {
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View
        style={{
          paddingHorizontal: 24,
          paddingTop: 24,
          paddingBottom: 61,
          gap: 24,
        }}
      >
        <ThemedText type='text24' lightColor='#ffffff'>
          Aclaraciones
        </ThemedText>
        <ThemedText type='defaultSemiBold' lightColor='#ffffff'>
          Lamentamos lo ocurrido. Cuéntanos cómo podemos ayudarte a solucionarlo.
        </ThemedText>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default ClarificationsHeader;
