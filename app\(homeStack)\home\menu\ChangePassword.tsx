import React, { useState, useEffect } from 'react';
import PageBaseWithConditions from '@/components/templates/PageBaseWithConditions';
import ChangePasswordForm from '@/components/molecules/home/<USER>';
import { router } from 'expo-router';
import validatePasswordComplexity from '@/shared/validatePasswordComplexity';
import validatePasswordLength from '@/shared/validatePasswordLength';
import { useAppContext } from '../../../../context/AppProvider';
import { setFieldError } from '@/shared/setFieldError';
import { useUserContext } from '@/context/UserContext';
import { changePassword } from '@/services/userService';
import { StatusBar } from 'react-native';
import { useInactivity } from '@/context/InactivityContext';

type ErrorProps = {
  error: boolean;
  message?: string;
};

export type ErrorsProps = {
  currentPassword: ErrorProps;
  newPassword: ErrorProps;
  confirmNewPassword: ErrorProps;
};

const initialErrorsState = {
  currentPassword: {
    error: false,
    message: undefined,
  },
  newPassword: {
    error: false,
    message: undefined,
  },
  confirmNewPassword: {
    error: false,
    message: undefined,
  },
};

const ChangePassword = () => {
  const { user } = useUserContext();
  const { setShowSuccessAlert } = useAppContext();
  const { resetInactivityTimer } = useInactivity();

  const [data, setData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: '',
  });

  const [errors, setErrors] = useState<ErrorsProps>(initialErrorsState);
  const updateErrors = (
    error: boolean,
    field: keyof ErrorsProps,
    message: string | undefined,
  ) => {
    setFieldError<ErrorsProps>(error, field, message, setErrors);
  };
  const isValidnewPassword =
    validatePasswordComplexity(data.newPassword) &&
    validatePasswordLength(data.newPassword);

  const isValidConfirmNewPassword =
    validatePasswordComplexity(data.confirmNewPassword) &&
    validatePasswordLength(data.confirmNewPassword);

  const isValidNewsPasswords = isValidnewPassword && isValidConfirmNewPassword;

  const isMatchPasswords = data.newPassword === data.confirmNewPassword;

  const isValidCurrentPassword =
    validatePasswordComplexity(data.currentPassword) &&
    validatePasswordLength(data.currentPassword);

  const handleValidateForm = () => {
    setErrors(initialErrorsState);

    if (isValidNewsPasswords && isMatchPasswords) {
      handleSaveData();
    }
  };

  const handleChangeInput = (key: string, value: string) => {
    updateErrors(false, key as keyof ErrorsProps, undefined);
    setData(prevData => ({
      ...prevData,
      [key]: value,
    }));
    resetInactivityTimer(); // Reiniciar el temporizador de inactividad
  };

  useEffect(() => {
    if (!isMatchPasswords && data.confirmNewPassword !== '') {
      updateErrors(
        true,
        'confirmNewPassword',
        'La nueva contraseña no coincide',
      );
      updateErrors(true, 'newPassword', undefined);

      return;
    }

    // ---codigo por si se requiere validar las contraseñas cumplen con los criterios----

    if (
      !isValidnewPassword &&
      data.newPassword !== '' &&
      data.confirmNewPassword !== ''
    ) {
      updateErrors(true, 'newPassword', undefined);
    } else {
      updateErrors(false, 'newPassword', undefined);
    }

    if (!isValidConfirmNewPassword && data.confirmNewPassword !== '') {
      updateErrors(
        true,
        'confirmNewPassword',
        'La nueva contraseña no es válida',
      );
    } else {
      updateErrors(false, 'confirmNewPassword', undefined);
    }
  }, [
    data.confirmNewPassword,
    data.newPassword,
    isMatchPasswords,
    isValidConfirmNewPassword,
    isValidnewPassword,
  ]);

  const handleSaveData = async () => {
    const response = await changePassword({
      email: Array.isArray(user?.email) ? user?.email[0] : user?.email,
      oldPassword: data.currentPassword,
      newPassword: data.newPassword,
    });

    if (response.statusCode === 200) {
      router.back();
      setShowSuccessAlert(true);
      return;
    }

    if (response.statusCode === 400) {
      updateErrors(
        true,
        'currentPassword',
        'La contraseña actual es incorrecta',
      );
      return;
    }

    if (response.statusCode === 404) {
      updateErrors(
        true,
        'confirmNewPassword',
        'Error al cambiar la contraseña',
      );
    }
  };

  const hasErrors = Object.values(errors).some(error => error.error);

  const enableSuccessBtn =
    !hasErrors &&
    data.currentPassword &&
    isValidCurrentPassword &&
    data.newPassword &&
    data.confirmNewPassword &&
    isMatchPasswords;

  return (
    <>
      <StatusBar backgroundColor='#ffffff' barStyle='dark-content' translucent={false} />
      <PageBaseWithConditions
        title='Cambiar contraseña'
        description='Su nueva contraseña debe ser diferente de las utilizadas anteriormente.'
        conditions={[
          'Debe tener al menos 8 caracteres',
          'Debe tener números y letras',
          'Debe tener al menos una letra mayúscula'
        ]}
        titlePrimaryButton='Cambiar contraseña'
        titleSecondaryButton='Cancelar'
        enableSaveBtn={!enableSuccessBtn}
        onPressPrimaryBtn={handleValidateForm}
        onPressSecondaryBtn={() => router.back()}
      >
        <ChangePasswordForm
          data={data}
          errors={errors}
          handleChangeInput={handleChangeInput}
        />
      </PageBaseWithConditions>
    </>
  );
};

export default ChangePassword;
