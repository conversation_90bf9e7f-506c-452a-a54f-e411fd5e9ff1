import { StyleSheet, Text, View } from 'react-native';
import React, { useState } from 'react';
import { router, useLocalSearchParams } from 'expo-router';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import { CARD_TYPES } from '@/constants/typeCard';
import CardDetails from '@/components/organisms/cards/CardDetails';
import OnOffCardHelpModal from '@/components/organisms/cards/OnOffCardHelpModal';
import AlertSucess from '@/components/atoms/AlertSucess';
import { useAppContext } from '../../../context/AppProvider';
import { Card as TypeCard } from '@/types/services/cards';
import { CARD_STATUS } from '@/constants/CardStatus';
import updateCardStatus from '@/services/cards/updateCardStatus';
import getCVV from '@/services/cards/getCVV';
import getRemainTimeCvv from '@/utils/getRemainTimeCvv';
import { DecryptAsync } from '@/shared/decrypted';
import ChangeNipBottomModal from '@/components/organisms/cards/ChangeNipBottomModal';

const Card = () => {
  const { showSuccessAlert, showAlert, setCardsLoading, hideAlert, alertText, showChangeNipModal, setShowChangeNipModal } = useAppContext();

  const params = useLocalSearchParams();
  const cardParams = params.cardData as string;
  const cardData: TypeCard = JSON.parse(cardParams);
  const [showModal, setShowModal] = useState(false);
  const [isEnabled, setIsEnabled] = useState(cardData.status === CARD_STATUS.NORMAL);
  // const [showAlert, setShowAlert] = useState(false);
  // const [showAlertChangeNip, setShowAlertChangeNip] = useState(false);
  // update status loading
  const [updateStatusLoading, setUpdateStatusLoading] = useState(false);
  const [showModalChangeNip, setShowModalChangeNip] = useState(false);

  // show cvv state
  const [cvv, setCvv] = useState<null | string>(null);

  // time cvv remain
  const [timeLeft, setTimeLeft] = useState<number | null>(null); // 5 minutes in seconds

  const openUpdateModal = () => setShowModal(true);

  const closeUpdateModal = () => setShowModal(false);

  const handleUpdateCardStatus = () => {
    setUpdateStatusLoading(true);
    const status = isEnabled ? CARD_STATUS.BLOCKED : CARD_STATUS.NORMAL;
    updateCardStatus(cardData.id, status).then((res) => {
      const newStatus = res.card_status === CARD_STATUS.BLOCKED ? CARD_STATUS.BLOCKED : CARD_STATUS.NORMAL;
      if (newStatus === CARD_STATUS.BLOCKED) {
        showAlert('La tarjeta se ha apagado con éxito');
        setIsEnabled(false);

      } else {
        showAlert('La tarjeta se encendió con éxito');
        setIsEnabled(true);
      }
      setCardsLoading(true);
      setShowModal(false);
      setUpdateStatusLoading(false);
    }).catch((err) => {
      console.error(err);
    });
  }

  const handleGetCvv = () => {
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    getCVV(cardData.id, cardData.card_type, cardData.expiration_date, timeZone).then((response) => {
      // ya que tengamos el cvv, lo desencriptamos, cuadno termine de desencriptar, seteamos el cvv y seteamos el tiempo restante
      DecryptAsync(response.cvv).then((decrypted) => {
        const remainTime = getRemainTimeCvv(response.expiration_date);
        setTimeLeft(remainTime);
        setCvv(decrypted);
      }).catch((err) => {
        console.error('err =>', err);
      })
    }).catch((err) => {
      console.error('err =>', err);
    })
  }

  const urlImageCard =
    cardData.card_type === CARD_TYPES.PHYSICAL
      ? require('@/assets/images/DetailCard-b.png')
      : require('@/assets/images/DetailCard-g.png');

  // funcion  para mostrar despues de 2 segundos solo si el showChangeNipModal
  const showChangeNipModalAfter2Seconds = () => {
    if (showChangeNipModal) {
      setTimeout(() => {
        setShowModalChangeNip(true);
      }, 2000);
    }
  }

  // cerrar el modal de cambiar nip  en el contexto y en el componente
  const hideChangeNipModal = () => {
    setShowChangeNipModal(false);
    setShowModalChangeNip(false);
  }

  return (
    <>
      <PageBaseBWReturnButton
        onGoBack={() => {
          hideAlert();
          router.back();
        }}
        topChildren={
          <View style={{ padding: 24 }}>
            <Text style={styles.title}>
              Detalle de tarjeta {cardData.card_last_4}
            </Text>
          </View>
        }
        bottomChildren={
          <CardDetails
            urlImageCard={urlImageCard}
            isEnabled={isEnabled}
            toggleSwitch={openUpdateModal}
            card={cardData}
            cvv={cvv}
            timeLeft={timeLeft}
            onSetCvv={(value) => setCvv(value)}
            onGetCvv={handleGetCvv}
            onResetTimeLeft={() => setTimeLeft(null)}
          />
        }
      />
      <OnOffCardHelpModal
        showModal={showModal}
        isEnable={isEnabled}
        loading={updateStatusLoading}
        onPressPrimaryBtn={handleUpdateCardStatus}
        onClose={closeUpdateModal}
      />
      <AlertSucess
        text={alertText}
        timeToHide={3000}
        isVisible={showSuccessAlert}
        onSetShowAlert={hideAlert}
        onExecuteCallbackFinally={showChangeNipModalAfter2Seconds}
      />
      <ChangeNipBottomModal
        isVisible={showModalChangeNip}
        onClose={hideChangeNipModal}
      />
    </>
  );
};

export default Card;

const styles = StyleSheet.create({
  title: {
    fontFamily: 'Inter',
    fontWeight: '400',
    fontSize: 24,
    lineHeight: 29,
    color: '#F4F5FB',
  },
});
