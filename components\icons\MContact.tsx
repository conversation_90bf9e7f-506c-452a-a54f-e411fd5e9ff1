import * as React from "react"
import Svg, {
    SvgProps,
    Path,
    Defs,
    LinearGradient,
    Stop,
} from "react-native-svg"
const MContact = (props: SvgProps) => (
    <Svg
        width={24}
        height={24}
        fill="none"
        {...props}
    >
        <Path
            fill="url(#a)"
            d="M12 17.998a2.25 2.25 0 0 1-2.211-1.83 7.501 7.501 0 1 1 8.555-11.17 7.5 7.5 0 0 1 1.12 3.252c.04.412-.3.748-.714.748s-.745-.337-.795-.748a6 6 0 1 0-7.927 6.417A2.249 2.249 0 1 1 12 17.998Zm-6-2.25v-.04A9.009 9.009 0 0 1 4.878 14.5a2.25 2.25 0 0 0-.378 1.248v.75c0 2.957 2.79 6 7.5 6s7.5-3.043 7.5-6v-.75a2.25 2.25 0 0 0-2.25-2.25H15c.329.435.563.945.675 1.5h1.575a.75.75 0 0 1 .75.75v.75c0 2.157-2.148 4.5-6 4.5s-6-2.343-6-4.5v-.75Zm6-3.75a3.75 3.75 0 0 0-2.358.834A4.485 4.485 0 0 1 7.5 8.998a4.5 4.5 0 1 1 6.858 3.834A3.75 3.75 0 0 0 12 11.998Zm-3-3a3 3 0 1 0 6 0 3 3 0 0 0-6 0Z"
        />
        <Defs>
            <LinearGradient
                id="a"
                x1={4.5}
                x2={19.5}
                y1={12}
                y2={12}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
        </Defs>
    </Svg>
)
export default MContact
