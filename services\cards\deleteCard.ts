import api from '../api/api';

const deleteCard = async (cardId: string) => {
  try {
    const response = await api.delete(`/dock-cards/delete-card/${cardId}`);
    const responseStatus = response.data.statusCode;

    if (responseStatus !== 200) {
      throw new Error(response.data.message);
    }

    return response.data.message;
  } catch (error: unknown) {
    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error(String(error));
    }
  }
};

export default deleteCard;
