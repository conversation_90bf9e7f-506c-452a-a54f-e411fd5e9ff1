import * as React from "react"
import Svg, {
    SvgProps,
    Path,
    Defs,
    LinearGradient,
    Stop,
} from "react-native-svg"
const LoadingIcon = (props: SvgProps) => (
    <Svg
        width={80}
        height={80}
        fill="none"
        {...props}
    >
        <Path
            stroke="url(#a)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={5}
            d="M40 15V2.5m17.708 19.792 8.959-8.959M65 40h12.5M57.708 57.708l8.959 8.959M40 65v12.5M22.292 57.708l-8.959 8.959M15 40H2.5m19.792-17.708-8.959-8.959"
        />
        <Defs>
            <LinearGradient
                id="a"
                x1={2.499}
                x2={77.501}
                y1={40.003}
                y2={40.003}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.019} stopColor="#DCB992" />
                <Stop offset={0.249} stopColor="#DAB890" />
                <Stop offset={0.459} stopColor="#D5B289" />
                <Stop offset={0.684} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
        </Defs>
    </Svg>
)
export default LoadingIcon
