import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import BottomModal from '../BottomModal';
import { ThemedText } from '@/components/ui/ThemedText';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import PowerOnCArd from '@/components/icons/PowerOnCArd';
import PowerOffCard from '@/components/icons/PowerOffCard';

type Props = {
  showModal: boolean;
  isEnable: boolean;
  loading: boolean;
  onPressPrimaryBtn: () => void;
  onClose: () => void;
};

const OnOffCardHelpModal = ({
  showModal,
  isEnable,
  loading,
  onClose,
  onPressPrimaryBtn,
}: Props) => {
  const texts = isEnable
    ? {
      title: 'Apagar tarjeta',
      subtitle: 'Si apagas tu tarjeta',
      text: 'Al apagar la tarjeta, no podrás hacer transferencias',
      text2: 'No podrás hacer compras con tu tarjeta',
      textBnt: 'Apagar tarjeta',
    }
    : {
      title: 'Encender tarjeta',
      subtitle: 'Si enciendes tu tarjeta',
      text: 'Al encender la tarjeta, podrás hacer envíos a otras cuentas',
      text2: 'Podrás hacer compras con tu tarjeta física ',
      textBnt: 'Encender tarjeta',
    };

  return (
    <BottomModal isVisible={showModal} onClose={onClose}>
      <View style={styles.imageCard}>
        {!isEnable ? (
          <PowerOnCArd style={styles.imageCard} />
        ) : (
          <PowerOffCard style={styles.imageCard} />
        )}
      </View>
      <Text style={styles.title}>{texts.title}</Text>
      <ThemedText
        type='defaultSemiBold'
        lightColor='#ffffff'
        style={{ paddingTop: 24 }}
      >
        {texts.subtitle}
      </ThemedText>
      <View style={styles.body}>
        <Text style={styles.text}>
          {'\u2022'} {texts.text}
        </Text>
        <Text style={styles.text}>
          {'\u2022'} {texts.text2}
        </Text>
        <Text style={styles.text}>
          Recuerda que no genera ningún costo el encender y apagar la tarjeta.
        </Text>
        <PrimaryButton
          title={texts.textBnt}
          disable={loading}
          onPress={onPressPrimaryBtn}
        />

        <SecondaryButton
          title='Cancelar'
          disabled={loading}
          onPress={() => {
            onClose();
          }}
        />
      </View>
    </BottomModal>
  );
};

export default OnOffCardHelpModal;

const styles = StyleSheet.create({
  imageCard: {
    paddingTop: 8,
  },
  body: {
    width: '100%',
    paddingVertical: 24,
    paddingHorizontal: 32,
    gap: 24,
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter',
    lineHeight: 29.05,
    fontWeight: '400',
    paddingTop: 12,
    color: '#ffffff',
  },
  text: {
    fontSize: 14,
    fontFamily: 'Inter',
    lineHeight: 16.94,
    fontWeight: '400',
    color: '#ffffff',
  },
});
