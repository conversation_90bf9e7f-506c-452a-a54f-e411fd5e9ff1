import {
  StyleSheet,
  Text,
  View,
  Image,
  SafeAreaView,
  ScrollView,
  Platform,
  StatusBar,
  Linking,
  TouchableOpacity,
} from 'react-native';
import React, { useRef } from 'react';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import AntDesign from '@expo/vector-icons/AntDesign';
import { ThemedText } from '@/components/ui/ThemedText';
import ViewShot from 'react-native-view-shot';

type Props = {
  onFinished: () => void;
  formatDate: (date: Date) => string;
  amount: string;
  concept: string;
  name: string;
  number: string;
  account_out: string;
  reference?: string;
  operationType?: string;
  operation?: string;
  trackingKey?: string;
  status?: string;
  isConvenia?: boolean;
  commission: number;
  cep?: string | undefined
};
const SuccesTranferLayer = ({
  formatDate,
  onFinished,
  amount,
  concept,
  name,
  number,
  account_out,
  reference = '**********',
  operationType = 'Transferencia',
  operation = '**********',
  trackingKey = '**********',
  status = 'En proceso',
  isConvenia = false,
  commission,
  cep,
}: Props) => {
  const viewShotRef = useRef<ViewShot>(null);

  const handleOpenURL = async (url: string) => {
    try {
      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
      }
    } catch (error) {
      console.error('Error al abrir el URL:', error);
    }
  };

  return (
    <SafeAreaView style={styles.safe}>
      <StatusBar backgroundColor='#ffffff' barStyle='dark-content' translucent={false} />
      <ScrollView contentContainerStyle={styles.scrollContent}>

        <View style={styles.wrapper}>
          <View style={styles.gap40}>
            <ViewShot ref={viewShotRef} style={styles.captureArea}>
              <View style={styles.content}>
                <View style={styles.centeredGap8}>
                  <Text style={styles.firstText}>Transferencia exitosa</Text>
                  <Image
                    style={styles.successImage}
                    source={require('@/assets/images/checkSuccessPassword.png')}
                  />
                  <ThemedText type='subtitle' lightColor='#F4F5FB'>
                    {formatDate(new Date())} h
                  </ThemedText>
                  <Text style={styles.amount}>${amount}</Text>
                  <ThemedText type='subtitle' lightColor='#F4F5FB'>
                    Comisión: ${commission?.toFixed(2)}
                  </ThemedText>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    width: '100%',
                    gap: 12,
                  }}
                >
                  <View style={styles.centeredGap4}>
                    <Text style={styles.to}>Cuenta</Text>
                    <Text style={styles.to}>****{account_out}</Text>
                  </View>
                  <AntDesign
                    name='arrowright'
                    size={24}
                    color='#ACAFC2'
                    style={{ margin: 'auto' }}
                  />
                  <View style={styles.centeredGap4}>
                    <Text style={styles.to}>
                      {name}
                      {/* juan carlos soza bvadilla */}
                    </Text>
                    <Text style={styles.to}>{number}</Text>
                  </View>
                </View>
                <View style={styles.textContent}>
                  <ThemedText type='defaultSemiBold' lightColor='#9093A5'>
                    Concepto
                  </ThemedText>
                  <Text style={styles.secondText}>{concept}</Text>
                </View>
                {/* {!isConvenia && (
                  <View style={styles.textContent}>
                    <ThemedText type='defaultSemiBold' lightColor='#9093A5'>
                      Estatus
                    </ThemedText>
                    <Text style={styles.secondText}>{status}</Text>
                  </View>)
                } */}
                <View style={styles.textContent}>
                  <ThemedText type='defaultSemiBold' lightColor='#9093A5'>
                    Referencia
                  </ThemedText>
                  <Text style={styles.secondText}>{reference}</Text>
                </View>
                <View style={styles.textContent}>
                  <ThemedText type='defaultSemiBold' lightColor='#9093A5'>
                    Tipo de operación
                  </ThemedText>
                  <Text style={styles.secondText}>{operationType}</Text>
                </View>
                <View style={styles.textContent}>
                  <ThemedText type='defaultSemiBold' lightColor='#9093A5'>
                    Folio de operación
                  </ThemedText>
                  <Text style={styles.secondText}>{operation}</Text>
                </View>
                {!isConvenia && (
                  <View style={styles.textContent}>
                    <ThemedText type='defaultSemiBold' lightColor='#9093A5'>
                      Clave de rastreo
                    </ThemedText>
                    <Text style={styles.secondText}>{trackingKey}</Text>
                  </View>
                )}
                {
                  cep && (
                    <View style={styles.textContent}>
                      <ThemedText type='defaultSemiBold' lightColor='#9093A5'>
                        Verifica el status de tu operación
                      </ThemedText>
                      <TouchableOpacity onPress={() => handleOpenURL(cep)}>
                        <Text style={[styles.secondText, styles.link]}>www.banxico.org.mx</Text>
                      </TouchableOpacity>
                    </View>
                  )
                }
              </View>
            </ViewShot>
          </View>
          <View
            style={{
              width: '100%',
              paddingBottom: Platform.OS === 'ios' ? 0 : 24,
            }}
          >
            <SecondaryButton title='Salir' onPress={onFinished} />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SuccesTranferLayer;

const styles = StyleSheet.create({
  safe: {
    flex: 1,
    backgroundColor: '#000000',
  },
  wrapper: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#000000',
    gap: 40,
    paddingHorizontal: 32,
  },
  textContent: {
    gap: 4,
    alignItems: 'center',
  },
  gap40: {
    gap: 40,
    paddingTop: 50,
  },
  content: {
    gap: 36,
    alignItems: 'center',
  },
  centeredGap8: {
    alignItems: 'center',
    gap: 8,
  },
  centeredGap4: {
    // justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
    flexGrow: 1,
    flexShrink: 1,
    flex: 1,
    // height: 'auto',
  },
  centeredGap6: {
    alignItems: 'center',
    gap: 6,
  },
  firstText: {
    color: '#F4F5FB',
    fontWeight: '500',
    fontSize: 32,
    lineHeight: 38,
    textAlign: 'center',
    fontFamily: 'Inter',
    width: 326,
  },
  labelTitle: {
    textAlign: 'center',
    width: '100%', // O usa un valor como 300 si quieres acotarlo más
    color: '#9093A5',
    fontFamily: 'Inter',
    fontSize: 16,
    fontWeight: '600',
    alignSelf: 'center',
  },
  secondText: {
    color: '#ffffff',
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 19.36,
    textAlign: 'center',
    fontFamily: 'Inter',
    paddingHorizontal: 8,
    maxWidth: 300,
    alignSelf: 'center',
    wordWrap: 'break-word',
  },
  to: {
    color: '#ffffff',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 16.94,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
  amount: {
    color: '#F4F5FB',
    fontWeight: '400',
    fontSize: 36,
    lineHeight: 43.57,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
  successImage: {
    width: 60,
    height: 60,
  },
  shareImage: {
    width: 24,
    height: 24,
  },
  statusRow: {
    alignItems: 'center',
    flexDirection: 'row',
    width: '100%',
    gap: 27,
    paddingTop: 20,
  },
  statusImage: {
    width: 24,
    height: 24,
  },
  captureArea: {
    width: '100%',
    height: 'auto',
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
  },
  link: {
    textDecorationLine: 'underline',
  },
});
