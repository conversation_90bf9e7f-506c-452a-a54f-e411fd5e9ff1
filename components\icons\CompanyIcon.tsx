import * as React from "react"
import Svg, {
    SvgProps,
    Rect,
    Path,
    Defs,
    LinearGradient,
    Stop,
} from "react-native-svg"
const CompanyIcon = (props: SvgProps) => (
    <Svg
        width={40}
        height={41}
        fill="none"
        {...props}
    >
        <Rect width={40} height={40} y={0.5} fill="#000" rx={20} />
        <Path
            fill="url(#a)"
            d="M16.31 17.184c-.202.16-.4.313-.603.47-.027-.025-.054-.041-.067-.058-.802-.928-1.879-1.36-3.163-1.427-.77-.04-1.531.005-2.27.231-1.298.404-2.176 1.2-2.6 2.367-.445 1.225-.432 2.457.104 3.661.604 1.353 1.748 2.128 3.334 2.367 1.17.177 2.315.062 3.4-.4a4.2 4.2 0 0 0 1.483-1.072c.19.157.374.31.572.47a6.174 6.174 0 0 1-1.401 1.002c-.887.458-1.856.672-2.874.701-.96.029-1.888-.099-2.757-.478-1.415-.61-2.311-1.62-2.72-2.969-.361-1.18-.334-2.367.125-3.525.635-1.608 1.928-2.557 3.77-2.89 1.312-.24 2.614-.17 3.861.309.676.255 1.45.795 1.807 1.24Z"
        />
        <Path
            fill="url(#b)"
            d="M33.5 20.397c0 1.099-.189 2.026-.698 2.879-.726 1.213-1.846 1.906-3.291 2.14-.97.156-1.933.103-2.865-.226-1.188-.418-2.064-1.176-2.573-2.263-.761-1.632-.77-3.293.008-4.917.659-1.386 1.856-2.169 3.433-2.423 1.035-.168 2.056-.103 3.031.287 1.458.582 2.33 1.64 2.745 3.055.154.52.218 1.058.21 1.468Zm-.753.127a5.104 5.104 0 0 0-.218-1.566c-.35-1.132-1.048-2.002-2.219-2.485-.846-.349-1.736-.41-2.642-.267-1.355.213-2.372.89-2.92 2.087-.645 1.407-.645 2.85-.047 4.269.44 1.046 1.253 1.755 2.399 2.1a5 5 0 0 0 2.27.13c1.29-.208 2.257-.84 2.851-1.951.393-.726.526-1.51.526-2.317Z"
        />
        <Defs>
            <LinearGradient
                id="a"
                x1={6.503}
                x2={16.5}
                y1={20.501}
                y2={20.501}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.022} stopColor="#93663A" />
                <Stop offset={0.086} stopColor="#805A33" />
                <Stop offset={0.118} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.697} stopColor="#DCB992" />
                <Stop offset={0.789} stopColor="#C9A678" />
                <Stop offset={0.904} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
            <LinearGradient
                id="b"
                x1={23.502}
                x2={33.501}
                y1={20.501}
                y2={20.501}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.022} stopColor="#93663A" />
                <Stop offset={0.086} stopColor="#805A33" />
                <Stop offset={0.118} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.697} stopColor="#DCB992" />
                <Stop offset={0.789} stopColor="#C9A678" />
                <Stop offset={0.904} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
        </Defs>
    </Svg>
)
export default CompanyIcon
