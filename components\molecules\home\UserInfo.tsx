import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { ThemedText } from '@/components/ui/ThemedText';
import { useUserContext } from '@/context/UserContext';

const UserInfo = () => {
  const { user: userData } = useUserContext();
  // const getInitials = (name: string, surname: string) => {
  //   return `${name.charAt(0)}${surname.charAt(0)}`;
  // };
  // Función para obtener las iniciales del nombre completo
  const getInitials = (name: string) => {
    if (!name) return '?'; // Si no hay nombre, mostrar un valor predeterminado
    const words = name.split(' '); // Dividir el nombre completo en palabras
    const initials = words
      .slice(0, 2) // Tomar las dos primeras palabras (nombre y apellido si existen)
      .map(word => word.charAt(0).toUpperCase()) // Obtener la primera letra de cada palabra
      .join(''); // Unir las iniciales
    return initials;
  };

  return (
    <View style={styles.wrapper}>
      <View style={styles.leftContent}>
        <View style={styles.content}>
          <Text style={styles.label}>Nombre</Text>
          <ThemedText type='caption' lightColor='#232429' numberOfLines={1} ellipsizeMode="tail">
            {userData?.fullName!}
          </ThemedText>
        </View>
        <View style={styles.content}>
          <Text style={styles.label}>Correo</Text>
          <ThemedText type='caption' lightColor='#232429' numberOfLines={1} ellipsizeMode="tail">
            {userData?.email!}
          </ThemedText>
        </View>
      </View>
      <View style={styles.rightContent}>
        <View style={styles.initialsCircle}>
          <Text style={styles.initialsText}>
            {getInitials(userData?.fullName!)}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default UserInfo;

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  leftContent: {},
  content: {
    gap: 4,
    paddingVertical: 8,
    maxWidth: 210,
    flexShrink: 1,
  },
  rightContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: '50%',
  },
  initialsCircle: {
    width: 80,
    height: 80,
    borderRadius: '50%',
    backgroundColor: '#ACAFC2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    color: '#000000',
    fontSize: 36,
    fontWeight: '400',
    lineHeight: 43.57,
    textAlign: 'center',
  },
  label: {
    fontSize: 12,
    fontFamily: 'Inter',
    lineHeight: 14.52,
    textRendering: 'geometricPrecision',
    fontWeight: '400',
    color: '#232429',
  },
});
