import React, { useEffect, useState } from 'react';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import MovementsHeader from '@/components/organisms/movements/MovementsHeader';
import TransactionListAll from '@/components/molecules/movements/TransactionListAll';
import { useTransactionContext } from '@/context/TransactionContext';
import LoadingScreen from '@/components/molecules/LoadingScreen';

// Componente funcional Home
const Movements = () => {
  const { fetchTransactions } = useTransactionContext();
  const [selectedFilter, setSelectedFilter] = useState(''); // Estado del filtro seleccionado
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true); // Activar el loader
        await Promise.all([fetchTransactions()]); // Esperar a que ambas funciones terminen
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false); // Desactivar el loader
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    // Renderiza el componente PageBaseTwiceBlackWhite
    // topChildren: Contenido superior, en este caso el DashboardHeader
    // bottomChildren: Contenido inferior, en este caso el TransactionList
    <>
      <PageBaseBWReturnButton
        topChildren={
          <MovementsHeader
            selectedFilter={selectedFilter}
            setSelectedFilter={setSelectedFilter}
          />
        }
        bottomChildren={
          <TransactionListAll
            selectedFilter={selectedFilter}
          />
        }
      />
      {loading && <LoadingScreen />}
    </>
  );
};

// Exporta el componente Home como el valor por defecto
export default Movements;
