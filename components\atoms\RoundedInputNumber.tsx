import { StyleSheet, TextInput, Platform, NativeSyntheticEvent, TextInputKeyPressEventData } from 'react-native';
import React, {
  useState,
  forwardRef,
  useEffect,
  useImperativeHandle,
} from 'react';
import { LinearGradient } from 'expo-linear-gradient';

// Definición de los tipos de propiedades que acepta el componente
type Props = {
  hasError: boolean;
  value: string;
  maxLength: number | undefined;
  onChange: (value: string) => void;
  onBackspace?: () => void;
};

// Definición de la referencia expuesta
export type RoundedInputNumberRef = {
  focus: () => void;
  blur: () => void;
};

// Componente principal RoundedInputNumber
const RoundedInputNumber = forwardRef<RoundedInputNumberRef, Props>(
  ({ hasError, value, maxLength, onChange, onBackspace }, ref) => {
    // Estados locales para manejar el enfoque y los colores
    const [isFocused, setIsFocused] = useState(false);
    const [colorByState, setColorByState] = useState<
      readonly [string, string, ...string[]]
    >(['#C3C6D8', '#C3C6D8']);
    const [locationColor, setLocationColor] = useState<
      readonly [number, number, ...number[]]
    >([0.0, 0.0]);

    // Efecto para actualizar los colores según el valor y el estado de error
    useEffect(() => {
      if (value) {
        setLocationColor([
          0.0041, 0.2303, 0.3945, 0.5056, 0.7408, 0.8693, 0.9156, 0.9486,
          0.9751, 0.9977, 0.9985, 1.0,
        ]);
        setColorByState([
          '#75532F',
          '#8B6539',
          '#9F7A49',
          '#AF8B55',
          '#DCB992',
          '#DAB890',
          '#D5B289',
          '#CCA87C',
          '#BF9B6B',
          '#AF8B56',
          '#AF8B55',
          '#E9D6B8',
        ]);
      } else {
        setLocationColor([0.0, 0.0]);
        setColorByState(['#ffffff', '#ffffff']);
      }
    }, [hasError, isFocused, value]);

    // Función para manejar el cambio de texto y filtrar solo números
    const handleChangeText = (text: string) => {
      const numericValue = text.replace(/[^0-9]/g, ''); // Elimina cualquier carácter que no sea un número
      onChange(numericValue);
    };

    // Manejar evento de tecla presionada para detectar backspace
    const handleKeyPress = (e: NativeSyntheticEvent<TextInputKeyPressEventData>) => {
      if (e.nativeEvent.key === 'Backspace' && !value && onBackspace) {
        onBackspace();
      }
    };

    // Referencia al TextInput
    const inputRef = React.useRef<TextInput>(null);

    // Exponer la referencia del TextInput al componente padre
    useImperativeHandle(ref, () => ({
      focus: () => inputRef.current?.focus(),
      blur: () => inputRef.current?.blur(),
    }));

    return (
      // Componente LinearGradient para el fondo degradado
      <LinearGradient
        colors={colorByState}
        locations={locationColor}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[
          styles.inputNumber,
          isFocused && !value ? styles.inputNumberFocused : null,
          hasError ? styles.inputNumberError : null,
          !isFocused && !value && !hasError ? styles.inputNumberIdle : null,
        ]}
        onTouchStart={() => inputRef.current?.focus()}
      >
        <TextInput
          ref={inputRef}
          numberOfLines={1}
          style={[
            styles.textInput,
            hasError && styles.textInputError,
            value &&
            !hasError && {
              width: 59,
              height: 59.5,
              color: '#AF8B55',
              backgroundColor: '#ffffff',
              borderRadius: 16,
              textAlign: 'center',
            },
          ]}
          value={value}
          placeholderTextColor={hasError ? '#F04438' : '#C3C6D8'}
          placeholder='0'
          keyboardType='numeric'
          maxLength={maxLength}
          multiline={Platform.OS === 'android'}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onChangeText={handleChangeText}
          onKeyPress={handleKeyPress}
          textContentType="oneTimeCode" // Ayuda con el autollenado de códigos SMS
        />
      </LinearGradient>
    );
  },
);

RoundedInputNumber.displayName = 'RoundedInputNumber';

export default RoundedInputNumber;

// Estilos para el componente
const styles = StyleSheet.create({
  inputNumber: {
    width: 64,
    height: 64,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    boxSizing: 'border-box',
    position: 'relative',
  },
  inputNumberFocused: {
    borderColor: '#000000',
    borderWidth: 2,
  },
  inputNumberError: {
    borderColor: '#F04438',
    borderWidth: 2,
  },
  inputNumberIdle: {
    borderColor: '#C3C6D8',
    borderWidth: 1,
  },
  textInput: {
    fontSize: 28,
    textAlign: 'center',
    width: 59,
    minHeight: 60,
    borderRadius: 18,
    backgroundColor: '#ffffff',
    fontFamily: 'Inter',
  },
  textInputError: {
    color: '#FF0000',
    textAlign: 'center',
  },
});
