import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import DashboardCardDetails from '@/components/molecules/home/<USER>';
import { ThemedText } from '@/components/ui/ThemedText';
import { useAccountContext } from '@/context/AccountContext';
import { formatDate } from '@/shared/utils/formatDate';
import { useUserContext } from '@/context/UserContext';

type Props = {
  onSetShowCopyCLABEAlert: () => void;
  onSetShowCopyAccountAlert: () => void;
};

// Componente principal que muestra los detalles de la cuenta
const SeeAccountDetails = ({
  onSetShowCopyCLABEAlert,
  onSetShowCopyAccountAlert,
}: Props) => {
  const {
    accountData
  } = useAccountContext();
  const { user } = useUserContext()
  const date = formatDate(user?.createdAt || '');
  return (
    <View style={styles.wrapper}>
      {/* Componente que muestra los detalles del dashboard */}
      <DashboardCardDetails
        mode='dark'
        isActiveAccount={!!user?.enabled}
        onSetShowCopyCLABEAlert={onSetShowCopyCLABEAlert}
        onSetShowCopyAccountAlert={onSetShowCopyAccountAlert}
        availableResource={accountData?.availableResource!}
        idAccount={user?.convenia_account!}
        membership_number={user?.membership_number!}
      // clabe={accountData?.clabe!}
      />
      {/* Texto temático que muestra el título "Datos de cuenta" */}
      <ThemedText type='defaultSemiBold' lightColor='#000000'>
        Datos de cuenta
      </ThemedText>
      <View style={styles.detailsContainer}>
        {/* Contenedor para el detalle del nombre */}
        <View style={styles.detailContainer}>
          <Text style={styles.label}>Nombre</Text>
          <ThemedText type='subtitle' lightColor='#000000'>
            {user?.fullName}
          </ThemedText>
        </View>
        {/* Contenedor para el detalle de la fecha de alta */}
        <View style={styles.detailContainer}>
          <Text style={styles.label}>Fecha de alta</Text>
          <ThemedText type='subtitle' lightColor='#000000'>
            {date}
          </ThemedText>
        </View>
        {/* Contenedor para el detalle del correo electrónico */}
        <View style={styles.detailContainer}>
          <Text style={styles.label}>Correo electrónico</Text>
          <ThemedText type='subtitle' lightColor='#000000'>
            {user?.email}
          </ThemedText>
        </View>
      </View>
    </View>
  );
};

export default SeeAccountDetails;

// Estilos para el componente
const styles = StyleSheet.create({
  // Estilo para el contenedor principal
  wrapper: {
    backgroundColor: '#E6E8F4',
    flex: 1,
    padding: 24,
    gap: 24,
  },
  // Estilo para el contenedor de detalles
  detailsContainer: {
    gap: 24,
  },
  // Estilo para cada contenedor de detalle individual
  detailContainer: {
    gap: 4,
  },
  // Estilo para las etiquetas de los detalles
  label: {
    fontSize: 14,
    lineHeight: 16.94,
    fontFamily: 'Inter',
    color: '#6F7280',
  },
});
