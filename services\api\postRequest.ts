import api from "./api";

/**
 * Generic function to make POST requests
 * @param endpoint - API endpoint to call
 * @param data - Payload data (can be an object or string)
 * @param options - Additional axios options (optional)
 * @returns Promise with the response data
 */
const postRequest = async <T = any>(
  endpoint: string,
  data: object | string,
  options?: object
): Promise<T> => {
  try {
    const response = await api.post(endpoint, data, options);
    // Check if response has a code property and validate it
    if (response.data && response.data.code && response.data.code !== 200) {
      throw new Error(response.data.message || 'Request failed');
    }
    
    return response.data;
  } catch (error: unknown) {
    if (error instanceof Error) {
      throw error; // If it's already an Error, throw it directly
    } else {
      // If it's another type (object, string, etc.), convert it to string appropriately
      throw new Error(String(error));
    }
  }
};

export default postRequest;