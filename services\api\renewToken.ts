import api from "./api";

// renomar token con auth/renew-token GET
export const renewToken = async (): Promise<string | undefined> => {
  try {
    const response = await api.post(`/auth/renew-token`);
    const statusCode = response.data.statusCode;

    if (statusCode !== 200) {
      throw new Error(response.data.message);
    }
    
    return response.data.data.token;
  } catch (error: any) {
    throw new Error(error.message);
  }
}