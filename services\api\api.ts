import axios from 'axios';
import BASE_URL from '../config';
import * as SecureStore from 'expo-secure-store';
import { Alert } from 'react-native';

// Axios instance with base configuration
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para añadir el token de autenticación a cada solicitud
api.interceptors.request.use(
  async (config) => {
    // Obtener el token desde SecureStore
    const token = await SecureStore.getItemAsync('userToken');
    
    // Si existe un token, añadirlo como Bearer token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    // si no existe un token, no añadir nada
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Variable para controlar si hay una alerta actualmente visible
let isAlertVisible = false;

// Interceptor para manejar errores globales
api.interceptors.response.use(
  response => response,
  error => {
    if ((error.response?.status === 401 || error.response?.status === 403 || 
         error.response?.statusCode === 401 || error.response?.statusCode === 403) && 
        !isAlertVisible) {
      
      // Marcar que hay una alerta visible
      isAlertVisible = true;
      
      Alert.alert(
        "Error",
        `No se pudo realizar la operación. Ocurrió un error, vuelve a intentarlo`,
        [{ 
          text: "OK",
          onPress: () => {
            // Cuando el usuario cierra la alerta, marcar que ya no hay alerta visible
            isAlertVisible = false;
          }
        }]
      );
    }

    return Promise.reject(error);
  }
);

export default api;