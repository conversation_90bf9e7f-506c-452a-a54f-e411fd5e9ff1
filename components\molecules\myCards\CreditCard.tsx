import type { Card } from '@/types/services/cards';
import { router } from 'expo-router';
import React, { useState } from 'react';
import useIsTablet from '@/hooks/useIsTablet';
import { CARD_TYPES } from '@/constants/typeCard';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Image } from 'expo-image';

type Props = {
  card: Card;
  disabled: boolean;
  onPress: (card: Card, onNavigate: (card: Card) => Promise<void> | void) => void;
};

const CreditCard = ({ card, disabled, onPress }: Props) => {
  const isTablet = useIsTablet();
  const [loadingImageCard, setLoadingImageCard] = useState(true);
  const urlImage =
    card.card_type === CARD_TYPES.PHYSICAL
      ? require('@/assets/images/blackcard.webp')
      : require('@/assets/images/goldcard.webp');

  const calculateHeightCardresponsive = () => {
    const screenWidth = Dimensions.get('window').width;
    const width = screenWidth / 2 - 48;
    const height = (width * 250) / 145;
    return height;
  };

  // Extrae la lógica de navegación para que sea llamada desde el padre
  const handleGoToCardDetails = async () => {
    if (!card || !card.id) return;
    await router.push({
      pathname: '/(homeStack)/myCards/[...card]',
      params: {
        card: [card.id],
        cardData: JSON.stringify(card)
      }
    });
  };

  return (
    <TouchableOpacity
      onPress={() => onPress(card, handleGoToCardDetails)}
      disabled={!card.id || loadingImageCard || disabled}
      style={{
        flex: 0.5,
        height: calculateHeightCardresponsive(),
        opacity: loadingImageCard ? 0 : 1,
      }}
    >
      <View style={styles.cardContainer}>
        <Image
          source={urlImage}
          style={styles.cardBackground}
          contentFit="fill"
          cachePolicy='memory-disk'
          onLoad={() => setLoadingImageCard(false)}
        />
        <View style={[styles.cardDetails, isTablet && { paddingLeft: 24 }]}>
          <Text style={[styles.text, isTablet && styles.textTablet]}>
            {card.card_type === CARD_TYPES.PHYSICAL ? 'Tarjeta Física' : 'Tarjeta Virtual'}
          </Text>
          <Text style={[styles.text, isTablet && styles.textTablet]}>
            ************{card.card_last_4}
          </Text>
          <Text style={[styles.text, isTablet && styles.textTablet]}>
            {card.format_expiration_date}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    width: '100%',
    height: '100%',
    flex: 1,
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
  },
  cardBackground: {
    ...StyleSheet.absoluteFillObject,
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
  cardDetails: {
    width: '100%',
    paddingLeft: 12,
    paddingBottom: 56,
    position: 'absolute',
    bottom: 0,
  },
  text: {
    color: 'white',
    fontSize: 10,
    marginBottom: 6,
    fontFamily: 'PublicSansMedium',
    lineHeight: 12.1,
    fontWeight: '500',
    textRendering: 'geometricPrecision',
    width: '100%',
  },
  textTablet: {
    fontSize: 24,
    lineHeight: 24,
  },
});

export default CreditCard;
