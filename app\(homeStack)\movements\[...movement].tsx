import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import { ThemedText } from '@/components/ui/ThemedText';
import { router, useLocalSearchParams, useNavigation } from 'expo-router';
import ShareGreen from '@/components/icons/ShareGreen';
import AlertRed from '@/components/icons/AlertRed';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  SafeAreaView,
  Platform,
  ScrollView,
} from 'react-native';
import ViewShot from 'react-native-view-shot';
import { useRef, useState, useEffect } from 'react';
import * as Sharing from 'expo-sharing';
import { useAccountContext } from '@/context/AccountContext';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import CustomAlert from '@/components/ui/CustomAlert';

const Movement = () => {
  const { accountData } = useAccountContext();
  const navigation = useNavigation();
  const params = useLocalSearchParams<{
    amount: string;
    date: string;
    description: string;
    id: string;
    movementType: string;
    operationDate: string;
    applicationDate: string;
    paymentType: string;
    beneficiaryName: string;
    beneficiaryAccount: string;
    bankName: string;
  }>();
  const viewShotRef = useRef<ViewShot>(null);
  const [isCapturing, setIsCapturing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showSharingAlert, setShowSharingAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      setIsNavigating(false);
    });

    return unsubscribe;
  }, [navigation]);

  const captureAndShare = async () => {
    try {
      setIsLoading(true);
      setIsCapturing(true);

      // Espera a que la vista se monte completamente
      await new Promise(resolve => setTimeout(resolve, 500));

      const uri = await viewShotRef.current?.capture?.();
      setIsCapturing(false);
      setIsLoading(false);

      if (!uri) {
        throw new Error('No se pudo capturar la vista.');
      }

      const isSharingAvailable = await Sharing.isAvailableAsync();
      if (isSharingAvailable) {
        await Sharing.shareAsync(uri);
      } else {
        setShowSharingAlert(true);
      }
    } catch (error) {
      setIsCapturing(false);
      setIsLoading(false);
      console.error('Error al capturar o compartir:', error);
      setShowErrorAlert(true);
    }
  };

  return (
    <>
      <PageBaseBWReturnButton
        topChildren={
          <View style={styles.topContainer}>
            <View style={styles.header}>
              <Text style={styles.title}>Movimientos</Text>
              <ThemedText
                style={[
                  styles.detailsHeader,
                  styles.boldText,
                  { marginBottom: 8 },
                ]}
              >
                Detalle
              </ThemedText>
              <Text style={[styles.detailsHeader, styles.boldText]}>
                {params?.date}
              </Text>
              <View style={styles.descriptionRow}>
                <Text style={[styles.detailsHeader, { color: '#F4F5FB' }]}>
                  {params?.description}
                </Text>
                <Text
                  style={[
                    styles.detailsHeader,
                    styles.boldText,
                    { color: '#F4F5FB' },
                  ]}
                >
                  {params?.amount}
                </Text>
              </View>
              <Text style={[styles.detailsHeader, { color: '#ACAFC2' }]}>
                {params?.id}
              </Text>
            </View>
          </View>
        }
        bottomChildren={
          <SafeAreaView style={styles.safe}>
            <View
              style={{
                flex: 1,
                gap: 8,
                paddingTop: 16,
                justifyContent: 'space-between',
              }}
            >
              <ScrollView
                style={{ flex: 1 }}
                contentContainerStyle={{ paddingBottom: 24 }}
                showsVerticalScrollIndicator={true}
              >
                <View style={[
                  styles.movementDetails,
                  styles.captureBackground,
                  isCapturing && { backgroundColor: '#fff' }
                ]}>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Cuenta de retiro</Text>
                    <Text style={styles.infoSection}>
                      {accountData?.clabe ? `**** **** **** ${accountData.clabe.slice(-4)}` : ''}
                    </Text>
                  </View>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Importe</Text>
                    <Text style={styles.infoSection}>{params?.amount}</Text>
                  </View>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Tipo de movimiento</Text>
                    <Text style={styles.infoSection}>{params?.paymentType}</Text>
                  </View>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Concepto</Text>
                    <Text style={styles.infoSection}>{params?.description}</Text>
                  </View>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Destinatario</Text>
                    <Text style={styles.infoSection}>{params?.beneficiaryName}</Text>
                  </View>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Clabe del destinatario</Text>
                    <Text style={styles.infoSection}>{params?.beneficiaryAccount}</Text>
                  </View>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Banco receptor</Text>
                    <Text style={styles.infoSection}>{params?.bankName}</Text>
                  </View>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Fecha de operación</Text>
                    <Text style={styles.infoSection}>{params.operationDate}</Text>
                  </View>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Fecha de aplicación</Text>
                    <Text style={styles.infoSection}>{params.applicationDate}</Text>
                  </View>
                  <View style={styles.item}>
                    <Text style={styles.nameSection}>Folio</Text>
                    <Text style={styles.infoSection}>{params.id}</Text>
                  </View>
                </View>
              </ScrollView>

              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={styles.whiteButton}
                  onPress={captureAndShare}
                >
                  <ShareGreen />
                  <Text style={styles.whiteButtonText}>
                    Compartir comprobante
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.whiteButton,
                    isNavigating && styles.disabledButton
                  ]}
                  onPress={() => {
                    if (!isNavigating) {
                      setIsNavigating(true);
                      router.push('/(homeStack)/movements/Contact');
                    }
                  }}
                  disabled={isNavigating}
                >
                  <AlertRed />
                  <Text style={[
                    styles.whiteButtonText,
                    isNavigating && styles.disabledButtonText
                  ]}>
                    No reconozco este movimiento
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </SafeAreaView>
        }
      />
      {isLoading && <LoadingScreen />}
      <CustomAlert
        visible={showSharingAlert}
        title="No disponible"
        message="La funcionalidad de compartir no está disponible en este dispositivo."
        onClose={() => setShowSharingAlert(false)}
        showCancelButton={false}
      />
      <CustomAlert
        visible={showErrorAlert}
        title="Error"
        message="Ocurrió un error al capturar o compartir la imagen."
        onClose={() => setShowErrorAlert(false)}
        showCancelButton={false}
      />
      {/* ViewShot oculto para capturar todos los datos del movimiento */}
      {isCapturing && (
        <View style={{ position: 'absolute', top: -9999, left: -9999 }}>
          <ViewShot
            ref={viewShotRef}
            options={{ format: 'png', quality: 1 }}
            style={{
              width: 360,
              padding: 24,
              backgroundColor: '#fff',
            }}
          >
            <View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Cuenta de retiro</Text>
                <Text style={styles.infoSection}>
                  {accountData?.clabe ? `**** **** **** ${accountData.clabe.slice(-4)}` : ''}
                </Text>
              </View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Importe</Text>
                <Text style={styles.infoSection}>{params?.amount}</Text>
              </View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Tipo de movimiento</Text>
                <Text style={styles.infoSection}>{params?.paymentType}</Text>
              </View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Concepto</Text>
                <Text style={styles.infoSection}>{params?.description}</Text>
              </View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Destinatario</Text>
                <Text style={styles.infoSection}>{params?.beneficiaryName}</Text>
              </View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Clabe del destinatario</Text>
                <Text style={styles.infoSection}>{params?.beneficiaryAccount}</Text>
              </View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Banco receptor</Text>
                <Text style={styles.infoSection}>{params?.bankName}</Text>
              </View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Fecha de operación</Text>
                <Text style={styles.infoSection}>{params.operationDate}</Text>
              </View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Fecha de aplicación</Text>
                <Text style={styles.infoSection}>{params.applicationDate}</Text>
              </View>
              <View style={styles.item}>
                <Text style={styles.nameSection}>Folio</Text>
                <Text style={styles.infoSection}>{params.id}</Text>
              </View>
            </View>
          </ViewShot>
        </View>
      )}
    </>
  );
};

export default Movement;

// Estilos para el componente
const styles = StyleSheet.create({
  topContainer: {
    backgroundColor: '#000',
  },
  safe: {
    flex: 1,
    backgroundColor: '#E6E8F4',
  },
  title: {
    fontSize: 24,
    color: '#F4F5FB',
    fontFamily: 'Inter',
    lineHeight: 29.05,
    textRendering: 'geometricPrecision',
    fontWeight: '400',
    marginBottom: 24,
    marginTop: 24,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 14,
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 1,
  },
  scrollContainer: {
    paddingBottom: 16,
    backgroundColor: '#E6E8F4',
  },
  movementDetails: {
    marginHorizontal: 24,
    flex: 1,
    justifyContent: 'space-between',
  },
  nameSection: {
    fontWeight: 'bold',
  },
  infoSection: {
    color: '#232429',
    fontSize: 14,
    fontFamily: 'Inter',
    fontWeight: '400',
    lineHeight: 19.36,
  },
  boldText: {
    fontWeight: 'bold',
    color: '#F4F5FB',
    // flex: 1,
    minHeight: 'auto',
  },
  descriptionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  whiteButton: {
    backgroundColor: '#E6E8F4',
    borderColor: '#000',
    borderWidth: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 26,
    height: 80,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '48%',
  },
  disabledButton: {
    opacity: 0.6,
  },
  whiteButtonText: {
    color: '#000',
    fontSize: 14,
    fontFamily: 'Inter',
    fontWeight: '400',
    lineHeight: 19.36,
    textAlign: 'center',
  },
  disabledButtonText: {
    color: '#666',
  },
  buttonIcon: {
    width: 24,
    height: 24,
    marginBottom: 8,
    color: '#000',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginHorizontal: 24,
    marginBottom: Platform.OS === 'ios' ? 0 : 12,
  },
  captureArea: {
    width: '100%',
    flex: 1,
    maxHeight: 480,
    minHeight: 400,
    backgroundColor: '#E6E8F4',
  },
  captureBackground: {
    backgroundColor: '#E6E8F4',
    borderRadius: 8,
  },
  item: {
    paddingVertical: 8,
  },
});
