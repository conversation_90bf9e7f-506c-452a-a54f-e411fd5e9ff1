import { StyleSheet, Text, View, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import React from 'react';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import PageBaseWhite from '@/components/templates/PageBaseWithe';
import { ThemedText } from '@/components/ui/ThemedText';
import GoldCheckMini from '../icons/GoldCheckMini';

type Props = {
  children: React.ReactNode;
  title: string;
  titlePrimaryButton: string;
  titleSecondaryButton: string;
  enableSaveBtn: boolean;
  description: string;
  conditions: string[];
  onPressPrimaryBtn: () => void;
  onPressSecondaryBtn: () => void;
};

const PageBaseWithConditions = ({
  enableSaveBtn,
  children,
  title,
  titlePrimaryButton,
  titleSecondaryButton,
  description,
  conditions,
  onPressPrimaryBtn,
  onPressSecondaryBtn,
}: Props) => {
  return (
    <PageBaseWhite>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[
          {
            flex: 1,
          },
        ]}
      >
        <ScrollView
          keyboardShouldPersistTaps='handled'
          contentContainerStyle={styles.scrollViewContent}
        >
          <View style={styles.wrapper}>
            <View style={styles.content}>
              <View style={styles.contentHeader}>
                <ThemedText type='title' lightColor='#232429'>
                  {title}
                </ThemedText>
                <ThemedText type='subtitle' lightColor='#4A4B55'>
                  {description}
                </ThemedText>
                <View style={styles.rememberContainer}>
                  {conditions.map((condition, index) => (
                    <View style={styles.rememberItem} key={index}>
                      <GoldCheckMini />
                      <Text style={styles.rememberText}>{condition}</Text>
                    </View>
                  ))}
                </View>
              </View>
              {children}
            </View>
            <View style={{ gap: 24 }}>
              <PrimaryButton
                title={titlePrimaryButton}
                disable={enableSaveBtn}
                onPress={onPressPrimaryBtn}
              />
              <SecondaryButton
                title={titleSecondaryButton}
                onPress={onPressSecondaryBtn}
                inverted
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </PageBaseWhite>
  );
};

export default PageBaseWithConditions;

// Estilos para el componente

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    justifyContent: 'space-between',
    // paddingBottom: 10,
    gap: 24,
  },
  contentHeader: {
    gap: 8,
  },
  rememberContainer: {
    gap: 8,
  },
  rememberItem: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  rememberText: {
    color: '#606572',
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
  content: {
    gap: 24,
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
    padding: 24,
    gap: 24,
  },
});
