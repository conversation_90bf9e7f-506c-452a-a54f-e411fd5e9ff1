import { StyleSheet, SafeAreaView } from 'react-native'
import React from 'react'
import { ThemedText } from '@/components/ui/ThemedText'
import { useBlockAndroidBack } from '@/hooks/useBlockAndroidBack'
import SecondaryButton from '@/components/atoms/SecondaryButton'
import { router } from 'expo-router'
import { useAccountContext } from '@/context/AccountContext'
import { useTransferContext } from '@/context/TransferContext'
import PendingTransferIcon from '@/components/icons/PendingTransferIcon'

const PendingTransfer = () => {
    useBlockAndroidBack()
    const { setTransferCalculate } = useAccountContext();
    const { clearTransferData } = useTransferContext();

    return (
        <SafeAreaView style={styles.safe}>
            <PendingTransferIcon width={150} height={150} />
            <ThemedText type='title' style={styles.title} lightColor='#ffffff'>La transferencia se encuentra en proceso.</ThemedText>
            <ThemedText type='subtitle' style={styles.subtitle} lightColor='#ffffff'>Aparecerá reflejada hasta que sea confirmada y procesada por el tercero.</ThemedText>
            <SecondaryButton
                title='Ir al inicio'
                onPress={() => {
                    setTransferCalculate(null);
                    clearTransferData();
                    router.dismissTo('/(homeStack)/transfers')
                }}

            />
        </SafeAreaView>
    )
}

export default PendingTransfer

const styles = StyleSheet.create({
    safe: {
        flex: 1,
        backgroundColor: '#000000',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 32,
    },
    title: {
        textAlign: 'center',
    },
    subtitle: {
        marginTop: 8,
        marginBottom: 40,
        textAlign: 'center',
    },
})