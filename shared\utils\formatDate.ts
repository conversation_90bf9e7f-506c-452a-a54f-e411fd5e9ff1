export const formatDate = (isoDate: string): string => {
  if (!isoDate) {
    return 'Fecha no disponible'; // Manejar valores nulos, indefinidos o vacíos
  }
  const date = new Date(isoDate); // Convertir la fecha ISO a un objeto Date

  // Verificar si la fecha es válida
  if (isNaN(date.getTime())) {
    return 'Fecha no válida'; // Manejar fechas inválidas
  }

  const day = String(date.getDate()).padStart(2, '0'); // Obtener el día
  const monthNames = [
    'enero',
    'febrero',
    'marzo',
    'abril',
    'mayo',
    'junio',
    'julio',
    'agosto',
    'septiembre',
    'octubre',
    'noviembre',
    'diciembre',
  ]; // Nombres de los meses
  const month = monthNames[date.getMonth()]; // Obtener el nombre del mes
  const year = date.getFullYear(); // Obtener el año
  return `${day} de ${month} del ${year}`; // Formatear la fecha como "26 de marzo de 2025"
};
