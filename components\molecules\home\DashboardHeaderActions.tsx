import { StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';
import { router } from 'expo-router';
import MenuIcon from '@/components/icons/MenuIcon';
import NotificationIcon from '@/components/icons/NotificationIcon';

// Componente funcional que representa las acciones del encabezado del dashboard
const DashboardHeaderActions = () => {
  const [disabled, setDisabled] = useState(false);

  const handlePress = async (route: string) => {
    if (disabled) return;
    setDisabled(true);
    router.push(route as any);
    // Opcional: re-habilitar después de un tiempo fijo si lo deseas
    setTimeout(() => setDisabled(false), 1000);
  };

  return (
    // Contenedor principal con estilo de navegación
    <View style={styles.nav}>
      {/* Botón de notificación */}
      <TouchableOpacity
        style={styles.iconNavContainer}
        onPress={() => handlePress('/(homeStack)/home/<USER>')}
        disabled={disabled}
      >
        <NotificationIcon />
      </TouchableOpacity>
      {/* Bot<PERSON> de menú */}
      <TouchableOpacity
        style={styles.iconNavContainer}
        onPress={() => handlePress('/(homeStack)/home/<USER>/Menu')}
        disabled={disabled}
      >
        <MenuIcon />
      </TouchableOpacity>
    </View>
  );
};

export default DashboardHeaderActions;

// Estilos para el componente
const styles = StyleSheet.create({
  // Estilo para el contenedor de navegación
  nav: {
    height: 48,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#000', // Fondo negro para el contenedor de navegación
  },
  // Estilo para el contenedor de los iconos de navegación
  iconNavContainer: {
    padding: 1,
  },
  // Estilo para el icono de notificación
  logoNotification: {
    width: 30,
    height: 30,
  },
});
