import { setFieldError } from "./setFieldError";

/**
 * Updates an error state for a specific field in a form
 * @param error Boolean indicating if there is an error
 * @param field Field name to update
 * @param message Error message (optional)
 * @param setErrors State setter function for the errors object
 */
export function setUpdateErrors<T>(
  error: boolean,
  field: keyof T,
  message: string | undefined,
  setErrors: React.Dispatch<React.SetStateAction<T>>
): void {
  setFieldError<T>(error, field, message, setErrors);
}