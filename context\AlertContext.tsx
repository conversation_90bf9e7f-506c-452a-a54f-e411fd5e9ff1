import React, { createContext, useContext, useState, ReactNode } from 'react';

interface AlertContextProps {
    show: boolean;
    message: string;
    showAlert: (message: string) => void;
    hideAlert: () => void;
}

const AlertContext = createContext<AlertContextProps | undefined>(undefined);

export const useAlert = () => {
    const context = useContext(AlertContext);
    if (!context) {
        throw new Error('useAlert must be used within an AlertProvider');
    }
    return context;
};

export const AlertProvider = ({ children }: { children: ReactNode }) => {
    const [show, setShow] = useState(false);
    const [message, setMessage] = useState('');

    const showAlert = (msg: string) => {
        setMessage(msg);
        setShow(true);
    };

    const hideAlert = () => {
        setShow(false);
        setMessage('');
    };

    return (
        <AlertContext.Provider value={{ show, message, showAlert, hideAlert }}>
            {children}
        </AlertContext.Provider>
    );
};
