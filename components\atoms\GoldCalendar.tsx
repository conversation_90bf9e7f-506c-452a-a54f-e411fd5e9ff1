import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  gradientColor,
  gradietColorLocations,
} from '@/constants/GoldGradientColor';

const GoldCalendar = ({
  onDateSelect,
}: {
  onDateSelect: (date1: Date | null, date2: Date | null) => void;
}) => {
  const [selectedDates, setSelectedDates] = useState<(Date | null)[]>([
    null,
    null,
  ]);
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());

  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const toggleDate = (date: Date) => {
    const isSelected = selectedDates.some(
      d => d?.toDateString() === date.toDateString(),
    );

    if (isSelected) {
      const newDates = selectedDates.map(d =>
        d?.toDateString() === date.toDateString() ? null : d,
      );
      setSelectedDates(newDates);
      onDateSelect(newDates[0], newDates[1]);
    } else if (selectedDates.filter(d => d !== null).length < 2) {
      const newDates = [...selectedDates];
      const emptyIndex = newDates.findIndex(d => d === null);
      newDates[emptyIndex] = date;
      setSelectedDates(newDates);
      onDateSelect(newDates[0], newDates[1]);
    }
  };

  const renderDay = (day: number) => {
    const date = new Date(new Date().getFullYear(), currentMonth, day);
    const isSelected = selectedDates.some(
      d => d?.toDateString() === date.toDateString(),
    );
    return (
      <TouchableOpacity
        key={day}
        style={styles.day}
        onPress={() => toggleDate(date)}
      >
        {isSelected && (
          <LinearGradient
            colors={gradientColor}
            locations={gradietColorLocations}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.gradient}
          />
        )}
        <Text style={isSelected ? styles.dayTextSelected : styles.dayText}>
          {day}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderCalendar = () => {
    const daysInMonth = getDaysInMonth(currentMonth, new Date().getFullYear());
    const firstDay = new Date(
      new Date().getFullYear(),
      currentMonth,
      1,
    ).getDay();

    const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
    const emptyDays = Array.from({ length: firstDay }, () => null);

    return [...emptyDays, ...days].map((day, index) =>
      day ? renderDay(day) : <View key={`empty-${index}`} style={styles.day} />,
    );
  };

  return (
    <View>
      <FlatList
        horizontal
        data={months}
        keyExtractor={item => item}
        renderItem={({ item, index }) => (
          <TouchableOpacity
            style={[
              styles.monthButton,
              index === currentMonth && styles.activeMonth,
            ]}
            onPress={() => setCurrentMonth(index)}
          >
            <Text
              style={
                index === currentMonth
                  ? styles.activeMonthText
                  : styles.monthText
              }
            >
              {item}
            </Text>
          </TouchableOpacity>
        )}
        removeClippedSubviews={false}
      />
      <View style={styles.daysOfWeekContainer}>
        {daysOfWeek.map(day => (
          <Text key={day} style={styles.dayOfWeekText}>
            {day}
          </Text>
        ))}
      </View>
      <View style={styles.daysContainer}>{renderCalendar()}</View>
    </View>
  );
};

export default GoldCalendar;

const styles = StyleSheet.create({
  monthButton: {
    padding: 8,
    marginHorizontal: 4,
    backgroundColor: '#E6E8F4',
    borderColor: '#000',
    borderWidth: 1,
    borderRadius: 20,
  },
  activeMonth: {
    backgroundColor: '#000',
  },
  monthText: {
    fontSize: 14,
    color: '#000',
  },
  activeMonthText: {
    fontSize: 14,
    color: '#fff',
  },
  daysOfWeekContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    paddingHorizontal: 8,
  },
  dayOfWeekText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ACAFC2',
    width: 40,
    textAlign: 'center',
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  day: {
    width: 44,
    height: 30,
    marginVertical: 8,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  dayText: {
    fontSize: 16,
    color: '#333',
  },
  dayTextSelected: {
    fontSize: 16,
    color: '#fff',
  },
  gradient: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1,
    borderRadius: 15,
  },
});
