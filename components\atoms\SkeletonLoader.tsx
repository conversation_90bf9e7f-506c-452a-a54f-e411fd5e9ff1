import { useEffect } from 'react';
import Animated, { useSharedValue, useAnimatedStyle, withRepeat, withSequence, withTiming } from 'react-native-reanimated';

type SkeletonLoaderProps = {
  width: number | string,
  height: number | string,
  style?: any
}

// Componente Skeleton para usar como loader
export default function SkeletonLoader({ width, height, style }: SkeletonLoaderProps) {
  const opacity = useSharedValue(0.3);

  useEffect(() => {
    // Secuencia de animación más simple y directa
    opacity.value = withRepeat(
      withSequence(
        withTiming(0.6, { duration: 500 }),
        withTiming(0.3, { duration: 500 })
      ),
      -1, // Repetir infinitamente
      false // No alternar
    );
  }, [opacity]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    backgroundColor: '#2e2e2e',
    width,
    height,
    borderRadius: 4,
    ...(style || {}),
  }));

  return <Animated.View style={animatedStyle} />;
};
