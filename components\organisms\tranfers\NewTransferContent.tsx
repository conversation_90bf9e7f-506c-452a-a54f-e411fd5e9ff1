import {
  StyleSheet,
  View,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import NewTransferForm from '@/components/organisms/tranfers/NewTransferForm';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import useKeyboardVisibility from '@/hooks/useKeyboardVisibility';
import { useUserContext } from '@/context/UserContext';
import { router } from 'expo-router';
import { saveContactsToTransfer } from '@/services/userService';
import { useBankContext } from '@/context/BankContext';
import { useTransferContext } from '@/context/TransferContext';

export type FormData = {
  name: string | null;
  number: string | null;
  bank: string | null;
  beneficiaryBank: string | null;
};

type NewTransferContentProps = {
  setLoading: (loading: boolean) => void;
};

const NewTransferContent = ({ setLoading }: NewTransferContentProps) => {
  const { fetchBanks } = useBankContext();
  const { setCurrentContact, fetchContacts } = useTransferContext();
  const { scrollViewRef } = useKeyboardVisibility();
  const { user } = useUserContext();
  const [saveContact, setSaveContact] = useState(false);
  const [dataFetched, setDataFetched] = useState(false);
  const [errorNumber, setErrorNumber] = useState<undefined | string>(undefined);
  const [errorName, setErrorName] = useState<string | undefined>(undefined);
  const [formData, setFormData] = useState<FormData>({
    name: null,
    number: null,
    bank: null,
    beneficiaryBank: null,
  });

  const isValidCardOrClabe = (text: string) => {
    const cardRegex = formData?.number?.startsWith('221') ? /^[0-9]{11}$/ : /^[0-9]{16}$/;; // Example for credit card (16 digits)
    const clabeRegex = /^[0-9]{18}$/; // Example for CLABE (18 digits)
    return cardRegex.test(text) || clabeRegex.test(text);
  };

  const getInformationContact = () => {
    if (!formData.name || !formData.number || !formData.bank) {
      return;
    }
    const payload = {
      userId: user?.id ?? '',
      name: formData.name,
      num_clabe: formData.number,
      bank_institution: formData.bank,
    };
    return payload
  }

  const handleContinueTransfer = async () => {
    const getContactInfo = getInformationContact();
    if (!getContactInfo) return;
    if (saveContact) {
      setLoading(true);
      await saveContactsToTransfer(getContactInfo);
      setLoading(false);
    }
    const contactList = await fetchContacts()
    const contact = contactList.find((contact) => contact.num_clabe === getContactInfo.num_clabe)
    setCurrentContact(contact!)
    router.push(
      `/(homeStack)/transfers/${formData.number}?name=${formData.name}&num_clabe=${formData.number}&bank=${formData.bank}&beneficiaryBank=${formData.beneficiaryBank}`,
    );
  }

  const disableButton =
    !!errorNumber ||
    !!errorName ||
    !formData.name ||
    !formData.number ||
    !formData.bank ||
    !isValidCardOrClabe(formData.number);

  useEffect(() => {
    if (dataFetched) {
      return;
    }
    const fetchData = async () => {
      try {
        setLoading(true);
        await fetchBanks();
        setLoading(false);
      } catch (error) {
        setLoading(false);
        console.error('Error fetching banks:', error);
      }
    };

    fetchData();
    setDataFetched(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#F4F5FB',
        padding: 24,
      }}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={[
          {
            flex: 1,
          },
        ]}
      >
        <ScrollView
          ref={scrollViewRef} // Referencia al ScrollView
          keyboardShouldPersistTaps='handled'
          contentContainerStyle={{
            flexGrow: 1,
            justifyContent: 'space-between',
          }}
        >
          <View style={styles.wrapper}>
            <NewTransferForm
              formData={formData}
              errorNumber={errorNumber}
              saveContact={saveContact}
              onSetFormData={setFormData}
              onSetSaveContact={setSaveContact}
              setErrorNumber={setErrorNumber}
              errorName={errorName}
              setErrorName={setErrorName}
            />
          </View>
          <PrimaryButton
            title='Siguiente'
            disable={disableButton}
            onPress={handleContinueTransfer}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default NewTransferContent;

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: '#F4F5FB',

    justifyContent: 'space-between',
    gap: 24,
  },
  container: {
    flexGrow: 1,
    // backgroundColor: 'red',
  },
  centerContent: {
    // backgroundColor: 'red',
    justifyContent: 'center',
  },
});