import React, { useRef, useEffect } from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  PanResponder,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

type Props = {
  children?: React.ReactNode;
  isVisible: boolean;
  onClose: () => void;
};

const BottomModal = ({ isVisible, children, onClose }: Props) => {
  // Animated values for slide and container animations
  const slideAnim = useRef(new Animated.Value(300)).current;
  const containerAnim = useRef(new Animated.Value(300)).current;

  useEffect(() => {
    // Trigger animations when isVisible changes
    if (isVisible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(containerAnim, {
          toValue: 0,
          duration: 50,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(containerAnim, {
          toValue: 300,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVisible]);

  // PanResponder to handle swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Start pan responder if user swipes down
        return gestureState.dy > 0;
      },
      onPanResponderMove: (_, gestureState) => {
        // Update slide animation value based on gesture
        if (gestureState.dy > 0) {
          slideAnim.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        // Close modal if swipe distance is greater than 100, otherwise reset
        if (gestureState.dy > 100) {
          Animated.timing(slideAnim, {
            toValue: 300,
            duration: 300,
            useNativeDriver: true,
          }).start(() => onClose());
        } else {
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }).start();
        }
      },
    }),
  ).current;

  return (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType='none'
      onRequestClose={onClose}
    >
      <Animated.View
        style={[styles.overlay, { transform: [{ translateY: containerAnim }] }]}
      >
        <TouchableOpacity style={styles.areaClose} onPress={onClose} />
        <Animated.View
          style={[
            styles.modalContainer,
            { transform: [{ translateY: slideAnim }] },
          ]}
          {...panResponder.panHandlers}
        >
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name='chevron-down' size={24} color='white' />
          </TouchableOpacity>
          {children}
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

export default BottomModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    borderRadius: 0,
  },
  areaClose: {
    flex: 1,
  },
  modalContainer: {
    backgroundColor: 'black',
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    alignItems: 'center',
    minHeight: 200,
  },
  closeButton: {
    marginTop: 24,
    alignSelf: 'center',
  },
  text: {
    color: 'white',
    fontSize: 18,
    marginTop: 40,
  },
});
