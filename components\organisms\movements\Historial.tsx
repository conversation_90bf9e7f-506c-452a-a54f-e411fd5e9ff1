import React, { useState, useRef } from 'react';
import { StyleSheet, Text, View, ScrollView, Alert } from 'react-native';
import { router } from 'expo-router';
import GoldCalendar from '@/components/atoms/GoldCalendar';
import GoldCheckBox from '@/components/atoms/GoldCheckBox';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import BottomModal from '../BottomModal';
import { useUserContext } from '@/context/UserContext';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import ViewShot from 'react-native-view-shot';
import * as Sharing from 'expo-sharing';
import { getLastMonthTransactions, getTransactionsByDateRange, generateStatementPDF, generateLastPeriodStatementPDF, convertTransactionsToMovements, calculateBalanceFromTransactions, Transaction } from '@/utils/generateStatementPDF';

// Función para formatear moneda
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

const Historial = () => {
  const [checked, setChecked] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [date1, setDate1] = useState<Date | null>(null);
  const [date2, setDate2] = useState<Date | null>(null);
  const [periodName, setPeriodName] = useState('');
  const [isCapturing, setIsCapturing] = useState(false);
  const viewShotRef = useRef<ViewShot>(null);

  const { user } = useUserContext();

  const handleDateSelect = (
    firstDate: Date | null,
    secondDate: Date | null,
  ) => {
    setDate1(firstDate);
    setDate2(secondDate);
  };

  const validateDates = (): boolean => {
    if (!checked) {
      if (!date1 || !date2) {
        Alert.alert(
          'Fechas requeridas',
          'Por favor selecciona ambas fechas para el período personalizado.',
          [{ text: 'Entendido', style: 'default' }]
        );
        return false;
      }

      if (date1 > date2) {
        Alert.alert(
          'Fechas inválidas',
          'La fecha de inicio debe ser anterior a la fecha final.',
          [{ text: 'Entendido', style: 'default' }]
        );
        return false;
      }

      const diffTime = Math.abs(date2.getTime() - date1.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays > 365) {
        Alert.alert(
          'Período muy extenso',
          'El período no puede ser mayor a 365 días. Por favor selecciona un rango menor.',
          [{ text: 'Entendido', style: 'default' }]
        );
        return false;
      }

      const today = new Date();
      today.setHours(23, 59, 59, 999);

      if (date2 > today) {
        Alert.alert(
          'Fecha futura',
          'No puedes seleccionar fechas futuras.',
          [{ text: 'Entendido', style: 'default' }]
        );
        return false;
      }
    }

    return true;
  };

  const getPeriodName = (): string => {
    if (checked) {
      const now = new Date();
      const monthName = now.toLocaleDateString('es-MX', {
        month: 'long',
        year: 'numeric'
      });
      return `Último período (${monthName})`;
    } else if (date1 && date2) {
      const startMonth = date1.toLocaleDateString('es-MX', {
        month: 'short',
        year: 'numeric'
      });
      const endMonth = date2.toLocaleDateString('es-MX', {
        month: 'short',
        year: 'numeric'
      });

      if (date1.getMonth() === date2.getMonth() &&
        date1.getFullYear() === date2.getFullYear()) {
        return startMonth;
      }

      return `${startMonth} - ${endMonth}`;
    }

    return 'Período personalizado';
  };

  const handleDownloadStatement = async () => {
    if (!user?.email) {
      Alert.alert(
        'Error de autenticación',
        'No se pudo obtener la información del usuario. Por favor inicia sesión nuevamente.',
        [
          {
            text: 'Ir a login',
            onPress: () => router.push('/auth/login'),
            style: 'default'
          }
        ]
      );
      return;
    }

    if (!validateDates()) {
      return;
    }

    try {
      setIsGeneratingPDF(true);
      const currentPeriodName = getPeriodName();
      setPeriodName(currentPeriodName);

      // Obtener las transacciones del período seleccionado
      let transactions: Transaction[] = [];
      if (checked) {
        transactions = await getLastMonthTransactions(user.email);
      } else if (date1 && date2) {
        transactions = await getTransactionsByDateRange(user.email, date1, date2);
      }

      if (!transactions || transactions.length === 0) {
        throw new Error('no_data');
      }

      // Convertir transacciones a movimientos y calcular balance
      const movements = convertTransactionsToMovements(transactions);
      const balanceInfo = calculateBalanceFromTransactions(transactions);

      const accountInfo = {
        name: user.fullName,
        email: user.email,
        phone: user.phone || '',
        accountBalance: formatCurrency(balanceInfo.saldoFinal),
        accountNumberConvenia: user.membership_number || '',
        accountNumberTransfer: user.membership_number || '',
        membershipNumber: user.membership_number || '',
        companyAlias: user.email
      };

      if (checked) {
        await generateLastPeriodStatementPDF(accountInfo, balanceInfo, movements);
      } else {
        await generateStatementPDF(accountInfo, balanceInfo, movements);
      }

      setShowModal(true);
    } catch (error: any) {
      console.error('Error al generar PDF:', error);

      let errorMessage = 'No se pudo generar el estado de cuenta. Inténtalo de nuevo.';
      let errorTitle = 'Error';

      if (error.message?.includes('No se encontraron') || error.message?.includes('no data')) {
        errorTitle = 'Sin movimientos';
        errorMessage = 'No se encontraron movimientos para el período seleccionado.';
      } else if (error.message?.includes('conexión') || error.message?.includes('network') || error.message?.includes('ECONNREFUSED')) {
        errorTitle = 'Error de conexión';
        errorMessage = 'Error de conexión con el servidor. Verifica tu internet e inténtalo de nuevo.';
      } else if (error.message?.includes('timeout')) {
        errorTitle = 'Tiempo agotado';
        errorMessage = 'La solicitud tardó demasiado. Inténtalo de nuevo.';
      } else if (error.message?.includes('401') || error.message?.includes('unauthorized')) {
        errorTitle = 'Sesión expirada';
        errorMessage = 'Tu sesión ha expirado. Por favor inicia sesión nuevamente.';
      } else if (error.message?.includes('403') || error.message?.includes('forbidden')) {
        errorTitle = 'Sin permisos';
        errorMessage = 'No tienes permisos para generar este estado de cuenta.';
      }

      Alert.alert(
        errorTitle,
        errorMessage,
        [{ text: 'Entendido', style: 'default' }]
      );
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const isButtonDisabled = (): boolean => {
    if (isGeneratingPDF) return true;
    if (!checked && (!date1 || !date2)) return true;
    return false;
  };

  const getButtonTitle = (): string => {
    if (isGeneratingPDF) return 'Generando PDF...';
    return 'Descargar estado de cuenta';
  };

  const handleModalClose = () => {
    setShowModal(false);
    if (!checked) {
      setDate1(null);
      setDate2(null);
    }
  };

  const captureAndShare = async () => {
    try {
      setIsCapturing(true);
      await new Promise(resolve => setTimeout(resolve, 500));

      const uri = await viewShotRef.current?.capture?.();
      setIsCapturing(false);

      if (!uri) {
        throw new Error('No se pudo capturar la vista.');
      }

      const isSharingAvailable = await Sharing.isAvailableAsync();
      if (isSharingAvailable) {
        await Sharing.shareAsync(uri);
      }
    } catch (error) {
      setIsCapturing(false);
      console.error('Error al capturar o compartir:', error);
    }
  };

  return (
    <>
      <ViewShot
        ref={viewShotRef}
        options={{ format: 'png', quality: 1 }}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.bottomContainer}>
            <View style={styles.allComponentsContainer}>
              <View>
                <Text style={styles.title}>Periodo de consulta</Text>
                <Text style={styles.subtitle}>
                  Selecciona el período para generar tu estado de cuenta
                </ Text>

                <View style={styles.optionsContainer}>
                  <View style={styles.checkboxContainer}>
                    <GoldCheckBox
                      checked={checked}
                      onPress={() => {
                        setChecked(!checked);
                        if (checked) {
                          setDate1(null);
                          setDate2(null);
                        }
                      }}
                    />
                    <View style={styles.checkboxTextContainer}>
                      <Text style={styles.checkboxText}>Último periodo</Text>
                      <Text style={styles.checkboxSubtext}>
                        Últimos 30 días de movimientos
                      </Text>
                    </View>
                  </View>

                  <View style={styles.checkboxContainer}>
                    <GoldCheckBox
                      checked={!checked}
                      onPress={() => {
                        setChecked(!checked);
                        if (!checked) {
                          setDate1(null);
                          setDate2(null);
                        }
                      }}
                    />
                    <View style={styles.checkboxTextContainer}>
                      <Text style={styles.checkboxText}>Consultar Manualmente</Text>
                      <Text style={styles.checkboxSubtext}>
                        Selecciona fechas específicas
                      </Text>
                    </View>
                  </View>
                </ View>

                {!checked && (
                  <View style={styles.calendarContainer}>
                    <Text style={styles.calendarTitle}>
                      Selecciona el rango de fechas
                    </Text>
                    <GoldCalendar onDateSelect={handleDateSelect} />
                    {date1 && date2 && (
                      <View style={styles.selectedDatesContainer}>
                        <Text style={styles.selectedDatesLabel}>
                          Período seleccionado:
                        </Text>
                        <Text style={styles.selectedDatesText}>
                          {date1.toLocaleDateString('es-MX')} - {date2.toLocaleDateString('es-MX')}
                        </Text>
                        <Text style={styles.selectedDatesSubtext}>
                          {Math.ceil(Math.abs(date2.getTime() - date1.getTime()) / (1000 * 60 * 60 * 24))} días
                        </Text>
                      </View>
                    )}
                  </View>
                )}
              </View>

              <View style={styles.buttonContainer}>
                <PrimaryButton
                  title={getButtonTitle()}
                  onPress={handleDownloadStatement}
                  disable={isButtonDisabled()}
                />
                <PrimaryButton
                  title="Compartir comprobante"
                  onPress={captureAndShare}
                  disable={isCapturing}
                />
                {isButtonDisabled() && !isGeneratingPDF && (
                  <Text style={styles.buttonHelpText}>
                    {!checked ? 'Selecciona ambas fechas para continuar' : ''}
                  </Text>
                )}
              </View>
            </View>

            <BottomModal isVisible={showModal} onClose={handleModalClose}>
              <View style={styles.modalBody}>
                <View style={styles.successIcon}>
                  <Text style={styles.successIconText}>✓</Text>
                </View>
                <Text style={styles.modalTitle}>Descarga completa</Text>
                <Text style={styles.modalText}>
                  {periodName} se ha descargado con éxito.
                </Text>
                <Text style={styles.modalSubtext}>
                  El archivo PDF ha sido guardado y está listo para compartir.
                </Text>
                <PrimaryButton
                  title={'Finalizar'}
                  onPress={() => {
                    handleModalClose();
                    router.push('/(homeStack)/home');
                  }}
                />
              </View>
            </BottomModal>
          </View>
        </ScrollView >
      </ViewShot >

      {isGeneratingPDF && (
        <LoadingScreen />
      )}
    </>
  );
};

export default Historial;

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    backgroundColor: '#E6E8F4',
  },
  bottomContainer: {
    flex: 1,
    backgroundColor: '#E6E8F4',
  },
  allComponentsContainer: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    paddingVertical: 24,
  },
  title: {
    fontSize: 24,
    color: '#000',
    marginBottom: 8,
    marginTop: 24,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    lineHeight: 22,
  },
  optionsContainer: {
    marginBottom: 20,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  checkboxTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  checkboxText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    marginBottom: 4,
  },
  checkboxSubtext: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  calendarContainer: {
    marginTop: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  calendarTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  selectedDatesContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#B8860B',
  },
  selectedDatesLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  selectedDatesText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
    marginBottom: 4,
  },
  selectedDatesSubtext: {
    fontSize: 12,
    color: '#B8860B',
    fontWeight: '500',
  },
  buttonContainer: {
    marginBottom: 24,
    width: '100%',
    gap: 12,
    marginTop: 24,
  },
  buttonHelpText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
  modalBody: {
    width: '100%',
    paddingVertical: 24,
    paddingHorizontal: 32,
    alignItems: 'center',
    gap: 16,
    marginBottom: 24,
  },
  successIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  successIconText: {
    fontSize: 30,
    color: 'white',
    fontWeight: 'bold',
  },
  modalTitle: {
    fontSize: 24,
    fontFamily: 'Inter',
    lineHeight: 29.05,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
  },
  modalText: {
    fontSize: 16,
    fontFamily: 'Inter',
    lineHeight: 20,
    fontWeight: '400',
    color: '#ffffff',
    textAlign: 'center',
  },
  modalSubtext: {
    fontSize: 14,
    fontFamily: 'Inter',
    lineHeight: 18,

    fontWeight: '400',
    color: '#cccccc',
    textAlign: 'center',
    marginBottom: 8,
  },
});




















