import api from "../api/api";

const getPinCard = async (cardId: string): Promise<string> => {
  try {
    const response = await api.get(`/dock-cards/consult-card-pin/${cardId}`);
    const data = response.data;
    const statusCode = data.statusCode;

    if (statusCode !== 200) {
      throw new Error(data.message);
    }
    const pin = response.data.pin;
    return pin;
} catch (error: unknown) {
    if (error instanceof Error) {
        throw error; // Si ya es un Error, lo lanzamos directamente
    } else {
        // Si es otro tipo (objeto, string, etc.), lo convertimos a string apropiadamente
        throw new Error(String(error));
    }
}
};

export default getPinCard;