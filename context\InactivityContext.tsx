/* eslint-disable react-hooks/exhaustive-deps */
import React, { createContext, useCallback, useContext, useEffect, useRef } from 'react';
import { AppState, AppStateStatus, Keyboard } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { useRouter } from 'expo-router';
import { useAlert as contextAlert } from './AlertContext';

type InactivityContextType = {
    resetInactivityTimer: () => void;
};

const InactivityContext = createContext<InactivityContextType>({
    resetInactivityTimer: () => { },
});

export const InactivityProvider = ({ children }: { children: React.ReactNode }) => {
    const INACTIVITY_TIMEOUT = 5 * 60 * 1000;
    const router = useRouter();
    const timeoutRef = useRef<number | null>(null);
    const appState = useRef(AppState.currentState);
    const backgroundTimeRef = useRef<number>(0);
    const { showAlert: alertGlobal } = contextAlert();

    const showAlertRef = useRef(false);
    const isDisplayedAlertRef = useRef(false);

    const handleExitApp = async () => {
        isDisplayedAlertRef.current = true;
        alertGlobal('Tu sesión se cerró por inactividad');
        // limpiamos los eventos de la app
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        setTimeout(() => {
            router.replace('/');
        }, 1000); // Esperamos un segundo para que se muestre el mensaje
        await SecureStore.deleteItemAsync('userToken');
        // redirigimos a la pantalla de login
    }

    const startOrResetInactivityTimer = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(async () => {
            if (showAlertRef.current && isDisplayedAlertRef.current) {
                return;
            }
            if (appState.current !== 'active') {
                showAlertRef.current = true;
                return;
            }
            showAlertRef.current = true;
            handleExitApp();
        }, INACTIVITY_TIMEOUT);
    }, []);

    useEffect(() => {
        startOrResetInactivityTimer(); // Start the timer on initial mount

        const appStateListener = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
            if (nextAppState === 'background' || nextAppState === 'inactive') {
                // App va a background o se inactiva
                backgroundTimeRef.current = Date.now();
            }

            if (nextAppState === 'active') {
                // App vuelve a primer plano
                if (backgroundTimeRef.current > 0) {
                    const timeInBackground = Date.now() - backgroundTimeRef.current;
                    if (timeInBackground >= INACTIVITY_TIMEOUT) {
                        // Si estuvo en background por más de INACTIVITY_TIMEOUT, cerramos sesión
                        if (!isDisplayedAlertRef.current) {
                            showAlertRef.current = true;
                            handleExitApp();
                        }
                    }
                }
            }

            appState.current = nextAppState;
        });

        const keyboardShow = Keyboard.addListener('keyboardDidShow', () => {
            startOrResetInactivityTimer();
        });
        const keyboardHide = Keyboard.addListener('keyboardDidHide', () => {
            startOrResetInactivityTimer();
        });

        return () => {
            appStateListener.remove();
            keyboardShow.remove();
            keyboardHide.remove();
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [startOrResetInactivityTimer]);

    return (
        <InactivityContext.Provider value={{ resetInactivityTimer: startOrResetInactivityTimer }}>
            {children}
        </InactivityContext.Provider>
    );
};

export const useInactivity = () => useContext(InactivityContext);
