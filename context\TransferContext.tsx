import { createContext, useContext, useState, ReactNode, Dispatch, SetStateAction, useCallback } from 'react';
import { useUserContext } from './UserContext';
import { useAccountContext } from './AccountContext';
import { detectBankName, BANK_TYPES } from '@/utils/detectBank';
import { Contact } from '@/app/(homeStack)/transfers';
import { getContactsToTransfer } from '@/services/userService';

type TransferData = {
  amount: string;
  concept: string;
  name: string;
  number: string;
  bank: string;
  beneficiaryBank?: string;
  commission?: string;
  reference?: string;
  folio_operation?: string;
  trackingKey?: string;
};

type TransferState = {
  rawAmount: string;
  formattedAmount: string;
  concept: string;
  errorAmount: string | null;
  errorConcept: string | null;
  isAmountValid: boolean;
  shouldShowCommission: boolean;
  currentContact?: Contact | null;
}

type TransferActions = {
  transferData: TransferData | null;
  setTransferData: Dispatch<SetStateAction<TransferData | null>>;
  clearTransferData: () => void;
  handleChangeAmount: (value: string) => void;
  handleSetConcept: (value: string) => void;
  setCurrentContact: Dispatch<SetStateAction<Contact | null>>;

  listContact: Contact[];
  isLoadingContacts: boolean;
  fetchContacts: (id?: string) => Promise<Contact[]>;

}

type TransferContextType = TransferState & TransferActions;

const TransferContext = createContext<TransferContextType | undefined>(undefined);

export const TransferProvider = ({ children }: { children: ReactNode }) => {
  const { user } = useUserContext();
  const { accountData, setTransferCalculate } = useAccountContext();

  const [transferData, setTransfer] = useState<TransferData | null>(null);
  const [rawAmount, setRawAmount] = useState('');
  const [formattedAmount, setFormattedAmount] = useState('0.00');
  const [concept, setConcept] = useState('');
  const [errorAmount, setErrorAmount] = useState<string | null>(null);
  const [errorConcept, setErrorConcept] = useState<string | null>(null);
  const [isAmountValid, setIsAmountValid] = useState(false);
  const [shouldShowCommission, setShouldShowCommission] = useState(false);
  const [currentContact, setCurrentContact] = useState<Contact | null>(null);
  const [listContact, setListContact] = useState<Contact[]>([]);
  const [isLoadingContacts, setLoadingContacts] = useState(false)

  const parseAmount = (raw: string): number => {
    return parseFloat(raw) / 100 || 0;
  };

  const handleChangeAmount = (value: string) => {
    const cleaned = value.replace(/[^0-9]/g, '');
    setRawAmount(cleaned);

    const amountNumber = parseAmount(cleaned);
    const formatted = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amountNumber);

    setFormattedAmount(formatted);
    const { type: bankType } = detectBankName(currentContact?.num_clabe ?? '');

    const appliesCommission = bankType === BANK_TYPES.UNKNOWN;
    let commission = user?.adminSettings?.spei_out ?? 0;
    setShouldShowCommission(appliesCommission);

    const isManager = user?.adminSettings?.isManager ?? false;
    if (appliesCommission && isManager) {
      commission = (amountNumber * (user?.adminSettings?.target_refound ?? 0)) / 100;
    }

    const total = amountNumber + commission;
    const canTransfer = total <= (accountData?.availableResource ?? 0);

    setTransferCalculate({
      amount: amountNumber,
      commission,
      total,
      isValid: canTransfer,
    });

    setTransfer({
      ...transferData,
      amount: cleaned,
      concept: '',
      name: '',
      number: '',
      bank: '',
      commission: commission.toFixed(2),
    });

    setIsAmountValid(amountNumber >= 0.01 && canTransfer);
    setErrorAmount(
      amountNumber < 0.01
        ? 'Ingresa un monto válido'
        : !canTransfer
          ? 'Saldo insuficiente para cubrir monto + comisión'
          : null
    );
  };

  const handleSetConcept = (value: string) => {
    setConcept(value);

    // No permitir la letra ñ o Ñ en cualquier parte
    if (/[ñÑ]/.test(value)) {
      setErrorConcept('No se permite la letra ñ');
      return;
    }

    // No permitir acentos
    const accentRegex = /[áéíóúÁÉÍÓÚüÜ]/;
    if (accentRegex.test(value)) {
      setErrorConcept('No se permiten acentos');
      return;
    }

    // Validar caracteres especiales o números
    const regex = /^[a-zA-Z\s]+$/;
    if (!regex.test(value)) {
      setErrorConcept('El concepto solo puede contener letras y espacios');
      return;
    }

    if (value.length < 3 || value.length > 30) {
      setErrorConcept('El concepto debe tener entre 3 y 30 caracteres');
      return;
    }

    setErrorConcept(null);
  };

  const fetchContacts = useCallback(async (id?: string) => {
    setLoadingContacts(true);
    try {
      const { data } = await getContactsToTransfer(id ?? user?.id);
      if (!data || !Array.isArray(data)) {
        setListContact([]);
        return [];
      }

      const sortedData = data.sort((a, b) =>
        (a.name || '').localeCompare(b.name || '', 'es', { sensitivity: 'base' })
      );
      setListContact(sortedData);
      return sortedData
    } finally {
      setLoadingContacts(false);
    }
  }, [user?.id]);

  const clearTransferData = () => {
    setTransfer(null);
    setRawAmount('');
    setFormattedAmount('0.00');
    setConcept('');
    setErrorAmount(null);
    setErrorConcept(null);
    setIsAmountValid(false);
    setTransferCalculate(null);
  };

  return (
    <TransferContext.Provider
      value={{
        isLoadingContacts,
        listContact,
        currentContact,
        setCurrentContact,
        fetchContacts,
        transferData,
        setTransferData: setTransfer,
        clearTransferData,
        rawAmount,
        formattedAmount,
        concept,
        errorAmount,
        errorConcept,
        isAmountValid,
        handleChangeAmount,
        handleSetConcept,
        shouldShowCommission,
      }}
    >
      {children}
    </TransferContext.Provider>
  );
};

export const useTransferContext = () => {
  const context = useContext(TransferContext);
  if (!context) {
    throw new Error('useTransferContext must be used within a TransferProvider');
  }
  return context;
};
