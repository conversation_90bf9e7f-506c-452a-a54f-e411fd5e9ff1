import React, { useEffect, useState } from 'react';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import NotificationsHeader from '@/components/molecules/home/<USER>';
import NotificationsBody from '@/components/organisms/home/<USER>';
import {
  getNotificationPreferences,
  updateNotificationPreferences
} from '@/services/notifications/notificationService';
import { useUserContext } from '@/context/UserContext';
import LoadingScreen from '@/components/molecules/LoadingScreen';

const Notifications = () => {
  const { user } = useUserContext();
  const [transactionToggle, setTransactionToggle] = useState(false);
  const [rememberToggle, setRememberToggle] = useState(false);
  const [loading, setLoading] = useState(true);

  // Función para obtener las preferencias de notificaciones
  const fetchPreferences = async () => {
    try {
      setLoading(true);
      const preferences = await getNotificationPreferences(user?.id!);
      setTransactionToggle(preferences.receive_transaction_notifications);
      setRememberToggle(preferences.receive_reminder_notifications);
    } catch (error) {
      console.error('Error fetching notification preferences:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleReminders = async () => {
    setRememberToggle(prev => !prev);
    try {
      await updateNotificationPreferences(
        user?.id!,
        {
          receive_reminder_notifications: !rememberToggle
        });
    } catch (error) {
      console.error('Error updating reminder notifications:', error);
    }
  };

  const handleToggleTransactions = async () => {
    setTransactionToggle(prev => !prev);
    try {
      await updateNotificationPreferences(
        user?.id!,
        {
          receive_transaction_notifications: !transactionToggle
        });
    } catch (error) {
      console.error('Error updating transaction notifications:', error);
    }
  };

  useEffect(() => {
    fetchPreferences(); // Obtener las preferencias al montar el componente
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading) {
    return <PageBaseBWReturnButton topChildren={<NotificationsHeader />} bottomChildren={<LoadingScreen />} />;
  }

  return (
    <PageBaseBWReturnButton
      topChildren={<NotificationsHeader />}
      bottomChildren={
        <NotificationsBody
          isEnabledReminders={rememberToggle}
          isEnabledTransactions={transactionToggle}
          onToggleReminders={handleToggleReminders}
          onToggleTransactions={handleToggleTransactions}
        />
      }
    />
  );
};

export default Notifications;
