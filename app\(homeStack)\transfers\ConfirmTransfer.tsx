import { Keyboard, Platform, Pressable, SafeAreaView, StatusBar, StyleSheet, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import Input from '@/components/atoms/Input';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import { ThemedText } from '@/components/ui/ThemedText';
import useIsTablet from '@/hooks/useIsTablet';
import { router } from 'expo-router';
import { TransferContact, transferDock, validatePasswordToTransfer } from '@/services/transfersService';
import { useUserContext } from '@/context/UserContext';
import { generateOTPCode } from '@/services/userService';
import { useInactivity } from '@/context/InactivityContext';
import { useAccountContext } from '@/context/AccountContext';
import { useTransferContext } from '@/context/TransferContext';
import { useBlockAndroidBack } from '@/hooks/useBlockAndroidBack';

const ConfirmTransfer = () => {
  useBlockAndroidBack();
  const { user } = useUserContext();
  const isTablet = useIsTablet();
  const { resetInactivityTimer } = useInactivity();
  const { setTransferCalculate } = useAccountContext();
  const { transferData, setTransferData, clearTransferData, currentContact } = useTransferContext();

  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [passwordError, setPasswordError] = useState({ error: false, message: null as string | null });

  const email = Array.isArray(user?.email) ? user.email[0] : user?.email;

  useEffect(() => {
    if (!transferData) {
      router.replace('/(homeStack)/transfers');
    }
  }, [transferData]);

  if (!transferData) {
    return null;
  }

  const {
    amount,
    concept,
    name,
    number,
    bank,
    beneficiaryBank
  } = transferData;

  const handleBigTransfer = async () => {
    const response = await generateOTPCode(email);
    setLoading(false);
    router.push({
      pathname: '/(homeStack)/transfers/VerifyEmailToTransfer',
      params: {
        number,
        email: user?.email,
        token: response.data.token,
        amount,
        concept,
        bank,
        name,
        beneficiaryBank,
      },
    });
  };

  const parseAmount = (amountStr: string): number => {
    const cleaned = amountStr.replace(/,/g, '');
    return parseFloat(cleaned);
  };

  const transferToAccount = async () => {
    const dataToTransfer: TransferContact = {
      num_clabe: number,
      amount: parseAmount(amount),
      description: concept,
      email: email || '',
      beneficiaryName: name,
    };
    if (!dataToTransfer.num_clabe.startsWith('527') || !dataToTransfer.num_clabe.startsWith('221')) {
      dataToTransfer.legalBank = beneficiaryBank;
      dataToTransfer.NameBank = bank;
    }
    const response = await transferDock(dataToTransfer);

    if (response.statusCode === 201) {
      return response;
    }

    if (response.statusCode === 102) {
      setLoading(false);
      router.replace('/(homeStack)/transfers/PendingTransfer');
      return;
    }

    setLoading(false);
    router.replace('/(homeStack)/transfers/TransferError');
    return;
  };

  const validatePassword = async () => {
    return await validatePasswordToTransfer(email, password);
  };

  const handleConfirm = async () => {
    setLoading(true);

    const isValid = await validatePassword();
    if (!isValid) {
      setLoading(false);
      setPasswordError({
        error: true,
        message: 'Contraseña incorrecta, intenta de nuevo',
      });
      return;
    }

    const amountNumber = parseAmount(amount);
    if (amountNumber >= 50000) {
      await handleBigTransfer();
      return;
    }

    const res = await transferToAccount();
    if (!res) {
      setLoading(false);
      return;
    }

    setTransferData({
      ...transferData,
      number: `****${number.slice(-4)}`,
      reference: res.data?.reference ?? '',
      folio_operation: res.data?.external_transaction_id ?? '',
      trackingKey: res.data?.transaction_id ?? '',
      commission: String(res.data?.commission),
    });

    setTransferCalculate(null);
    setPasswordError({ error: false, message: null });
    setLoading(false);

    router.replace({
      pathname: '/(homeStack)/transfers/TransferSuccess',
      params: {
        cep: res.data?.cep || null,
      }
    });
  };

  const handleChangePassword = (text: string) => {
    setPassword(text);
    setPasswordError({ error: false, message: null });
    resetInactivityTimer();
  };

  const handleCancel = () => {
    setTransferCalculate(null);
    clearTransferData();
    if (currentContact) {
      router.dismiss(2);
    } else {
      router.dismiss(3);
    }
  };

  return (
    <>
      <StatusBar backgroundColor="#ffffff" barStyle="dark-content" translucent={false} />
      <SafeAreaView style={styles.safeArea}>
        <Pressable onPress={() => Keyboard.dismiss()} style={styles.wrapper}>
          <View style={styles.content}>
            <ThemedText
              type="title"
              lightColor="#232429"
              style={{ width: isTablet ? '100%' : 342 }}
            >
              Escribe tu contraseña para confirmar el envío
            </ThemedText>
            <Input
              label="Contraseña"
              placeholder="Contraseña"
              secureTextEntry
              onChangeText={handleChangePassword}
              isError={passwordError.error}
              errorText={passwordError.message || ''}
            />
          </View>
          <View style={{ gap: 24, marginBottom: Platform.OS === 'ios' ? 0 : 24 }}>
            <PrimaryButton
              title="Realizar envío"
              disable={password === ''}
              onPress={handleConfirm}
            />
            <SecondaryButton title="Cancelar" inverted onPress={handleCancel} />
          </View>
        </Pressable>
      </SafeAreaView>
      {loading && <LoadingScreen />}
    </>
  );
};

export default ConfirmTransfer;

const styles = StyleSheet.create({
  safeArea: {
    justifyContent: 'space-between',
    flex: 1,
    backgroundColor: '#ffffff',
  },
  wrapper: {
    justifyContent: 'space-between',
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 24,
    paddingBottom: 0,
  },
  content: {
    gap: 24,
  },
});
