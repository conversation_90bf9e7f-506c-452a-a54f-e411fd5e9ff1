import { FlatList, StyleSheet, View, TextInput } from 'react-native';
import React, { useEffect, useState } from 'react';
import TransactionItem from '../home/<USER>';
import Feather from '@expo/vector-icons/Feather';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import { router } from 'expo-router';
import { useTransactionContext } from '@/context/TransactionContext';
import { Transaction } from '@/shared/types/transaction/Transaction';
import { useInactivity } from '@/context/InactivityContext';
import LoadingScreen from '../LoadingScreen';

// Componente principal que muestra la lista de transacciones
const TransactionListAll = ({ selectedFilter }: { selectedFilter: string }) => {
  const { transactions, fetchTransactions, isLoading } = useTransactionContext();
  const [searchText, setSearchText] = useState('');
  const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]); // Inicialmente muestra las primeras 5 transacciones
  const { resetInactivityTimer } = useInactivity();
  const [isNavigating, setIsNavigating] = useState(false);

  const handleHistoryPress = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.push('/(homeStack)/movements/HistorialMovements');
    // Resetear el estado después de un tiempo prudente
    setTimeout(() => setIsNavigating(false), 1000);
  };

  useEffect(() => {
    let filtered = transactions;

    // Aplica el filtro de búsqueda si hay texto
    if (searchText.trim() !== '') {
      filtered = filtered.filter(item =>
        item.description.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    setFilteredTransactions(filtered);
  }, [transactions, searchText, selectedFilter]);

  useEffect(() => {
    // Si hay filtro seleccionado, calcula el rango de fechas y llama a fetchTransactions
    if (selectedFilter) {
      const days = parseInt(selectedFilter.split(' ')[0], 10);
      const now = new Date();
      const startDate = new Date();
      startDate.setDate(now.getDate() - days);
      startDate.setHours(0, 0, 0, 0);

      fetchTransactions({
        initialDate: startDate.toISOString(),
        endDate: now.toISOString(),
        page: 0,
        limit: 150,
      });
    } else {
      // Si no hay filtro, trae los últimos 5 movimientos del mes
      fetchTransactions()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedFilter]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    // Contenedor principal de la lista
    <View style={styles.bottomContainer}>
      {/* Contenedor del input de búsqueda */}
      <View style={styles.searchContainer}>
        <Feather name='search' size={24} color='#6F7280' />
        <TextInput
          style={styles.searchInput}
          placeholder='Buscar por establecimiento'
          placeholderTextColor='#A6A6A6'
          value={searchText}
          onChangeText={(text) => {
            setSearchText(text);
            // Reinicia el temporizador de inactividad cada vez que se cambia el texto
            resetInactivityTimer();
          }}
        />
      </View>
      <View style={{ marginBottom: 24, flex: 1 }}>
        <FlatList
          style={{
            paddingHorizontal: 24,
          }}
          contentContainerStyle={{ gap: 16 }}
          data={filteredTransactions} // Mostrar solo las primeras 5 transacciones filtradas
          keyExtractor={item => item.id}
          renderItem={({ item }) => <TransactionItem item={item} />}
          removeClippedSubviews={false}
        />
        {filteredTransactions.length > 0 && (
          <View style={styles.historyButton}>
            <PrimaryButton
              title='Ver historial'
              onPress={handleHistoryPress}
              disable={isNavigating}
            />
          </View>
        )}
      </View>
    </View>
  );
};

export default TransactionListAll;

// Estilos para los componentes
const styles = StyleSheet.create({
  bottomContainer: {
    backgroundColor: '#E6E8F4',
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#C3C6D8',
    margin: 26,
    paddingHorizontal: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
    gap: 11,
    height: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#000000',
    fontFamily: 'Inter',
  },
  historyButton: {
    marginTop: 12,
    marginHorizontal: 24,
  },
});
