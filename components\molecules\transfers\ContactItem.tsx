import { StyleSheet, Text, View, Image, TouchableOpacity } from 'react-native';
import React, { useEffect, useState } from 'react';
import { Contact } from '@/app/(homeStack)/transfers';
import AntDesign from '@expo/vector-icons/AntDesign';
import { ThemedText } from '@/components/ui/ThemedText';
import { router } from 'expo-router';
import { useBankContext } from '@/context/BankContext';
import { useTransferContext } from '@/context/TransferContext';

// Definición del tipo de las propiedades que el componente recibirá
type Props = {
  item: Contact;
  onDelete: () => void;
  isEnabledToTransfer?: boolean;
};

// Componente funcional que representa un ítem de transacción
const ContactItem = ({ item, onDelete, isEnabledToTransfer }: Props) => {
  const { banks } = useBankContext();
  const { setCurrentContact } = useTransferContext();
  const [beneficiaryBank, setBeneficiaryBank] = useState<string>('');

  const detectBankByNumber = (number: string) => {
    if (number.startsWith('527') || number.startsWith('221')) return { name: 'CONVENIA' }; // Caso especial
    const code = number.slice(0, 3);

    const matchedBank = banks.find(bank => bank.legalCode === code);

    if (code.length >= 3) {
      if (matchedBank) {
        setBeneficiaryBank(matchedBank.code);
      } else {
        // Si no se encuentra un banco coincidente, retorna Banco no detectado
        setBeneficiaryBank('Banco no detectado');
      }
    } else {
      setBeneficiaryBank('');
    }
  };

  const handleTapTransfer = () => {
    setCurrentContact(item)
    if (isEnabledToTransfer) {
      router.push(
        `/(homeStack)/transfers/${item.num_clabe}?name=${item.name}&num_clabe=${item.num_clabe}&bank=${item.bank}&beneficiaryBank=${beneficiaryBank}&contact=true`,
      );
    }
  }

  // useEffect para detectar el banco al cargar el componente
  useEffect(() => {
    detectBankByNumber(item.num_clabe);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [item.num_clabe, banks]);

  return (
    <TouchableOpacity
      onPress={() => { handleTapTransfer() }}
    >
      <View style={styles.transactionItem}>
        <View style={styles.transactionDetails}>
          <Image
            source={item.icon} // Imagen de la transacción
            style={styles.transactionImage}
          />
          <View style={{ gap: 4 }}>
            <ThemedText type='defaultSemiBold' lightColor='#000000'>
              {item.name}
            </ThemedText>
            {/* Fecha de la transacción */}
            <Text style={styles.transactionDescription}>{item.bank}</Text>
            {/* Descripción de la transacción */}
            <Text style={styles.transactionDescription}>
              {/* {item.typeAccount} */}
              {item.typeAccount === 'card'
                ? `Número de tarjeta •••• ${item.last4Digits}`
                : `CLABE •••• ${item.last4Digits}`}
            </Text>
            {/* Clave única de la transacción */}
          </View>
        </View>
        <TouchableOpacity onPress={onDelete}>
          <AntDesign name='close' size={24} color='black' />
        </TouchableOpacity>
        {/* <Text style={styles.transactionAmount}>{item.amount}</Text> */}
        {/* Monto de la transacción */}
      </View>
    </TouchableOpacity>
  );
};

export default ContactItem;

// Estilos para el componente
const styles = StyleSheet.create({
  transactionItem: {
    flexDirection: 'row', // Elementos en fila
    justifyContent: 'space-between', // Espacio entre los elementos
    alignItems: 'center', // Alineación centrada verticalmente
    // backgroundColor: 'coral',
    // padding: 10, // Añadir padding para mejor separación
    height: 93, // Altura del ítem
  },
  transactionImage: {
    width: 40, // Ancho de la imagen
    height: 40, // Altura de la imagen
    borderRadius: 25, // Radio de borde para hacer la imagen circular
  },
  transactionDetails: {
    flex: 1, // Flexibilidad del contenedor
    flexDirection: 'row', // Elementos en fila
    alignItems: 'center', // Alineación centrada verticalmente
    gap: 8, // Espacio entre los elementos
  },
  transactionDescription: {
    fontSize: 14, // Tamaño de fuente
    color: '#6F7280', // Color del texto
    fontWeight: '400', // Grosor de la fuente
    fontFamily: 'Inter', // Familia de la fuente
    lineHeight: 16.94, // Altura de línea
  },
});
