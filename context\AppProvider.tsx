import React, { createContext, useContext, useState, useCallback } from 'react';
import { AppContextProps, AppProviderProps } from '../types/AppContextTypes';

// Creamos el contexto con un valor inicial undefined
const AppContext = createContext<AppContextProps | undefined>(undefined);

/**
 * App Provider Component
 * Provides global state and functions to the application
 */
const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  // Alert states
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [alertText, setAlertText] = useState(''); // Estado inicial del texto de la alerta

  // Estado inicial del cambio de NIP
  const [showChangeNipModal, setShowChangeNipModal] = useState(false);

  // Card states
  const [cardsLoading, setCardsLoading] = useState(false); // Estado inicial del registro de tarjeta

  // Helper methods
  const showAlert = useCallback((text: string) => {
    setAlertText(text);
    setShowSuccessAlert(true);
  }, []);

  const hideAlert = useCallback(() => {
    setShowSuccessAlert(false);
    setAlertText('');
  }, []);

  // Context value
  const contextValue: AppContextProps = {
    // Alert related
    showSuccessAlert,
    setShowSuccessAlert,
    alertText,
    setAlertText,

    // Card related
    cardsLoading,
    setCardsLoading,

    // Helper methods
    showAlert,
    hideAlert,

    // helper para mostrar modal de cambio de NIP
    setShowChangeNipModal,
    showChangeNipModal,
  };

  return (
    // Proveemos el contexto a los componentes hijos
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

/**
 * Custom hook to use the App Context
 * @returns The App Context properties
 * @throws Error if used outside of AppProvider
 */
export const useAppContext = (): AppContextProps => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider'); // Error si el hook es usado fuera del proveedor
  }
  return context;
};

export default AppProvider; // Exporta AppProvider como el valor por defecto
