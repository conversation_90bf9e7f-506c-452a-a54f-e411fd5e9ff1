import { SafeAreaView, StyleSheet, View } from 'react-native';
import React from 'react';

// Definición de los tipos de propiedades que el componente acepta
type Props = {
  children: React.ReactNode; // Elementos hijos que se renderizarán dentro del componente
};

// Definición del componente funcional PageBaseWithBackButton
const PageBaseWhite = ({ children }: Props) => {
  return (
    <SafeAreaView style={[styles.wrapper]}>
      <View style={styles.body}>{children}</View>
      {/* Renderiza los elementos hijos */}
    </SafeAreaView>
  );
};

export default PageBaseWhite;

// Definición de los estilos del componente
const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: '#FFFFFF',
    flex: 1,
  },
  body: {
    paddingVertical: 24,
    paddingBottom: 0,
    flex: 1,
  },
});
