import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { useBlockAndroidBack } from '@/hooks/useBlockAndroidBack'
import PrimaryButton from '@/components/atoms/PrimaryButton';
import AlertYellow from '@/components/icons/AlertYellow';
import { useRouter } from 'expo-router';
const InactiveScreen = () => {
    useBlockAndroidBack();
    const router = useRouter();
    const onClose = () => {
        router.dismissTo('/');
        // Aquí puedes manejar la lógica para cerrar la pantalla o modal
    }
    return (
        <View style={styles.body}>
            <View style={styles.rowContainer}>
                <View style={styles.textContent}>
                    <AlertYellow />
                    <Text style={styles.title}>
                        La cuenta esta pendiente por activarse
                    </Text>
                </View>
                <Text style={styles.text}>
                    Una vez activada tu cuenta, podrás disfrutar de todas las funciones
                    de la aplicación.
                </Text>
            </View>
            <PrimaryButton title='Aceptar' onPress={onClose} />
        </View>
    )
}

export default InactiveScreen

const styles = StyleSheet.create({
    body: {
        width: '100%',
        paddingVertical: 24,
        paddingHorizontal: 32,
        gap: 50,
        flex: 1,
        backgroundColor: '#000000',
        justifyContent: 'center',
    },
    rowContainer: {
        gap: 24,
    },
    textContent: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 11,
    },
    title: {
        fontSize: 24,
        fontFamily: 'Inter',
        lineHeight: 29.05,
        fontWeight: '400',
        color: '#ffffff',
    },
    text: {
        fontSize: 14,
        fontFamily: 'Inter',
        lineHeight: 16.94,
        fontWeight: '400',
        color: '#ffffff',
        paddingLeft: 21,
    },
});
