// componente que muestra un item de la lista de opciones de notificaciones
import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import CustomSwitch from '@/components/atoms/CustomSwitch';
import { ThemedText } from '@/components/ui/ThemedText';

type Props = {
  title: string;
  description: string;
  isEnabled: boolean;
  onToggle: () => void;
};

const NotificationsOptionItem = ({
  isEnabled,
  title,
  description,
  onToggle,
}: Props) => {
  return (
    <View style={styles.item}>
      <CustomSwitch
        isEnabled={isEnabled}
        value={isEnabled}
        toggleSwitch={onToggle}
      />
      <View style={styles.textContainer}>
        <ThemedText type='defaultSemiBold' lightColor='#000000'>
          {title}
        </ThemedText>
        <Text style={styles.description}>{description}</Text>
      </View>
    </View>
  );
};

export default NotificationsOptionItem;

const styles = StyleSheet.create({
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 24,
  },
  textContainer: {
    flex: 1,
    gap: 5,
  },
  description: {
    fontSize: 14,
    lineHeight: 16.94,
    fontWeight: '400',
    fontFamily: 'Inter',
    color: '#101828',
  },
});
