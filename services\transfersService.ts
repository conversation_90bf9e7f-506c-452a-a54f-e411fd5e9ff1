import api from './api/api';

export interface TransferContact {
  num_clabe: string;
  email: string;
  amount: number;
  description: string;
  beneficiaryName: string;
  legalBank?: string;
  NameBank?: string;
}

export interface Bank {
  code: string;
  name: string;
  legalCode: string;
  isActive: boolean;
}

interface TransferDockData {
  cep: string | null;
  commission: number; // o number, dependiendo de cómo lo quieras trabajar internamente
  external_transaction_id: string;
  reference: string;
  transaction_id: string;
}

interface TransferDockResponse {
  statusCode: number;
  message: string;
  data?: TransferDockData;
}

export const transferDock = async (
  data: TransferContact,
): Promise<TransferDockResponse> => {
  try {
    const response = await api.post(`/transfer/dock`, data);
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al realizar la transferencia:',
      error?.response?.data || error,
    );
    return {
      statusCode: error?.response?.status || 500,
      message:
        error?.response?.data?.message || 'Error al realizar la transferencia',
    };
  }
};

export const validatePasswordToTransfer = async (
  email: string,
  password: string,
) => {
  try {
    const response = await api.post(`/transfer/validatePasswordToTransfer`, {
      email,
      password,
    });
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al validar la contraseña:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message || 'Error al validar la contraseña',
    );
  }
};

export const deleteTransferContact = async (id: string) => {
  try {
    const response = await api.delete(`/contact/${id}`);
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al eliminar el contacto de transferencia:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message ||
        'Error al eliminar el contacto de transferencia',
    );
  }
};

export const getBanks = async () => {
  try {
    const response = await api.get(`/transfer-orders/get-banks`);
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al obtener los bancos:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message || 'Error al obtener los bancos',
    );
  }
};
