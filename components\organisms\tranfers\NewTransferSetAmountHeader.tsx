import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { ThemedText } from '@/components/ui/ThemedText';
import ChangeItem from '@/components/molecules/transfers/ChangeItem';

type Props = {
  destination: string;
  bankName: string;
  numberAccount: string;
  onBack: () => void;
};

const NewTransferSetAmountHeader = ({
  destination,
  bankName,
  numberAccount,
  onBack,
}: Props) => {
  return (
    <View style={styles.wrapper}>
      <ThemedText type='text24' lightColor='#F4F5FB'>
        Transferencias
      </ThemedText>
      <ChangeItem onBack={onBack}>
        <Text style={styles.label}>Destinatario</Text>
        <Text style={styles.text}>{destination}</Text>
        <Text style={styles.label}>Cuenta {bankName}</Text>
        <Text style={styles.text}>{numberAccount}</Text>
      </ChangeItem>
    </View>
  );
};

export default NewTransferSetAmountHeader;

const styles = StyleSheet.create({
  wrapper: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 13,
    gap: 24,
  },
  label: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 16.94,
    color: '#F4F5FB',
  },
  text: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 14.52,
    color: '#F4F5FB',
  },
});
