import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { Transaction } from '@/shared/types/transaction/Transaction';
import { getListTransactions } from '@/services/accountService';
import { useUserContext } from './UserContext';
import { formatCurrency } from '@/shared/utils/formatCurrency';
import { formatDate } from '@/shared/utils/formatDate';

interface TransactionContextType {
  transactions: Transaction[];
  fetchTransactions: (options?: {
    initialDate: string;
    endDate: string;
    page: number;
    limit: number;
  }) => Promise<void>;
  addTransaction: (transaction: Transaction) => void;
  isLoading: boolean
}

const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

export const TransactionProvider = ({ children }: { children: ReactNode }) => {
  const { user } = useUserContext();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false)

  const fetchTransactions = async (options?: {
    initialDate: string;
    endDate: string;
    page: number;
    limit: number;
  }) => {
    try {
      if (user?.enabled) {
        setIsLoading(true)
        const now = new Date();
        const oneMonthAgo = new Date(now);
        oneMonthAgo.setMonth(now.getMonth() - 1);

        const {
          initialDate = oneMonthAgo.toISOString(),
          endDate = now.toISOString(),
          page = 0,
          limit = 5,
        } = options || {};

        //const userData = await findUserByEmail(user.email);
        const data = await getListTransactions(user.email, {
          initialDate,
          endDate,
          page,
          limit,
        });
        const formattedData = data.map(transaction => {
          const formattedAmount =
            transaction.type === 'creditor'
              ? `+${formatCurrency(parseFloat(transaction.amount))}` // "+" si es "creditor"
              : `-${formatCurrency(parseFloat(transaction.amount))}`; // "-" si es "debtor"

          return {
            ...transaction,
            dateFormatted: formatDate(transaction.date), // Formatear la fecha
            operationDate: formatDate(transaction.operationDate), // Formatear la fecha de operación
            applicationDate: formatDate(transaction.applicationDate), // Formatear la fecha de aplicación
            amount: formattedAmount, // Usar el monto formateado con el signo correcto
          };
        });
        setTransactions(formattedData);
        setIsLoading(false)
      }

    } catch (err) {
      console.error('Error fetching transactions:', err);
    }
  };

  const addTransaction = (transaction: Transaction) => {
    const formattedAmount =
      transaction.type === 'creditor'
        ? `+${formatCurrency(parseFloat(transaction.amount))}`
        : `-${formatCurrency(parseFloat(transaction.amount))}`;

    const formattedTransaction = {
      ...transaction,
      dateFormatted: formatDate(transaction.date),
      amount: formattedAmount,
    };

    setTransactions(prev => [formattedTransaction, ...prev]);
  };

  useEffect(() => {
    if (user?.email) {
      fetchTransactions();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.email]);

  return (
    <TransactionContext.Provider value={{ transactions, fetchTransactions, addTransaction, isLoading }}>
      {children}
    </TransactionContext.Provider>
  );
};

export const useTransactionContext = () => {
  const context = useContext(TransactionContext);
  if (!context) {
    throw new Error('useTransactionContext must be used within a TransactionProvider');
  }
  return context;
};
