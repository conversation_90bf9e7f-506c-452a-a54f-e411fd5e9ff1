import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import Historial from '@/components/organisms/movements/Historial';
import { StyleSheet, Text, View } from 'react-native';
import { ThemedText } from '@/components/ui/ThemedText';

const HistorialMovements = () => {
  return (
    <PageBaseBWReturnButton
      topChildren={
        <View style={styles.topContainer}>
          <View style={styles.header}>
            <Text style={styles.title}>Historial</Text>
            <ThemedText type={'caption'} lightColor='#F4F5FB'>
              Descarga tu estado de cuenta fácilmente
            </ThemedText>
          </View>
        </View>
      }
      bottomChildren={<Historial />}
    />
  );
};

export default HistorialMovements;

const styles = StyleSheet.create({
  topContainer: {
    backgroundColor: '#000',
  },
  title: {
    fontSize: 24,
    color: '#F4F5FB',
    fontFamily: 'Inter',
    lineHeight: 29.05,
    textRendering: 'geometricPrecision',
    fontWeight: '400',
    marginBottom: 24,
    marginTop: 24,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
});
