import React from 'react';
import AlertSucess from '@/components/atoms/AlertSucess';

type Props = {
  isVisible: boolean;
  onSetShowAlert: (value: boolean) => void;
};
const AlertSuccessChangeNip = ({ isVisible, onSetShowAlert }: Props) => {
  return (
    <AlertSucess
      text='EL NIP se ha cambiado correctamente
recuerda acudir a un cajero para confirmar el cambio haciendo una consulta de saldo'
      timeToHide={2000}
      isVisible={isVisible}
      onSetShowAlert={onSetShowAlert}
    />
  );
};

export default AlertSuccessChangeNip;
