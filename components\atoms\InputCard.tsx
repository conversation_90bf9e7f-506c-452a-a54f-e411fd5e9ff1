import React from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TextInputProps,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Definición de las propiedades personalizadas para el componente Input
interface CustomInputProps extends TextInputProps {
  label: string; // Etiqueta del campo de entrada
  errorText?: string; // Texto de error opcional
  isError: boolean; // Indica si hay un error
  icon?: keyof typeof Ionicons.glyphMap; // Icono opcional
  lightLabel?: boolean; // Indica si la etiqueta debe ser clara
  typeCard: string;
  onPressIcon?: () => void; // Función opcional para manejar el evento de presionar el icono
  onChangeText: (text: string) => void; // Función para manejar el cambio de texto
}

// Componente funcional Input
const InputCard: React.FC<CustomInputProps> = ({
  label,
  lightLabel,
  errorText,
  icon,
  isError,
  style,
  typeCard,
  onPressIcon,
  onChangeText,
  ...props
}) => {
  return (
    <View style={styles.container}>
      {/* Etiqueta del campo de entrada */}
      <Text
        style={[
          styles.label,
          {
            color: lightLabel ? '#FFFFFF' : '#000000', // Color de la etiqueta basado en lightLabel
          },
        ]}
      >
        {label}
      </Text>
      {/* Contenedor del campo de entrada y el icono */}
      <View style={[styles.inputWrapper, isError && styles.inputWrapperError]}>
        <View style={styles.cardsContainer}>
          {typeCard !== 'mastercard' && (
            <Ionicons name='card-outline' size={27} />
          )}
          {typeCard === 'mastercard' && (
            <Image
              source={require('@/assets/images/mastercard.png')}
              style={{ width: 34, height: 24 }}
            />
          )}
        </View>
        <TextInput
          style={[styles.input, isError && styles.inputError, style]} // Estilo del campo de entrada
          onChangeText={onChangeText} // Maneja el cambio de texto
          {...props} // Otras propiedades del TextInput
          placeholderTextColor='#9093A5'
          maxLength={16}
        />
        {/* Icono opcional */}
        {(isError || icon) && (
          <Ionicons
            name={icon || 'alert-circle-outline'} // Nombre del icono basado en icon
            size={16}
            color={isError ? '#F04438' : 'black'} // Color del icono basado en isError
            style={styles.icon}
            onPress={onPressIcon} // Maneja el evento de presionar el icono
          />
        )}
      </View>
      {/* Texto de error opcional */}
      {isError && <Text style={styles.errorText}>{errorText}</Text>}
    </View>
  );
};

// Estilos del componente
const styles = StyleSheet.create({
  container: {
    width: '100%',
    gap: 6,
  },
  label: {
    fontFamily: 'Inter',
    fontStyle: 'normal',
    fontWeight: '600',
    fontSize: 14,
    lineHeight: 17,
    color: '#000000',
    alignSelf: 'stretch',
    flexGrow: 0,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    boxSizing: 'border-box',
    paddingHorizontal: 12,
    gap: 8,
    width: '100%',
    height: 48,
    backgroundColor: '#FFFFFF',
    borderColor: '#C3C6D8',
    borderWidth: 1,
    shadowColor: 'rgba(16, 24, 40, 0.05)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 1,
    shadowRadius: 2,
    borderRadius: 10,
  },
  inputWrapperError: {
    borderColor: 'red', // Color del borde en caso de error
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 19,
    height: '100%',
    fontFamily: 'Inter',
    color: '#000000',
  },
  inputError: {
    borderColor: '#F04438', // Color del borde del campo de entrada en caso de error
  },
  icon: {
    marginLeft: 8,
  },
  errorText: {
    color: '#D92D20', // Color del texto de error
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 16.94,
    fontFamily: 'Roboto',
  },
  cardsContainer: {
    width: 34,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default InputCard;
