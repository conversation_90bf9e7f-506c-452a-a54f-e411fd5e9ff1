import * as React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';
const CopyWhite = (props: SvgProps) => (
  <Svg width={20} height={20} fill='none' {...props}>
    <Path
      stroke='#F4F5FB'
      strokeWidth={1.5}
      d='M5 9.167c0-2.357 0-3.536.732-4.268.732-.732 1.911-.732 4.268-.732h2.5c2.357 0 3.536 0 4.267.732.733.732.733 1.911.733 4.268v4.166c0 2.357 0 3.536-.733 4.268-.731.732-1.91.732-4.267.732H10c-2.357 0-3.536 0-4.268-.732C5 16.869 5 15.69 5 13.333V9.167Z'
    />
    <Path
      stroke='#F4F5FB'
      strokeWidth={1.5}
      d='M5 15.833a2.5 2.5 0 0 1-2.5-2.5v-5c0-3.142 0-4.714.977-5.69.976-.975 2.547-.976 5.69-.976H12.5a2.5 2.5 0 0 1 2.5 2.5'
      opacity={0.5}
    />
  </Svg>
);
export default CopyWhite;
