import { View, StyleSheet, Image } from 'react-native';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import { ThemedText } from '@/components/ui/ThemedText';
import { router } from 'expo-router';
import HandsTwiceWhite from '@/components/icons/HandsTwiceWhite';

const SuccessClarifications = () => {
    return (
        <View style={styles.container}>
            <HandsTwiceWhite />
            <View style={styles.content}>
                <Image
                    source={require('@/assets/images/checkSuccessPassword.png')}
                    style={{ width: 60, height: 60 }}
                />
                <ThemedText type='title' lightColor='#F4F5FB' style={styles.title}>
                    Tu aclaración fue enviada con éxito.
                </ThemedText>
                <ThemedText type='default' lightColor='#F4F5FB' style={styles.description}>
                    Estamos revisando tu caso, a la brevedad responderemos tu aclaración
                </ThemedText>
            </View>
            <SecondaryButton
                title='Ir al inicio'
                onPress={() => router.dismissAll()}
            />
        </View>
    );
};
export default SuccessClarifications;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#000000',
        gap: 40,
        paddingHorizontal: 24,
    },
    content: {
        gap: 8,
        alignItems: 'center',
    },
    title: {
        textAlign: 'center',
    },
    description: {
        textAlign: 'center',
    },
});