import React, { useEffect, useRef, useState } from 'react';
import {
  StyleSheet,
  View,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import Input from '../../atoms/Input';
import LoadingScreen from '../../molecules/LoadingScreen';
import PageBaseWithBackButton from '../../templates/PageBaseWithBackButton';
import PrimaryButton from '../../atoms/PrimaryButton';
import { ThemedText } from '../../ui/ThemedText';
import InputCard from '@/components/atoms/InputCard';
import type { ErrorProps } from '@/types';
import useIsTablet from '@/hooks/useIsTablet';
import { useInactivity } from '@/context/InactivityContext';

type formData = {
  cardNumber: string;
  expirationDate: string;
};

type Props = {
  loading?: boolean;
  data: formData;
  cardNumberError: ErrorProps;
  expirationDateError: ErrorProps;
  cardType: string;
  disable: boolean;
  onPress: () => void;
  handleInputChangeCardNumber: (text: string) => void;
  handleInputChangeExpirationDate: (text: string) => void;
};

const AddCard = ({
  loading,
  data,
  cardNumberError,
  expirationDateError,
  cardType,
  disable,
  onPress,
  handleInputChangeCardNumber,
  handleInputChangeExpirationDate,
}: Props) => {
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const scrollViewRef = useRef<ScrollView | null>(null);
  const isTablet = useIsTablet();
  const { resetInactivityTimer } = useInactivity();

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
        scrollViewRef.current?.scrollToEnd({ animated: true });
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false),
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return (
    <>
      <PageBaseWithBackButton>
        {/* <Pressable onPress={Keyboard.dismiss}> */}
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{ height: '100%' }}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20} // Ajustar este valor según sea necesario
        >
          <ScrollView
            ref={scrollViewRef} // Referencia al ScrollView
            contentContainerStyle={[
              styles.container,
              !isKeyboardVisible && styles.centerContent, // Centra el contenido si el teclado no está visible
            ]}
            scrollEnabled={isKeyboardVisible} // Activa el scroll solo si el teclado está visible
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps='handled'
          >
            <View style={styles.wrapper}>
              <View style={styles.content}>
                <View style={styles.contentHeader}>
                  <ThemedText type='title' lightColor='#232429'>
                    Agregar Tarjeta
                  </ThemedText>
                  <ThemedText type='subtitle' lightColor='#4A4B55'>
                    Te pediremos los siguientes datos para agregar tu nueva
                    tarjeta
                  </ThemedText>
                </View>
                <Image
                  source={require('@/assets/images/DetailCard-b.png')}
                  style={{
                    width: isTablet ? 300 : 250,
                    height: isTablet ? 200 : 150,
                    marginHorizontal: 'auto',
                    objectFit: 'contain',
                  }}
                />
                <View style={{ gap: 16 }}>
                  <InputCard
                    label='Ingresa los 16 dígitos de tu tarjeta'
                    placeholder='16 dígitos de tu tarjeta'
                    keyboardType='numeric'
                    value={data.cardNumber}
                    isError={cardNumberError.error}
                    typeCard={cardType}
                    returnKeyType='done'
                    onSubmitEditing={Keyboard.dismiss}
                    onChangeText={text => {
                      resetInactivityTimer()
                      handleInputChangeCardNumber(text);
                    }}
                    errorText={cardNumberError.message}
                  />
                  <Input
                    label='Fecha de expiración'
                    placeholder='MM/AA'
                    keyboardType='numeric'
                    returnKeyType='done'
                    value={data.expirationDate}
                    isError={expirationDateError.error}
                    onChangeText={text => {
                      handleInputChangeExpirationDate(text)
                      resetInactivityTimer(); // Reiniciar el temporizador de inactividad
                    }}
                    errorText={expirationDateError.message}
                  />
                </View>
              </View>
              <PrimaryButton
                title='Agregar'
                disable={disable}
                onPress={onPress}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
        {/* </Pressable> */}
      </PageBaseWithBackButton>
      {loading && <LoadingScreen />}
    </>
  );
};

export default AddCard;

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    justifyContent: 'space-between',
    paddingBottom: 10,
    minHeight: '100%',
    gap: 50,
  },
  contentHeader: {
    gap: 8,
  },
  content: {
    gap: 24,
    paddingBottom: 24,
  },
  container: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  centerContent: {
    justifyContent: 'center',
  },
});
