import React from 'react';
import { router } from 'expo-router';
import ErrorTransferLayer from '@/components/organisms/tranfers/ErrorTransferLayer';
import { useAccountContext } from '@/context/AccountContext';
import { useBlockAndroidBack } from '@/hooks/useBlockAndroidBack';

const TransferSuccess = () => {
  // bloquea el boton de regresar del celular
  useBlockAndroidBack()

  const { setTransferCalculate } = useAccountContext();

  return (
    <ErrorTransferLayer
      onFinished={() => {
        // limpiamos el monto original - comision, del contexto
        setTransferCalculate(null);
        router.dismissTo('/(homeStack)/transfers');
      }}
    />
  );
};

export default TransferSuccess;
