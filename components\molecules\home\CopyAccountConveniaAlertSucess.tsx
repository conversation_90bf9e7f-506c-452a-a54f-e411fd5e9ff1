import React from 'react';
import AlertSucess from '@/components/atoms/AlertSucess';

type Props = {
  showCopyCLABEAlert: boolean;
  setShowCopyAlert: (value: boolean) => void;
};
const CopyAccountConveniaAlertSucess = ({
  showCopyCLABEAlert,
  setShowCopyAlert,
}: Props) => {
  return (
    <AlertSucess
      isVisible={showCopyCLABEAlert}
      text='El número de cuenta ha sido copiado al portapapeles.'
      timeToHide={2000}
      onSetShowAlert={() => setShowCopyAlert(false)}
    />
  );
};

export default CopyAccountConveniaAlertSucess;
