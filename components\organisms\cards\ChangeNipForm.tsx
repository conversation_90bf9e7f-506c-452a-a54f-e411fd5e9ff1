import { StyleSheet, Text, View, Keyboard, Pressable } from 'react-native';
import React, { useState } from 'react';
import Input from '@/components/atoms/Input';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import PageBaseWhite from '@/components/templates/PageBaseWithe';
import { ThemedText } from '@/components/ui/ThemedText';
import { router } from 'expo-router';
import GoldCheckMini from '@/components/icons/GoldCheckMini';

type Props = {
  error: string | null;
  enableSaveBtn: boolean;
  nip: string;
  confirmNip: string;
  onSetNip: (nip: string) => void;
  onSetConfirmNip: (confirmNip: string) => void;
  onSaved: () => void;
};
const ChangeNipForm = ({
  error,
  confirmNip,
  nip,
  enableSaveBtn,
  onSetNip,
  onSetConfirmNip,
  onSaved,
}: Props) => {
  const [showNip, setShowNip] = useState(false);
  const [showConfirmNip, setShowConfirmNip] = useState(false);

  return (
    <PageBaseWhite>
      <Pressable onPress={() => Keyboard.dismiss()} style={styles.wrapper}>
        <View style={styles.content}>
          <View style={styles.contentHeader}>
            <ThemedText type='title' lightColor='#232429'>
              Recomendaciones para tu nuevo NIP
            </ThemedText>
            <ThemedText type='subtitle' lightColor='#4A4B55'>
              Es importante que sea fácil de recordar, pero también, seguro, por
              eso:
            </ThemedText>
            <View style={styles.rememberContainer}>
              <View style={styles.rememberItem}>
                <GoldCheckMini />
                <Text style={styles.rememberText}>
                  No uses números consecutivos. Ej: 1234
                </Text>
              </View>
              <View style={styles.rememberItem}>
                <GoldCheckMini />
                <Text style={styles.rememberText}>
                  Ni números idénticos seguidos. Ej 1118
                </Text>
              </View>
            </View>
          </View>
          <View style={{ gap: 16 }}>
            <Input
              label='Nuevo NIP'
              value={nip}
              placeholder='Ingresa tu nuevo NIP'
              secureTextEntry={!showNip}
              isError={!!error}
              keyboardType='number-pad'
              returnKeyType='done'
              icon={
                !!error
                  ? 'alert-circle-outline'
                  : showNip
                    ? 'eye-outline'
                    : 'eye-off-outline'
              }
              maxLength={4}
              onPressIcon={() => setShowNip(!showNip)}
              onChangeText={onSetNip}
            />
            <Input
              label='Confirmar nuevo NIP'
              placeholder='Confirma tu nuevo NIP'
              value={confirmNip}
              secureTextEntry={!showConfirmNip}
              keyboardType='number-pad'
              returnKeyType='done'
              isError={!!error}
              icon={
                !!error
                  ? 'alert-circle-outline'
                  : showConfirmNip
                    ? 'eye-outline'
                    : 'eye-off-outline'
              }
              maxLength={4}
              onPressIcon={() => setShowConfirmNip(!showConfirmNip)}
              onChangeText={onSetConfirmNip}
              errorText={error ? error : 'Tu NIP no coincide'}
            />
          </View>
        </View>
        <View style={{ gap: 24 }}>
          <PrimaryButton
            title='Guardar NIP'
            disable={enableSaveBtn}
            onPress={onSaved}
          />
          <SecondaryButton
            title='Cancelar'
            onPress={() => router.back()}
            inverted
          />
        </View>
      </Pressable>
    </PageBaseWhite>
  );
};

export default ChangeNipForm;

// Estilos para el componente

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    justifyContent: 'space-between',
    paddingBottom: 10,
    paddingHorizontal: 24,
    gap: 50,
  },
  contentHeader: {
    gap: 8,
  },
  rememberContainer: {
    gap: 8,
  },
  rememberItem: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  rememberText: {
    color: '#606572',
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
  content: {
    gap: 24,
  },
});
