import { StyleSheet, Text, View, TouchableWithoutFeedback, Dimensions } from 'react-native'
import React from 'react'
import { Ionicons } from '@expo/vector-icons'

interface IconTooltipProps {
    isError?: boolean;
    text: string;
}

const IconTooltip = ({ isError = false, text }: IconTooltipProps) => {
    const [showTooltip, setShowTooltip] = React.useState(false)

    return (
        <>
            <View style={styles.container}>
                <Ionicons
                    name={'alert-circle-outline'}
                    size={16}
                    color={isError ? '#F04438' : 'black'}
                    onPress={() => setShowTooltip(!showTooltip)}
                />
                {showTooltip && (
                    <View style={styles.tooltipContainer}>
                        <View style={styles.triangle} />
                        <View style={styles.tooltip}>
                            <Text style={styles.tooltipText}>{text}</Text>
                        </View>
                    </View>
                )}
            </View>
            {showTooltip && (
                <TouchableWithoutFeedback onPress={() => setShowTooltip(false)}>
                    <View style={styles.overlay} />
                </TouchableWithoutFeedback>
            )}
        </>
    )
}

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        marginRight: 12,
    },
    tooltipContainer: {
        position: 'absolute',
        right: 0,
        top: 17,
        zIndex: 1,
    },
    tooltip: {
        backgroundColor: '#C3C6D8',
        paddingVertical: 6.5,
        paddingHorizontal: 5,
        borderRadius: 5,
        minWidth: 120,
        maxWidth: 500,
    },
    tooltipText: {
        color: '#000000',
        fontSize: 8,
        lineHeight: 8,
        textRendering: 'geometricPrecision'
    },
    triangle: {
        alignSelf: 'flex-end',
        marginBottom: -4,
        width: 0,
        height: 0,
        backgroundColor: 'transparent',
        borderStyle: 'solid',
        borderLeftWidth: 8,
        borderRightWidth: 8,
        borderBottomWidth: 8,
        borderLeftColor: 'transparent',
        borderRightColor: 'transparent',
        borderBottomColor: '#c2c2c2',
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
        backgroundColor: 'transparent',
        zIndex: 0,
    },
})

export default IconTooltip