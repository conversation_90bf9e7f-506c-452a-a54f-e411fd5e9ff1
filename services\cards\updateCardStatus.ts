import api from "../api/api";

const updateCardStatus = async (cardId: string, status: string) => {
    try {
        const response = await api.put('/dock-cards/control-card-status', { card_dock_id: cardId, card_status: status });
        const responseStatus = response.data.statusCode;
        if (responseStatus!== 200) {
            throw new Error(response.data.message);
        }
        return response.data;
    } catch (error: unknown) {
        if (error instanceof Error) {
            throw error; // Si ya es un Error, lo lanzamos directamente
        } else {
            // Si es otro tipo (objeto, string, etc.), lo convertimos a string apropiadamente
            throw new Error(String(error));
        }
    }
}

export default updateCardStatus;