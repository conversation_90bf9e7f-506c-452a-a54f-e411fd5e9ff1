import React, { useCallback, useState } from 'react';
import PageBaseTwiceBlackWhite from '@/components/templates/PageBaseTwiceBlackWhite';
import TransactionList from '@/components/molecules/home/<USER>';
import DashboardHeader from '@/components/organisms/home/<USER>';
import useExitAppOnBackPress from '@/hooks/useExitAppOnBackPress';
import { useLocalSearchParams } from 'expo-router';
import CopyCLABEAlertSucess from '@/components/molecules/home/<USER>';
import CopyAccountConveniaAlertSucess from '@/components/molecules/home/<USER>';
import AlertSucess from '@/components/atoms/AlertSucess';
import { useUserContext } from '@/context/UserContext';
import { useTransactionContext } from '@/context/TransactionContext';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import { RefreshControl, ScrollView, View } from 'react-native';
import { useAccountContext } from '@/context/AccountContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const Home = () => {
  const { successCreateAccount } = useLocalSearchParams();
  const { isAccountActivated, isLoading, loadUser } = useUserContext();
  const { fetchTransactions } = useTransactionContext();
  const { fetchAccountData } = useAccountContext();
  const insets = useSafeAreaInsets();

  const [showCopyCLABEAlert, setShowCopyCLABEAlert] = useState(false);
  const [showCopyAccountAlert, setShowCopyAccountAlert] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await Promise.all([
      loadUser(true),
      fetchTransactions(),
      fetchAccountData(),
    ]);
    setRefreshing(false);
  }, [loadUser, fetchTransactions, fetchAccountData]);

  useExitAppOnBackPress();

  return (
    // Renderiza el componente PageBaseTwiceBlackWhite
    // topChildren: Contenido superior, en este caso el DashboardHeader
    // bottomChildren: Contenido inferior, en este caso el TransactionList
    <View
      style={{
        flex: 1,
        backgroundColor: '#000',
        paddingTop: insets.top, // Ajusta el margen superior para que no se solape con la barra de notificaciones en Android
      }}
    >
      <ScrollView
        style={{ flex: 1, }} // ← fondo negro como tu header
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: insets.bottom, // Ajusta el margen inferior para que no se solape con el teclado en Android
          backgroundColor: '#E6E8F4'
        }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor="#fffffe" // iOS spinner color
            colors={['#fffffe']} // Android spinner color
            progressBackgroundColor="#000" // fondo del spinner en Android
            progressViewOffset={30} //// fondo del spinner en Android
          />
        }
      >
        <PageBaseTwiceBlackWhite
          topChildren={
            <DashboardHeader
              onSetShowCopyCLABEAlert={() => setShowCopyCLABEAlert(true)}
              onSetShowCopyAccountAlert={() => setShowCopyAccountAlert(true)}
            />
          }
          bottomChildren={<TransactionList />}
        />
        <CopyCLABEAlertSucess
          showCopyCLABEAlert={showCopyCLABEAlert}
          setShowCopyCLABEAlert={() => setShowCopyCLABEAlert(false)}
        />
        <CopyAccountConveniaAlertSucess
          showCopyCLABEAlert={showCopyAccountAlert}
          setShowCopyAlert={() => setShowCopyAccountAlert(false)}
        />
        <AlertSucess // Alerta de éxito al crear la cuenta
          text='Tu cuenta ya está activa'
          timeToHide={3000}
          isVisible={isAccountActivated && successCreateAccount === 'true'}
          onSetShowAlert={() => { }}
        />
        {isLoading && <LoadingScreen />}
      </ScrollView>
    </View>
  );
};

export default Home;
