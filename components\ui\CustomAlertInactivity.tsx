import React from 'react';
import { Modal, View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useAlert } from '../../context/AlertContext';

const CustomAlertInactivity = () => {
    const { show, message, hideAlert } = useAlert();

    return (
        <Modal
            visible={show}
            transparent
            animationType="fade"
            onRequestClose={hideAlert}
        >
            <View style={styles.overlay}>
                <View style={styles.container}>
                    <Text style={styles.title}>Tu sesión ha sido cerrada por inactividad</Text>
                    <Text style={styles.message}>{message}</Text>
                    <TouchableOpacity style={styles.button} onPress={hideAlert}>
                        <Text style={styles.buttonText}>Aceptar</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    container: {
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: 24,
        alignItems: 'center',
        width: 300,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 8,
        color: '#222',
        textAlign: 'center',
    },
    message: {
        fontSize: 16,
        marginBottom: 16,
        color: '#444',
        textAlign: 'center',
    },
    button: {
        backgroundColor: '#000',
        borderRadius: 8,
        paddingVertical: 10,
        paddingHorizontal: 24,
    },
    buttonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
});

export default CustomAlertInactivity;
