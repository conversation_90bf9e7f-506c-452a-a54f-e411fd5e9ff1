import { TouchableOpacity, SafeAreaView, StyleSheet, Text, View } from 'react-native';
import React, { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

// Definición de los tipos de propiedades que el componente acepta
type Props = {
  backTitle?: string; // Título opcional para el botón de retroceso
  children: React.ReactNode; // Elementos hijos que se renderizarán dentro del componente
  invert?: boolean; // Opción para invertir los colores del fondo y del texto
  onGoBack?: () => void; // Función opcional que se ejecuta al presionar el botón de retroceso
};

// Definición del componente funcional PageBaseWithBackButton
const PageBaseWithBackButton = ({
  children,
  backTitle,
  invert,
  onGoBack,
}: Props) => {
  const [disabled, setDisabled] = useState(false);

  const handleGoBack = () => {
    if (disabled) return;
    setDisabled(true);
    try {
      if (onGoBack) {
        onGoBack();
      } else {
        router.back();
      }
    } finally {
      // Si quieres volver a habilitar el botón después de cierto tiempo, puedes usar setTimeout aquí.
      // Si no, el botón quedará deshabilitado tras el primer press.
    }
  };
  // Definición de los colores basados en la propiedad 'invert'
  const backgroundColor = invert ? '#000000' : '#FFFFFF';
  const goBackTextColor = invert ? '#ffffff' : '#4A4B55';
  const goBackIconColor = invert ? '#ffffff' : '#4A4B55';

  return (
    <SafeAreaView
      style={[
        styles.wrapper,
        {
          backgroundColor,
        },
      ]}
    >
      <View style={styles.containerHeader}>
        <TouchableOpacity
          style={{
            position: 'relative', zIndex: 2, flexDirection: 'row', alignItems: 'center', gap: 8,
          }}
          onPress={handleGoBack} // Ejecuta la función onGoBack o navega hacia atrás si no se proporciona
        >
          <Ionicons name='arrow-back' size={24} color={goBackIconColor} />
          {/* Icono de flecha hacia atrás */}
          <Text
            style={[
              styles.text,
              {
                color: goBackTextColor,
              },
            ]}
          >
            {backTitle || 'Atrás'}
            {/* Texto del botón de retroceso, por defecto 'Atrás' */}
          </Text>
        </TouchableOpacity>
      </View>
      <View style={styles.body}>{children}</View>
      {/* Renderiza los elementos hijos */}
    </SafeAreaView>
  );
};

export default PageBaseWithBackButton;

// Definición de los estilos del componente
const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: '#FFFFFF',
    flex: 1,
  },
  body: {
    flex: 1,
  },
  containerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 24,
    gap: 8,
  },
  text: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 17,
    color: '#4A4B55',
  },
});
