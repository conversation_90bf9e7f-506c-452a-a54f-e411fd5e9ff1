import React, { useEffect, useRef } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'react-native';
import { renewToken } from '@/services/api/renewToken';
import * as SecureStore from 'expo-secure-store';
import AppRootProvider from '../AppRootProvider';

// Este provider fue creado solo para manejar el estado de una notificación de cambio de NIP, ya que no encontre otra forma de mostrarlo al regresar de una pantalla
// Este provider "AppProvider" se puede eliminar o mofificar si asi se desea
const RootLayout = () => {
  // Cambiar el tipo de NodeJS.Timeout a number
  const tokenIntervalRef = useRef<number | null>(null);

  // Intervalo para renovar el token en milisegundos (10 minuto)
  const TOKEN_RENEWAL_INTERVAL = 10 * 60 * 1000;

  // Efecto para renovar el token periódicamente
  useEffect(() => {
    // Función para renovar el token
    const handleTokenRenewal = async () => {
      try {
        const newtoken = await renewToken();
        if (newtoken) {
          await SecureStore.setItemAsync('userToken', newtoken);
        } else {
          throw new Error('Token no renovado'); // Lanzar un error
        }
      } catch (error) {
        console.error('Error al renovar el token :', error);
      }
    };

    // Configurar el intervalo para renovar el token periódicamente
    tokenIntervalRef.current = setInterval(handleTokenRenewal, TOKEN_RENEWAL_INTERVAL);

    // Limpiar el intervalo al desmontar el componente
    return () => {
      if (tokenIntervalRef.current) {
        clearInterval(tokenIntervalRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <AppRootProvider>
      <StatusBar backgroundColor='#000000' barStyle='light-content' translucent={false} />
      <Stack
        // Opciones de configuración para las pantallas del stack
        screenOptions={{
          animation: 'slide_from_bottom', // Animación de transición
          animationDuration: 500, // Duración de la animación
          // statusBarBackgroundColor: 'black',
          headerShown: false, // Ocultar el encabezado
        }}
      >
        {/* Definición de las diferentes pantallas del stack */}
        <Stack.Screen name='home/index' options={{
          gestureEnabled: false,
        }} />
        <Stack.Screen name='InactiveScreen' options={{
          gestureEnabled: false,
        }} />
        <Stack.Screen name='home/SeeAccount' />
        {/* pantallas del menu */}
        <Stack.Screen
          name='home/menu/Menu'
          options={{
            animation: 'slide_from_right',
          }}
        />
        <Stack.Screen name='home/menu/Tyc' />
        <Stack.Screen name='home/menu/Faq' />
        <Stack.Screen name='home/menu/ChangePassword' />
        <Stack.Screen name='home/menu/Clarifications' />
        <Stack.Screen name='home/menu/SuccessClarifications' />
        {/* pantalla de notificaciones */}
        <Stack.Screen
          name='home/Notifications'
          options={{
            animation: 'slide_from_left',
            headerShown: false, // Ocultar el encabezado
          }}
        />
        {/* // pantallas de movimientos */}
        <Stack.Screen name='movements/index' />
        <Stack.Screen name='movements/[...movement]' />
        <Stack.Screen name='movements/HistorialMovements' />
        <Stack.Screen name='movements/Contact' />
        {/* Pantallas de mis tarjetas */}
        <Stack.Screen name='myCards/index' />
        <Stack.Screen name='myCards/addCard' />
        <Stack.Screen name='myCards/SuccessAddCard' />
        <Stack.Screen name='myCards/[...card]' />
        <Stack.Screen name='myCards/SuccessDeleteCard' options={{
          gestureEnabled: false,
        }} />
        <Stack.Screen name='myCards/PassDeleteCard' />
        <Stack.Screen name='myCards/PasswordSeeNip' />
        <Stack.Screen name='myCards/SeeNip' />
        <Stack.Screen name='myCards/ResetNip' />
        <Stack.Screen name='myCards/ConfirmPasswordResetNip' />
        {/* Pantallas de transferencias */}
        <Stack.Screen name='transfers/index' />
        <Stack.Screen name='transfers/NewTransfer' />
        <Stack.Screen name='transfers/[...transferamount]' />
        <Stack.Screen name='transfers/TransferReview' />
        <Stack.Screen name='transfers/ConfirmTransfer' options={{
          gestureEnabled: false,
        }} />
        <Stack.Screen name='transfers/TransferSuccess' options={{
          gestureEnabled: false,
        }} />
        <Stack.Screen name='transfers/TransferError' options={{
          gestureEnabled: false,
        }} />
        <Stack.Screen name='transfers/PendingTransfer' options={{
          gestureEnabled: false,
        }} />
        <Stack.Screen name='transfers/VerifyEmailToTransfer' />
        <Stack.Screen name='+not-found' />
      </Stack>
    </AppRootProvider>

  );
};

// Exportación del componente RootLayout
export default RootLayout;

// Estilos (comentados)
// const styles = StyleSheet.create({});
