import { SafeAreaView, StyleSheet, View } from 'react-native';
import React from 'react';

// Definición de los tipos de las propiedades que el componente puede recibir
type Props = {
  topChildren?: React.ReactNode; // Elementos hijos que se mostrarán en la parte superior
  bottomChildren?: React.ReactNode; // Elementos hijos que se mostrarán en la parte inferior
};

// Definición del componente funcional PageBaseTwiceBlackWith
const PageBaseTwiceBlackWith = ({ topChildren, bottomChildren }: Props) => {
  return (
    // Vista principal que envuelve todo el contenido
    <View style={styles.wrapper}>
      {/* Área segura que contiene los elementos hijos superiores */}
      <SafeAreaView style={styles.safeArea}>{topChildren}</SafeAreaView>
      {/* Elementos hijos inferiores */}
      {bottomChildren}
    </View>
  );
};

export default PageBaseTwiceBlackWith;

// Definición de los estilos utilizados en el componente
const styles = StyleSheet.create({
  wrapper: {
    flex: 1, // La vista ocupa todo el espacio disponible
  },
  safeArea: {
    flex: 0, // La vista no crece para ocupar el espacio disponible
    backgroundColor: '#000', // Color de fondo negro
  },
});
