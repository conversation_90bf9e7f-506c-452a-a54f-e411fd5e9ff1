import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import ButtonGroupActionsCard from '@/components/molecules/home/<USER>';
import DashboardHeaderActions from '@/components/molecules/home/<USER>';
import DashboardCardDetails from '@/components/molecules/home/<USER>';
import { useAccountContext } from '@/context/AccountContext';
import { useUserContext } from '@/context/UserContext';

type Props = {
  onSetShowCopyCLABEAlert: () => void;
  onSetShowCopyAccountAlert: () => void;
};
// Componente principal del encabezado del dashboard
const DashboardHeader = ({
  onSetShowCopyCLABEAlert,
  onSetShowCopyAccountAlert,
}: Props) => {
  const { accountData, isLoading } = useAccountContext();
  const { user } = useUserContext()
  return (
    <View style={styles.topContainer}>
      <View style={styles.header}>
        {/* Acciones del encabezado del dashboard */}
        <DashboardHeaderActions />
        {/* Título de bienvenida */}
        <Text style={styles.title}>Bienvenido {user?.fullName}</Text>
        {/* Detalles de la tarjeta del dashboard */}
        <DashboardCardDetails
          mode='light'
          isActiveAccount={user?.enabled ?? false}
          onSetShowCopyCLABEAlert={onSetShowCopyCLABEAlert}
          onSetShowCopyAccountAlert={onSetShowCopyAccountAlert}
          availableResource={accountData?.availableResource!}
          idAccount={user?.convenia_account!}
          // clabe={accountData?.clabe!}
          membership_number={user?.membership_number!}
          loading={isLoading}
        />
      </View>
      {/* Línea divisoria */}
      <View style={styles.divider} />
      {/* Grupo de botones de acciones */}
      <ButtonGroupActionsCard idAccount={accountData?.idAccount!} />
    </View>
  );
};

export default DashboardHeader;

// Estilos del componente
const styles = StyleSheet.create({
  topContainer: {
    backgroundColor: '#000', // Fondo negro para el contenedor superior
  },
  title: {
    fontSize: 24, // Tamaño de fuente del título
    color: '#F4F5FB', // Color del texto del título
    fontFamily: 'Inter', // Familia de fuente del título
    lineHeight: 29.05, // Altura de línea del título
    textRendering: 'geometricPrecision', // Renderizado de texto preciso
    fontWeight: '400', // Peso de la fuente del título
    marginBottom: 24, // Margen inferior del título
  },
  header: {
    paddingHorizontal: 24, // Relleno horizontal del encabezado
    paddingBottom: 14, // Relleno inferior del encabezado
  },
  text: {
    color: '#FFF', // Color del texto
    fontSize: 16, // Tamaño de fuente del texto
  },
  divider: {
    height: 0.5, // Altura de la línea divisoria
    backgroundColor: '#F4F5FB', // Color de la línea divisoria
  },
});
