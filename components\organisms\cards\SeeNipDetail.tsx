import { SafeAreaView, StyleSheet, View, Text } from 'react-native';
import React from 'react';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import { ThemedText } from '@/components/ui/ThemedText';
import { router } from 'expo-router';

type Props = {
  pin: string | null;
  cardId: string;
};

const SeeNipDetail: React.FC<Props> = ({
  pin,
  cardId,
}) => {
  const onFinish = () => router.back();
  const onChangeNip = () => router.replace({
    pathname: '/(homeStack)/myCards/ResetNip',
    params: {
      cardId,
    },
  });
  return (
    <SafeAreaView style={styles.safe}>
      <View style={styles.wrapper}>
        <View style={styles.content}>
          <ThemedText type='title' lightColor='#232429'>
            Listo, tu NIP es:
          </ThemedText>
          <View style={styles.nipContainer}>
            <Text style={styles.text}>{pin}</Text>
          </View>
        </View>
        <View style={styles.buttons}>
          <PrimaryButton
            title='Listo'
            onPress={onFinish} // Llamar a la función handleSave
          />
          <SecondaryButton
            title='Cambiar NIP'
            onPress={onChangeNip} // Regresar a la pantalla anterior
            inverted
            disabled={!pin}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SeeNipDetail;

const styles = StyleSheet.create({
  safe: {
    flex: 1,
    backgroundColor: '#Ffffff',
  },
  wrapper: {
    flex: 1,
    backgroundColor: '#Ffffff',
    padding: 24,
    paddingBottom: 0,
    justifyContent: 'space-between',
  },
  content: {
    gap: 24,
  },
  nipContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 15,
    gap: 48,
    width: '100%',
    maxWidth: 442,
    height: 74,
    backgroundColor: '#D6D8E8',
    borderRadius: 20,
    margin: 'auto',
  },
  text: {
    fontFamily: 'Inter',
    fontWeight: '400',
    fontSize: 36,
    lineHeight: 43.47,
    color: '#000000',
    letterSpacing: 16,
  },
  buttons: {
    flexDirection: 'column', // Cambiar a disposición vertical
    justifyContent: 'center', // Centrar los botones verticalmente
    alignItems: 'center', // Centrar horizontalmente (opcional)
    gap: 16, // Espaciado entre botones (alternativa a marginBottom)
    paddingVertical: 16, // Espaciado interno superior/inferior (opcional)
  },
});
