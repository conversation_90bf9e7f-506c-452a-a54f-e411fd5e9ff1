import { StyleSheet, Text, TouchableOpacity, DimensionValue } from 'react-native';
import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';

type Props = {
  title: string;
  width?: DimensionValue;
  height?: DimensionValue;
  disable?: boolean;
  isSelected?: boolean;
  onPress: () => void;
};

const FilterButton = ({
  title,
  width,
  height,
  disable,
  isSelected,
  onPress,
}: Props) => {
  const gradientColor: readonly [string, string, ...string[]] = isSelected
    ? [
      '#75532F',
      '#8B6539',
      '#9F7A49',
      '#AF8B55',
      '#DCB992',
      '#DAB890',
      '#D5B289',
      '#CCA87C',
      '#BF9B6B',
      '#AF8B56',
      '#AF8B55',
      '#E9D6B8',
    ]
    : ['#ACAFC2', '#ACAFC2'];

  const gradietColorLocations: readonly [number, number, ...number[]] =
    isSelected
      ? [
        0.0041, 0.2303, 0.3945, 0.5056, 0.7408, 0.8693, 0.9156, 0.9486,
        0.9751, 0.9977, 0.9985, 1.0,
      ]
      : [0.0, 0.0];

  return (
    <TouchableOpacity
      style={[
        styles.wrapper,
        {
          width: 'auto',
          height: height || 40,
        },
      ]}
      disabled={disable}
      onPress={disable ? undefined : onPress}
    >
      <LinearGradient
        colors={gradientColor}
        locations={gradietColorLocations}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[
          styles.gradientContent,
          {
            width: width || '100%',
            height: height || 40,
          },
        ]}
      >
        <Text
          style={
            isSelected ? styles.buttonTextSelected : styles.buttonTextUnSelected
          }
        >
          {title}
        </Text>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default FilterButton;

const styles = StyleSheet.create({
  wrapper: {
    alignItems: 'center',
    borderRadius: 6,
  },
  gradientContent: {
    width: '100%',
    // paddingHorizontal: 15,
    alignItems: 'center',
    borderRadius: 26,
    justifyContent: 'center',

    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  buttonTextSelected: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Inter',
  },
  buttonTextUnSelected: {
    color: '#000',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Inter',
  },
});
