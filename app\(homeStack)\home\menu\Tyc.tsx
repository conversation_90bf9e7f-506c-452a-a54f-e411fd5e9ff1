import React from 'react';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import TycHeader from '@/components/molecules/home/<USER>';
import TycBody from '@/components/molecules/home/<USER>';
import { router } from 'expo-router';

const Tyc = () => {
  return (
    <PageBaseBWReturnButton
      onGoBack={() => router.back()}
      topChildren={<TycHeader />}
      bottomChildren={<TycBody />}
    />
  );
};

export default Tyc;
