import * as FileSystem from 'expo-file-system';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { copyTemplateToDocuments } from './copyTemplates';
import { getListTransactions } from '../services/accountService';
import { Asset } from 'expo-asset';

const MOVEMENTS_PER_PAGE = 15;
const FIRST_PAGE_MOVEMENTS = 3;

export interface Transaction {
  id: string;
  date: string;
  dateFormatted: string;
  description: string;
  amount: string | number;
  type: 'creditor' | 'debtor';
  key?: string;
}

export interface AccountInfo {
  name: string;
  email: string;
  phone: string;
  accountBalance: string;
  accountNumberConvenia: string;
  accountNumberTransfer: string;
  membershipNumber: string;
  companyAlias: string;
  convenia_account?: string;
  admin?: {
    companyName?: string;
    rfc?: string;
  };
}

export interface MovementInfo {
  fechaOperacion: string;
  fechaLiquidacion: string;
  concepto: string;
  claveRastreo: string;
  cargos: number;
  abonos: number;
  saldos: number;
}

export interface BalanceInfo {
  saldoAnterior: number;
  depositos: number;
  retiros: number;
  saldoFinal: number;
  promedioSaldosDiariosAbonos: number;
  promedioSaldosDiariosCargos: number;
}

// Función para formatear moneda
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Función para cargar imágenes como base64
const loadImageAsBase64 = async (imagePath: string): Promise<string> => {
  try {
    const asset = Asset.fromModule(imagePath);
    await asset.downloadAsync();

    if (asset.localUri) {
      const base64 = await FileSystem.readAsStringAsync(asset.localUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Determinar el tipo MIME basado en la extensión
      const extension = imagePath.split('.').pop()?.toLowerCase();
      let mimeType = 'image/png';
      if (extension === 'svg') {
        mimeType = 'image/svg+xml';
      } else if (extension === 'jpg' || extension === 'jpeg') {
        mimeType = 'image/jpeg';
      }

      return `data:${mimeType};base64,${base64}`;
    }

    throw new Error('No se pudo cargar la imagen');
  } catch (error) {
    console.warn('Error cargando imagen:', imagePath, error);
    // Retornar una imagen transparente como fallback
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9InRyYW5zcGFyZW50Ii8+PC9zdmc+';
  }
};

// Función para calcular el período de un mes atrás desde la fecha actual
const calculateLastMonthPeriod = (): {
  startDate: Date;
  endDate: Date;
  periodString: string;
} => {
  const now = new Date();
  const endDate = new Date(now);
  const startDate = new Date(now);
  startDate.setMonth(now.getMonth() - 1);

  const startMonth = startDate.toLocaleDateString('es-MX', {
    month: 'long',
    year: 'numeric',
  });
  const endMonth = endDate.toLocaleDateString('es-MX', {
    month: 'long',
    year: 'numeric',
  });

  // Si es el mismo mes y año, mostrar solo el mes
  if (
    startDate.getMonth() === endDate.getMonth() &&
    startDate.getFullYear() === endDate.getFullYear()
  ) {
    return {
      startDate,
      endDate,
      periodString: startMonth,
    };
  }

  // Si es diferente mes/año, mostrar rango
  return {
    startDate,
    endDate,
    periodString: `${startMonth} - ${endMonth}`,
  };
};

// Función para obtener transacciones del último mes
export const getLastMonthTransactions = async (
  email: string,
): Promise<Transaction[]> => {
  try {
    const { startDate, endDate } = calculateLastMonthPeriod();

    const transactions = await getListTransactions(email, {
      initialDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      page: 0,
      limit: 100,
    });

    return transactions.map((transaction: any) => ({
      id: transaction.id,
      date: transaction.date,
      dateFormatted:
        transaction.dateFormatted ||
        new Date(transaction.date).toLocaleDateString('es-MX'),
      description: transaction.description,
      amount: transaction.amount,
      type: transaction.type,
      key: transaction.key || transaction.id,
    }));
  } catch (error) {
    console.error('Error obteniendo transacciones del último mes:', error);
    throw new Error('No se pudieron obtener las transacciones del último mes');
  }
};

// Función para obtener transacciones por rango de fechas
export const getTransactionsByDateRange = async (
  email: string,
  startDate: Date,
  endDate: Date,
): Promise<Transaction[]> => {
  try {
    const transactions = await getListTransactions(email, {
      initialDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      page: 0,
      limit: 100,
    });

    return transactions.map((transaction: any) => ({
      id: transaction.id,
      date: transaction.date,
      dateFormatted:
        transaction.dateFormatted ||
        new Date(transaction.date).toLocaleDateString('es-MX'),
      description: transaction.description,
      amount: transaction.amount,
      type: transaction.type,
      key: transaction.key || transaction.id,
    }));
  } catch (error) {
    console.error('Error obteniendo transacciones por rango de fechas:', error);
    throw new Error(
      'No se pudieron obtener las transacciones para el período seleccionado',
    );
  }
};

// Función para filtrar transacciones del último mes
export const filterLastMonthTransactions = (
  transactions: Transaction[],
): Transaction[] => {
  if (!transactions || transactions.length === 0) {
    return [];
  }

  const { startDate, endDate } = calculateLastMonthPeriod();

  return transactions.filter(tx => {
    const txDate = new Date(tx.date);
    if (isNaN(txDate.getTime())) {
      return false;
    }

    return txDate >= startDate && txDate <= endDate;
  });
};

// Función para convertir transacciones al formato de movimientos
export const convertTransactionsToMovements = (
  transactions: Transaction[],
): MovementInfo[] => {
  // No filtrar aquí, usar todas las transacciones que se pasen
  return transactions.map(tx => {
    // Manejar diferentes formatos de amount
    let amount = 0;
    if (typeof tx.amount === 'string') {
      // Si es string, limpiar y convertir
      amount = Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, '')) || 0);
    } else if (typeof tx.amount === 'number') {
      // Si es número, usar directamente
      amount = Math.abs(tx.amount);
    } else {
      // Si es undefined o null, usar 0
      console.warn('Transacción con amount inválido:', tx);
      amount = 0;
    }

    return {
      fechaOperacion:
        tx.dateFormatted || new Date(tx.date).toLocaleDateString('es-MX'),
      fechaLiquidacion:
        tx.dateFormatted || new Date(tx.date).toLocaleDateString('es-MX'),
      concepto: tx.description || 'Sin concepto',
      claveRastreo: tx.key || tx.id || '-',
      cargos: tx.type === 'debtor' ? amount : 0,
      abonos: tx.type === 'creditor' ? amount : 0,
      saldos: 0, // Se calculará después el saldo acumulado
    };
  });
};

// Función principal para generar PDF desde las transacciones
export const generateStatementPDFFromTransactions = async (
  accountInfo: AccountInfo,
  transactions: Transaction[],
  useDownload: boolean = true,
): Promise<void> => {
  try {
    // Validar parámetros
    if (!accountInfo) {
      throw new Error('Faltan parámetros requeridos: accountInfo');
    }

    // Asegurar que transactions sea un array (puede estar vacío)
    const safeTransactions = transactions || [];

    // Convertir transacciones al formato esperado
    const movements = convertTransactionsToMovements(safeTransactions);
    const balanceInfo = calculateBalanceFromTransactions(safeTransactions);

    // Cargar template
    const templatesDir = FileSystem.documentDirectory + 'templates/';
    await copyTemplateToDocuments();

    const mainTemplate = await FileSystem.readAsStringAsync(
      templatesDir + 'statementTemplate.html',
    );
    const continuationTemplate = await FileSystem.readAsStringAsync(
      templatesDir + 'statementTemplateContinuation.html',
    );

    // Generar HTML del estado de cuenta
    const { periodString } = calculateLastMonthPeriod();

    // Calcular paginación
    const remainingMovements =
      movements.length > FIRST_PAGE_MOVEMENTS
        ? movements.length - FIRST_PAGE_MOVEMENTS
        : 0;
    const continuationPages = Math.ceil(
      remainingMovements / MOVEMENTS_PER_PAGE,
    );
    const totalPages = remainingMovements > 0 ? continuationPages + 1 : 1;

    // Datos comunes para todas las páginas
    const commonData = {
      fecha: periodString,
      denominacionSocial: accountInfo.admin?.companyName || accountInfo.name,
      nombreComercial: accountInfo.name,
      numeroCuenta: accountInfo.convenia_account || 'N/A',
      numeroClabe: accountInfo.accountNumberTransfer || 'N/A',
      direccionFiscal: 'CONVENIA S.A.P.I de C.V. S.O.F.O.M. ENR',
      saldoAnterior: formatCurrency(balanceInfo.saldoAnterior),
      depositos: formatCurrency(balanceInfo.depositos),
      retiros: formatCurrency(balanceInfo.retiros),
      saldoFinal: formatCurrency(balanceInfo.saldoFinal),
      promedioAbonos: formatCurrency(balanceInfo.promedioSaldosDiariosAbonos),
      promedioCargos: formatCurrency(balanceInfo.promedioSaldosDiariosCargos),
      year: new Date().getFullYear(),
      fechaGeneracion: new Date().toLocaleDateString('es-MX'),
      horaGeneracion: new Date().toLocaleTimeString('es-MX'),
    };

    // Generar primera página
    let firstPage = mainTemplate;
    const mainPageMovements = generateMovementsHTML(
      movements,
      0,
      FIRST_PAGE_MOVEMENTS,
    );

    for (const [key, value] of Object.entries({
      ...commonData,
      movimientosHTML: mainPageMovements,
      pageNumber: '1 / ' + totalPages,
    })) {
      firstPage = firstPage.replace(
        new RegExp(`{{${key}}}`, 'g'),
        value.toString(),
      );
    }

    const pages = [firstPage];

    // Generar páginas de continuación
    if (remainingMovements > 0) {
      for (let i = 0; i < continuationPages; i++) {
        let continuationPage = continuationTemplate;
        const startIndex = FIRST_PAGE_MOVEMENTS + i * MOVEMENTS_PER_PAGE;
        const pageMovements = generateMovementsHTML(
          movements,
          startIndex,
          MOVEMENTS_PER_PAGE,
        );

        for (const [key, value] of Object.entries({
          ...commonData,
          movimientosHTML: pageMovements,
          pageNumber: `${i + 2} / ${totalPages}`,
        })) {
          continuationPage = continuationPage.replace(
            new RegExp(`{{${key}}}`, 'g'),
            value.toString(),
          );
        }
        pages.push(continuationPage);
      }
    }

    // Generar y guardar PDF
    const html = pages.join('');
    const { uri } = await Print.printToFileAsync({ html });
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(uri);
    }
  } catch (error) {
    console.error('Error generando PDF desde transacciones locales:', error);
    throw error;
  }
};

// Función para calcular balance desde transacciones
export const calculateBalanceFromTransactions = (
  transactions: Transaction[],
): BalanceInfo => {
  const movements = transactions.map(tx => {
    // Manejar diferentes formatos de amount
    let amount = 0;
    if (typeof tx.amount === 'string') {
      // Si es string, limpiar y convertir
      amount = Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, '')) || 0);
    } else if (typeof tx.amount === 'number') {
      // Si es número, usar directamente
      amount = Math.abs(tx.amount);
    } else {
      // Si es undefined o null, usar 0
      console.warn('Transacción con amount inválido en balance:', tx);
      amount = 0;
    }

    return {
      fechaOperacion: tx.dateFormatted || tx.date,
      fechaLiquidacion: tx.dateFormatted || tx.date,
      concepto: tx.description || 'Sin concepto',
      claveRastreo: tx.key || tx.id || '-',
      cargos: tx.type === 'debtor' ? amount : 0,
      abonos: tx.type === 'creditor' ? amount : 0,
      saldos: 0, // Se calculará después el saldo acumulado
    };
  });

  const depositos = movements.reduce((sum, mov) => sum + mov.abonos, 0);
  const retiros = movements.reduce((sum, mov) => sum + mov.cargos, 0);

  return {
    saldoAnterior: 0, // No tenemos saldo anterior
    depositos,
    retiros,
    saldoFinal: depositos - retiros,
    promedioSaldosDiariosAbonos: depositos / Math.max(movements.length, 1),
    promedioSaldosDiariosCargos: retiros / Math.max(movements.length, 1),
  };
};

// Función para generar HTML de movimientos
const generateMovementsHTML = (
  movements: MovementInfo[],
  startIndex: number,
  count: number,
): string => {
  if (!movements || movements.length === 0) {
    return `
      <tr class="empty-row">
        <td colspan="7" style="text-align: center; padding: 20px; font-style: italic; color: #666;">
          No hay movimientos disponibles para el período seleccionado
        </td>
      </tr>
    `;
  }

  let runningBalance = 0;
  if (startIndex > 0) {
    runningBalance = movements.slice(0, startIndex).reduce((balance, mov) => {
      return balance + mov.abonos - mov.cargos;
    }, 0);
  }

  return movements
    .slice(startIndex, startIndex + count)
    .map((movement, index) => {
      runningBalance += movement.abonos - movement.cargos;

      return `
        <tr class="${index % 2 === 0 ? 'even-row' : 'odd-row'}">
          <td>${movement.fechaOperacion}</td>
          <td>${movement.fechaLiquidacion}</td>
          <td>${movement.concepto}</td>
          <td>${movement.claveRastreo}</td>
          <td class="amount-cell">${
            movement.cargos > 0 ? formatCurrency(movement.cargos) : '-'
          }</td>
          <td class="amount-cell">${
            movement.abonos > 0 ? formatCurrency(movement.abonos) : '-'
          }</td>
          <td class="balance-cell">${formatCurrency(runningBalance)}</td>
        </tr>
      `;
    })
    .join('');
};

// Función para generar las páginas HTML del template
const getHTMLTemplate = async (
  accountInfo: AccountInfo,
  balanceInfo: BalanceInfo,
  movements: MovementInfo[],
): Promise<string[]> => {
  const pages: string[] = [];
  const { periodString } = calculateLastMonthPeriod();

  // Calcular el número total de páginas necesarias
  const remainingMovements =
    movements.length > FIRST_PAGE_MOVEMENTS
      ? movements.length - FIRST_PAGE_MOVEMENTS
      : 0;
  const continuationPages = Math.ceil(remainingMovements / MOVEMENTS_PER_PAGE);
  const totalPages = remainingMovements > 0 ? continuationPages + 1 : 1;

  // Asegurarnos de que los templates existen
  await copyTemplateToDocuments();

  // Cargar las imágenes como base64
  const headerGoldenBlockImage = await loadImageAsBase64(
    require('../assets/images/header-golden-block.svg'),
  );
  const headerPatternImage = await loadImageAsBase64(
    require('../assets/images/header-pattern.svg'),
  );

  // Leer los templates
  const templatesDir = FileSystem.documentDirectory + 'templates/';
  const mainTemplatePath = templatesDir + 'statementTemplate.html';
  const continuationTemplatePath =
    templatesDir + 'statementTemplateContinuation.html';

  const mainTemplate = await FileSystem.readAsStringAsync(mainTemplatePath);
  const continuationTemplate = await FileSystem.readAsStringAsync(
    continuationTemplatePath,
  );

  // Datos comunes para todos los templates
  const commonData = {
    fecha: periodString,
    denominacionSocial: accountInfo.companyAlias || accountInfo.name,
    nombreComercial: accountInfo.name,
    numeroCuenta: accountInfo.accountNumberConvenia,
    numeroClabe: accountInfo.accountNumberTransfer,
    rfc: accountInfo.admin?.rfc || 'N/A',
    email: accountInfo.email,
    telefono: accountInfo.phone,
    numeroAfiliacion: accountInfo.membershipNumber,
    direccionFiscal: 'CONVENIA S.A.P.I de C.V. S.O.F.O.M. ENR',
    saldoAnterior: formatCurrency(balanceInfo.saldoAnterior),
    depositos: formatCurrency(balanceInfo.depositos),
    retiros: formatCurrency(balanceInfo.retiros),
    saldoFinal: formatCurrency(balanceInfo.saldoFinal),
    promedioAbonos: formatCurrency(balanceInfo.promedioSaldosDiariosAbonos),
    promedioCargos: formatCurrency(balanceInfo.promedioSaldosDiariosCargos),
    year: new Date().getFullYear(),
    fechaGeneracion: new Date().toLocaleDateString('es-MX'),
    horaGeneracion: new Date().toLocaleTimeString('es-MX'),
    headerGoldenBlockImage,
    headerPatternImage,
  };

  // Generar primera página
  let mainPage = mainTemplate;
  const mainPageMovements = generateMovementsHTML(
    movements,
    0,
    FIRST_PAGE_MOVEMENTS,
  );

  for (const [key, value] of Object.entries({
    ...commonData,
    movimientos: mainPageMovements,
    pageNumber: '1 / ' + totalPages,
  })) {
    mainPage = mainPage.replace(
      new RegExp(`{{${key}}}`, 'g'),
      value.toString(),
    );
  }
  pages.push(mainPage);

  // Generar páginas de continuación si hay más movimientos
  if (remainingMovements > 0) {
    for (let i = 0; i < continuationPages; i++) {
      let continuationPage = continuationTemplate;
      const startIndex = FIRST_PAGE_MOVEMENTS + i * MOVEMENTS_PER_PAGE;
      const pageMovements = generateMovementsHTML(
        movements,
        startIndex,
        MOVEMENTS_PER_PAGE,
      );

      for (const [key, value] of Object.entries({
        ...commonData,
        movimientos: pageMovements,
        pageNumber: `${i + 2} / ${totalPages}`,
      })) {
        continuationPage = continuationPage.replace(
          new RegExp(`{{${key}}}`, 'g'),
          value.toString(),
        );
      }
      pages.push(continuationPage);
    }
  }

  return pages;
};
// Función para generar el PDF del estado de cuenta
export const generateStatementPDF = async (
  accountInfo: AccountInfo,
  balanceInfo: BalanceInfo,
  movements: MovementInfo[],
): Promise<void> => {
  try {
    const pages = await getHTMLTemplate(accountInfo, balanceInfo, movements);
    const html = pages.join('');
    const { uri } = await Print.printToFileAsync({ html });
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(uri);
    }
  } catch (error) {
    console.error('Error generating statement PDF:', error);
    throw error;
  }
};

// Función para generar el PDF del último período
export const generateLastPeriodStatementPDF = async (
  accountInfo: AccountInfo,
  balanceInfo: BalanceInfo,
  movements: MovementInfo[],
): Promise<void> => {
  try {
    const pages = await getHTMLTemplate(accountInfo, balanceInfo, movements);
    const html = pages.join('');
    const { uri } = await Print.printToFileAsync({ html });
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(uri);
    }
  } catch (error) {
    console.error('Error generating last period statement PDF:', error);
    throw error;
  }
};
