import * as FileSystem from 'expo-file-system';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { copyTemplateToDocuments } from './copyTemplates';

const MOVEMENTS_PER_PAGE = 15;
const FIRST_PAGE_MOVEMENTS = 3;

export interface Transaction {
  id: string;
  date: string;
  dateFormatted: string;
  description: string;
  amount: string;
  type: 'creditor' | 'debtor';
  key?: string;
}

export interface AccountInfo {
  name: string;
  convenia_account?: string;
  accountNumberTransfer?: string;
  admin?: {
    companyName?: string;
    rfc?: string;
  };
}

export interface MovementInfo {
  fechaOperacion: string;
  fechaLiquidacion: string;
  concepto: string;
  claveRastreo: string;
  cargos: number;
  abonos: number;
  saldos: number;
}

export interface BalanceInfo {
  saldoAnterior: number;
  depositos: number;
  retiros: number;
  saldoFinal: number;
  promedioSaldosDiariosAbonos: number;
  promedioSaldosDiariosCargos: number;
}

// Función para formatear moneda
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Función para calcular el período de un mes atrás desde la fecha actual
const calculateLastMonthPeriod = (): { startDate: Date; endDate: Date; periodString: string } => {
  const now = new Date();
  const endDate = new Date(now);
  const startDate = new Date(now);
  startDate.setMonth(now.getMonth() - 1);

  const startMonth = startDate.toLocaleDateString('es-MX', { month: 'long', year: 'numeric' });
  const endMonth = endDate.toLocaleDateString('es-MX', { month: 'long', year: 'numeric' });

  // Si es el mismo mes y año, mostrar solo el mes
  if (startDate.getMonth() === endDate.getMonth() && startDate.getFullYear() === endDate.getFullYear()) {
    return {
      startDate,
      endDate,
      periodString: startMonth
    };
  }

  // Si es diferente mes/año, mostrar rango
  return {
    startDate,
    endDate,
    periodString: `${startMonth} - ${endMonth}`
  };
};

// Función para filtrar transacciones del último mes
export const filterLastMonthTransactions = (transactions: Transaction[]): Transaction[] => {
  if (!transactions || transactions.length === 0) {
    return [];
  }

  const { startDate, endDate } = calculateLastMonthPeriod();

  return transactions.filter(tx => {
    const txDate = new Date(tx.date);
    if (isNaN(txDate.getTime())) {
      return false;
    }

    return txDate >= startDate && txDate <= endDate;
  });
};

// Función para convertir transacciones al formato de movimientos
export const convertTransactionsToMovements = (transactions: Transaction[]): MovementInfo[] => {
  const filteredTransactions = filterLastMonthTransactions(transactions);

  return filteredTransactions.map(tx => {
    const amount = Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, '')) || 0);

    return {
      fechaOperacion: tx.dateFormatted || tx.date,
      fechaLiquidacion: tx.dateFormatted || tx.date,
      concepto: tx.description || 'Sin concepto',
      claveRastreo: tx.key || tx.id || '-',
      cargos: tx.type === 'debtor' ? amount : 0,
      abonos: tx.type === 'creditor' ? amount : 0,
      saldos: 0 // Se calculará después el saldo acumulado
    };
  });
};

// Función principal para generar PDF desde las transacciones
export const generateStatementPDFFromTransactions = async (
  accountInfo: AccountInfo,
  transactions: Transaction[],
  useDownload: boolean = true
): Promise<void> => {
  try {
    // Validar parámetros
    if (!accountInfo) {
      throw new Error('Faltan parámetros requeridos: accountInfo');
    }

    // Asegurar que transactions sea un array (puede estar vacío)
    const safeTransactions = transactions || [];

    // Convertir transacciones al formato esperado
    const movements = convertTransactionsToMovements(safeTransactions);
    const balanceInfo = calculateBalanceFromTransactions(safeTransactions);

    // Cargar template
    const templatesDir = FileSystem.documentDirectory + 'templates/';
    await copyTemplateToDocuments();

    const mainTemplate = await FileSystem.readAsStringAsync(templatesDir + 'statementTemplate.html');
    const continuationTemplate = await FileSystem.readAsStringAsync(templatesDir + 'statementTemplateContinuation.html');

    // Generar HTML del estado de cuenta
    const { periodString } = calculateLastMonthPeriod();

    // Calcular paginación
    const remainingMovements = movements.length > FIRST_PAGE_MOVEMENTS
      ? movements.length - FIRST_PAGE_MOVEMENTS
      : 0;
    const continuationPages = Math.ceil(remainingMovements / MOVEMENTS_PER_PAGE);
    const totalPages = remainingMovements > 0 ? continuationPages + 1 : 1;

    // Datos comunes para todas las páginas
    const commonData = {
      fecha: periodString,
      denominacionSocial: accountInfo.admin?.companyName || accountInfo.name,
      nombreComercial: accountInfo.name,
      numeroCuenta: accountInfo.convenia_account || 'N/A',
      numeroClabe: accountInfo.accountNumberTransfer || 'N/A',
      direccionFiscal: 'CONVENIA S.A.P.I de C.V. S.O.F.O.M. ENR',
      saldoAnterior: formatCurrency(balanceInfo.saldoAnterior),
      depositos: formatCurrency(balanceInfo.depositos),
      retiros: formatCurrency(balanceInfo.retiros),
      saldoFinal: formatCurrency(balanceInfo.saldoFinal),
      promedioAbonos: formatCurrency(balanceInfo.promedioSaldosDiariosAbonos),
      promedioCargos: formatCurrency(balanceInfo.promedioSaldosDiariosCargos),
      year: new Date().getFullYear(),
      fechaGeneracion: new Date().toLocaleDateString('es-MX'),
      horaGeneracion: new Date().toLocaleTimeString('es-MX'),
    };

    // Generar primera página
    let firstPage = mainTemplate;
    const mainPageMovements = generateMovementsHTML(movements, 0, FIRST_PAGE_MOVEMENTS);

    for (const [key, value] of Object.entries({
      ...commonData,
      movimientosHTML: mainPageMovements,
      pageNumber: '1 / ' + totalPages
    })) {
      firstPage = firstPage.replace(new RegExp(`{{${key}}}`, 'g'), value.toString());
    }

    const pages = [firstPage];

    // Generar páginas de continuación
    if (remainingMovements > 0) {
      for (let i = 0; i < continuationPages; i++) {
        let continuationPage = continuationTemplate;
        const startIndex = FIRST_PAGE_MOVEMENTS + (i * MOVEMENTS_PER_PAGE);
        const pageMovements = generateMovementsHTML(movements, startIndex, MOVEMENTS_PER_PAGE);

        for (const [key, value] of Object.entries({
          ...commonData,
          movimientosHTML: pageMovements,
          pageNumber: `${i + 2} / ${totalPages}`
        })) {
          continuationPage = continuationPage.replace(new RegExp(`{{${key}}}`, 'g'), value.toString());
        }
        pages.push(continuationPage);
      }
    }

    // Generar y guardar PDF
    const html = pages.join('');
    const { uri } = await Print.printToFileAsync({ html });
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(uri);
    }

  } catch (error) {
    console.error('Error generando PDF desde transacciones locales:', error);
    throw error;
  }
};

// Función para calcular balance desde transacciones
import * as FileSystem from 'expo-file-system';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { copyTemplateToDocuments } from './copyTemplates';

const MOVEMENTS_PER_PAGE = 15;
const FIRST_PAGE_MOVEMENTS = 3;

export interface Transaction {
  id: string;
  date: string;
  dateFormatted: string;
  description: string;
  amount: string;
  type: 'creditor' | 'debtor';
  key?: string;
}

export interface AccountInfo {
  name: string;
  convenia_account?: string;
  accountNumberTransfer?: string;
  admin?: {
    companyName?: string;
    rfc?: string;
  };
}

export interface MovementInfo {
  fechaOperacion: string;
  fechaLiquidacion: string;
  concepto: string;
  claveRastreo: string;
  cargos: number;
  abonos: number;
  saldos: number;
}

export interface BalanceInfo {
  saldoAnterior: number;
  depositos: number;
  retiros: number;
  saldoFinal: number;
  promedioSaldosDiariosAbonos: number;
  promedioSaldosDiariosCargos: number;
}

// Función para formatear moneda
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Función para calcular el período de un mes atrás desde la fecha actual
const calculateLastMonthPeriod = (): { startDate: Date; endDate: Date; periodString: string } => {
  const now = new Date();
  const endDate = new Date(now);
  const startDate = new Date(now);
  startDate.setMonth(now.getMonth() - 1);

  const startMonth = startDate.toLocaleDateString('es-MX', { month: 'long', year: 'numeric' });
  const endMonth = endDate.toLocaleDateString('es-MX', { month: 'long', year: 'numeric' });

  // Si es el mismo mes y año, mostrar solo el mes
  if (startDate.getMonth() === endDate.getMonth() && startDate.getFullYear() === endDate.getFullYear()) {
    return {
      startDate,
      endDate,
      periodString: startMonth
    };
  }

  // Si es diferente mes/año, mostrar rango
  return {
    startDate,
    endDate,
    periodString: `${startMonth} - ${endMonth}`
  };
};

// Función para filtrar transacciones del último mes
export const filterLastMonthTransactions = (transactions: Transaction[]): Transaction[] => {
  if (!transactions || transactions.length === 0) {
    return [];
  }

  const { startDate, endDate } = calculateLastMonthPeriod();

  return transactions.filter(tx => {
    const txDate = new Date(tx.date);
    if (isNaN(txDate.getTime())) {
      return false;
    }

    return txDate >= startDate && txDate <= endDate;
  });
};

// Función para convertir transacciones al formato de movimientos
export const convertTransactionsToMovements = (transactions: Transaction[]): MovementInfo[] => {
  const filteredTransactions = filterLastMonthTransactions(transactions);

  return filteredTransactions.map(tx => {
    const amount = Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, '')) || 0);

    return {
      fechaOperacion: tx.dateFormatted || tx.date,
      fechaLiquidacion: tx.dateFormatted || tx.date,
      concepto: tx.description || 'Sin concepto',
      claveRastreo: tx.key || tx.id || '-',
      cargos: tx.type === 'debtor' ? amount : 0,
      abonos: tx.type === 'creditor' ? amount : 0,
      saldos: 0 // Se calculará después el saldo acumulado
    };
  });
};

// Función para calcular balance desde transacciones
export const calculateBalanceFromTransactions = (transactions: Transaction[]): BalanceInfo => {
  const filteredTransactions = filterLastMonthTransactions(transactions);
  const movements = filteredTransactions.map(tx => ({
    fechaOperacion: tx.dateFormatted || tx.date,
    fechaLiquidacion: tx.dateFormatted || tx.date,
    concepto: tx.description || 'Sin concepto',
    claveRastreo: tx.key || tx.id || '-',
    cargos: tx.type === 'debtor' ? Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, ''))) : 0,
    abonos: tx.type === 'creditor' ? Math.abs(parseFloat(tx.amount.replace(/[^0-9.-]/g, ''))) : 0,
    saldos: 0 // Se calculará después el saldo acumulado
  }));

  const depositos = movements.reduce((sum, mov) => sum + mov.abonos, 0);
  const retiros = movements.reduce((sum, mov) => sum + mov.cargos, 0);

  return {
    saldoAnterior: 0, // No tenemos saldo anterior
    depositos,
    retiros,
    saldoFinal: depositos - retiros,
    promedioSaldosDiariosAbonos: depositos / Math.max(movements.length, 1),
    promedioSaldosDiariosCargos: retiros / Math.max(movements.length, 1)
  };
};

// Función para generar HTML de movimientos
const generateMovementsHTML = (
  movements: MovementInfo[],
  startIndex: number,
  count: number,
): string => {
  if (!movements || movements.length === 0) {
    return `
      <tr class="empty-row">
        <td colspan="7" style="text-align: center; padding: 20px; font-style: italic; color: #666;">
          No hay movimientos disponibles para el período seleccionado
        </td>
      </tr>
    `;
  }

  let runningBalance = 0;
  if (startIndex > 0) {
    runningBalance = movements.slice(0, startIndex).reduce((balance, mov) => {
      return balance + mov.abonos - mov.cargos;
    }, 0);
  }

  return movements
    .slice(startIndex, startIndex + count)
    .map((movement, index) => {
      runningBalance += movement.abonos - movement.cargos;

      return `
        <tr class="${index % 2 === 0 ? 'even-row' : 'odd-row'}">
          <td>${movement.fechaOperacion}</td>
          <td>${movement.fechaLiquidacion}</td>
          <td>${movement.concepto}</td>
          <td>${movement.claveRastreo}</td>
          <td class="amount-cell">${movement.cargos > 0 ? formatCurrency(movement.cargos) : '-'}</td>
          <td class="amount-cell">${movement.abonos > 0 ? formatCurrency(movement.abonos) : '-'}</td>
          <td class="balance-cell">${formatCurrency(runningBalance)}</td>
        </tr>
      `;
    })
    .join('');
};

// Función principal para generar PDF desde las transacciones
export const generateStatementPDFFromTransactions = async (
  accountInfo: AccountInfo,
  transactions: Transaction[],
  useDownload: boolean = true
): Promise<void> => {
  try {
    // Validar parámetros
    if (!accountInfo) {
      throw new Error('Faltan parámetros requeridos: accountInfo');
    }

    // Asegurar que transactions sea un array (puede estar vacío)
    const safeTransactions = transactions || [];

    // Convertir transacciones al formato esperado
    const movements = convertTransactionsToMovements(safeTransactions);
    const balanceInfo = calculateBalanceFromTransactions(safeTransactions);

    // Cargar template
    const templatesDir = FileSystem.documentDirectory + 'templates/';
    await copyTemplateToDocuments();

    const mainTemplate = await FileSystem.readAsStringAsync(templatesDir + 'statementTemplate.html');
    const continuationTemplate = await FileSystem.readAsStringAsync(templatesDir + 'statementTemplateContinuation.html');

    // Generar HTML del estado de cuenta
    const { periodString } = calculateLastMonthPeriod();

    // Calcular paginación
    const remainingMovements = movements.length > FIRST_PAGE_MOVEMENTS
      ? movements.length - FIRST_PAGE_MOVEMENTS
      : 0;
    const continuationPages = Math.ceil(remainingMovements / MOVEMENTS_PER_PAGE);
    const totalPages = remainingMovements > 0 ? continuationPages + 1 : 1;

    // Datos comunes para todas las páginas
    const commonData = {
      fecha: periodString,
      denominacionSocial: accountInfo.admin?.companyName || accountInfo.name,
      nombreComercial: accountInfo.name,
      numeroCuenta: accountInfo.convenia_account || 'N/A',
      numeroClabe: accountInfo.accountNumberTransfer || 'N/A',
      direccionFiscal: 'CONVENIA S.A.P.I de C.V. S.O.F.O.M. ENR',
      saldoAnterior: formatCurrency(balanceInfo.saldoAnterior),
      depositos: formatCurrency(balanceInfo.depositos),
      retiros: formatCurrency(balanceInfo.retiros),
      saldoFinal: formatCurrency(balanceInfo.saldoFinal),
      promedioAbonos: formatCurrency(balanceInfo.promedioSaldosDiariosAbonos),
      promedioCargos: formatCurrency(balanceInfo.promedioSaldosDiariosCargos),
      year: new Date().getFullYear(),
      fechaGeneracion: new Date().toLocaleDateString('es-MX'),
      horaGeneracion: new Date().toLocaleTimeString('es-MX'),
    };

    // Generar primera página
    let firstPage = mainTemplate;
    const mainPageMovements = generateMovementsHTML(movements, 0, FIRST_PAGE_MOVEMENTS);

    for (const [key, value] of Object.entries({
      ...commonData,
      movimientosHTML: mainPageMovements,
      pageNumber: '1 / ' + totalPages
    })) {
      firstPage = firstPage.replace(new RegExp(`{{${key}}}`, 'g'), value.toString());
    }

    const allPages = [firstPage];

    // Generar páginas de continuación
    if (remainingMovements > 0) {
      for (let i = 0; i < continuationPages; i++) {
        let continuationPage = continuationTemplate;
        const startIndex = FIRST_PAGE_MOVEMENTS + (i * MOVEMENTS_PER_PAGE);
        const pageMovements = generateMovementsHTML(movements, startIndex, MOVEMENTS_PER_PAGE);

        for (const [key, value] of Object.entries({
          ...commonData,
          movimientosHTML: pageMovements,
          pageNumber: `${i + 2} / ${totalPages}`
        })) {
          continuationPage = continuationPage.replace(new RegExp(`{{${key}}}`, 'g'), value.toString());
        }
        allPages.push(continuationPage);
      }
    }

    // Generar y guardar PDF
    const html = allPages.join('');
    const { uri } = await Print.printToFileAsync({ html });

    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(uri);
    }

  } catch (error) {
    console.error('Error generando PDF desde transacciones locales:', error);
    throw new Error('Error al generar el estado de cuenta: ' + (error instanceof Error ? error.message : 'Error desconocido'));
  }
};

const generateMovementsHTML = (
  movements: MovementInfo[],
  startIndex: number,
  count: number,
): string => {
  if (!movements || movements.length === 0) {
    return `
      <tr class="empty-row">
        <td colspan="7" style="text-align: center; padding: 20px; font-style: italic; color: #666;">
          No hay movimientos disponibles para el período seleccionado
        </td>
      </tr>
    `;
  }

  let runningBalance = 0;
  if (startIndex > 0) {
    runningBalance = movements.slice(0, startIndex).reduce((balance, mov) => {
      return balance + mov.abonos - mov.cargos;
    }, 0);
  }

  return movements
    .slice(startIndex, startIndex + count)
    .map((movement, index) => {
      runningBalance += movement.abonos - movement.cargos;

      return `
        <tr class="${index % 2 === 0 ? 'even-row' : 'odd-row'}">
          <td>${movement.fechaOperacion}</td>
          <td>${movement.fechaLiquidacion}</td>
          <td>${movement.concepto}</td>
          <td>${movement.claveRastreo}</td>
          <td class="amount-cell">${movement.cargos > 0 ? formatCurrency(movement.cargos) : '-'}</td>
          <td class="amount-cell">${movement.abonos > 0 ? formatCurrency(movement.abonos) : '-'}</td>
          <td class="balance-cell">${formatCurrency(runningBalance)}</td>
        </tr>
      `;
    })
    .join('');
};

// Función para generar el PDF del estado de cuenta
export const generateStatementPDF = async (
  accountInfo: AccountInfo,
  balanceInfo: BalanceInfo,
  movements: MovementInfo[],
): Promise<void> => {
  try {
    const pages = await getHTMLTemplate(accountInfo, balanceInfo, movements);
    const html = pages.join('');
    const { uri } = await Print.printToFileAsync({ html });
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(uri);
    }
  } catch (error) {
    console.error('Error generating statement PDF:', error);
    throw error;
  }
};

// Función para generar el PDF del último período
export const generateLastPeriodStatementPDF = async (
  accountInfo: AccountInfo,
  balanceInfo: BalanceInfo,
  movements: MovementInfo[],
): Promise<void> => {
  try {
    const pages = await getHTMLTemplate(accountInfo, balanceInfo, movements);
    const html = pages.join('');
    const { uri } = await Print.printToFileAsync({ html });
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(uri);
    }
  } catch (error) {
    console.error('Error generating last period statement PDF:', error);
    throw error;
  }
};

export const generateTemplates = async (
  accountInfo: AccountInfo,
  balanceInfo: BalanceInfo,
  movements: MovementInfo[],
): Promise<string[]> => {
  const MOVEMENTS_PER_PAGE = 15;
  const FIRST_PAGE_MOVEMENTS = 3;
  const pages: string[] = [];
  const { periodString } = calculateLastMonthPeriod();

  // Calcular el número total de páginas necesarias
  const remainingMovements = movements.length > FIRST_PAGE_MOVEMENTS
    ? movements.length - FIRST_PAGE_MOVEMENTS
    : 0;
  const continuationPages = Math.ceil(remainingMovements / MOVEMENTS_PER_PAGE);
  const totalPages = remainingMovements > 0 ? continuationPages + 1 : 1;

  // Asegurarnos de que los templates existen
  await copyTemplateToDocuments();

  // Leer los templates
  const templatesDir = FileSystem.documentDirectory + 'templates/';
  const mainTemplatePath = templatesDir + 'statementTemplate.html';
  const continuationTemplatePath = templatesDir + 'statementTemplateContinuation.html';

  const mainTemplate = await FileSystem.readAsStringAsync(mainTemplatePath);
  const continuationTemplate = await FileSystem.readAsStringAsync(continuationTemplatePath);

  // Datos comunes para todos los templates
  const commonData = {
    fecha: periodString,
    denominacionSocial: accountInfo.companyAlias || accountInfo.name,
    nombreComercial: accountInfo.name,
    numeroCuenta: accountInfo.accountNumberConvenia,
    numeroClabe: accountInfo.accountNumberTransfer,
    email: accountInfo.email,
    telefono: accountInfo.phone,
    numeroAfiliacion: accountInfo.membershipNumber,
    direccionFiscal: 'CONVENIA S.A.P.I de C.V. S.O.F.O.M. ENR',
    saldoAnterior: formatCurrency(balanceInfo.saldoAnterior),
    depositos: formatCurrency(balanceInfo.depositos),
    retiros: formatCurrency(balanceInfo.retiros),
    saldoFinal: formatCurrency(balanceInfo.saldoFinal),
    promedioAbonos: formatCurrency(balanceInfo.promedioSaldosDiariosAbonos),
    promedioCargos: formatCurrency(balanceInfo.promedioSaldosDiariosCargos),
    year: new Date().getFullYear(),
    fechaGeneracion: new Date().toLocaleDateString('es-MX'),
    horaGeneracion: new Date().toLocaleTimeString('es-MX'),
  };

  // Generar primera página
  let mainPage = mainTemplate;
  const mainPageMovements = generateMovementsHTML(movements, 0, FIRST_PAGE_MOVEMENTS);

  for (const [key, value] of Object.entries({ ...commonData, movimientosHTML: mainPageMovements, pageNumber: '1 / ' + totalPages })) {
    mainPage = mainPage.replace(new RegExp(`{{${key}}}`, 'g'), value.toString());
  }
  pages.push(mainPage);

  // Generar páginas de continuación
  if (remainingMovements > 0) {
    for (let i = 0; i < continuationPages; i++) {
      let continuationPage = continuationTemplate;
      const startIndex = FIRST_PAGE_MOVEMENTS + (i * MOVEMENTS_PER_PAGE);
      const pageMovements = generateMovementsHTML(movements, startIndex, MOVEMENTS_PER_PAGE);

      for (const [key, value] of Object.entries({ ...commonData, movimientosHTML: pageMovements, pageNumber: `${i + 2} / ${totalPages}` })) {
        continuationPage = continuationPage.replace(new RegExp(`{{${key}}}`, 'g'), value.toString());
      }
      pages.push(continuationPage);
    }
  }

  return pages;
  const pages: string[] = [];
  const { periodString } = calculateLastMonthPeriod();

  try {
    // Asegurarnos de que los templates existen
    await copyTemplateToDocuments();

    // Leer los templates
    const templatesDir = FileSystem.documentDirectory + 'templates/';
    const mainTemplatePath = templatesDir + 'statementTemplate.html';
    const continuationTemplatePath = templatesDir + 'statementTemplateContinuation.html';

    const mainTemplate = await FileSystem.readAsStringAsync(mainTemplatePath);
    const continuationTemplate = await FileSystem.readAsStringAsync(continuationTemplatePath);

    // Calcular paginación
    const remainingMovements = movements.length > FIRST_PAGE_MOVEMENTS ? movements.length - FIRST_PAGE_MOVEMENTS : 0;
    const continuationPages = Math.ceil(remainingMovements / MOVEMENTS_PER_PAGE);
    const totalPages = remainingMovements > 0 ? continuationPages + 1 : 1;

    // Datos comunes para todos los templates
  const commonData = {
    fecha: periodString,
    denominacionSocial: accountInfo.companyAlias || accountInfo.name,
    nombreComercial: accountInfo.name,
    numeroCuenta: accountInfo.accountNumberConvenia,
    numeroClabe: accountInfo.accountNumberTransfer,
    email: accountInfo.email,
    telefono: accountInfo.phone,
    numeroAfiliacion: accountInfo.membershipNumber,
    direccionFiscal: 'CONVENIA S.A.P.I de C.V. S.O.F.O.M. ENR',
    saldoAnterior: formatCurrency(balanceInfo.saldoAnterior),
    depositos: formatCurrency(balanceInfo.depositos),
    retiros: formatCurrency(balanceInfo.retiros),
    saldoFinal: formatCurrency(balanceInfo.saldoFinal),
    promedioAbonos: formatCurrency(balanceInfo.promedioSaldosDiariosAbonos),
    promedioCargos: formatCurrency(balanceInfo.promedioSaldosDiariosCargos),
    year: new Date().getFullYear(),
    fechaGeneracion: new Date().toLocaleDateString('es-MX'),
    horaGeneracion: new Date().toLocaleTimeString('es-MX'),
  };

  // Primera página con el template principal - solo 3 movimientos
  let mainPage = mainTemplate;
  const mainPageMovements = generateMovementsHTML(
    movements,
    0,
    FIRST_PAGE_MOVEMENTS,
  );

  // Reemplazar variables en la primera página
  const mainPageData = {
    ...commonData,
    movimientosHTML: mainPageMovements,
    pageNumber: '1 / ' + totalPages,
  };

  for (const [key, value] of Object.entries(mainPageData)) {
    mainPage = mainPage.replace(
      new RegExp(`{{${key}}}`, 'g'),
      value.toString(),
    );
  }
  pages.push(mainPage);

  // Generar páginas de continuación si hay más movimientos
  if (remainingMovements > 0) {
    for (let i = 0; i < continuationPages; i++) {
      let continuationPage = continuationTemplate;
      const startIndex = FIRST_PAGE_MOVEMENTS + (i * MOVEMENTS_PER_PAGE);
      const pageMovements = generateMovementsHTML(
        movements,
        startIndex,
        MOVEMENTS_PER_PAGE,
      );

    const continuationData = {
      ...commonData,
      movimientosHTML: pageMovements,
      pageNumber: `${i + 1} / ${totalPages}`,
    };

    for (const [key, value] of Object.entries(continuationData)) {
      continuationPage = continuationPage.replace(
        new RegExp(`{{${key}}}`, 'g'),
        value.toString(),
      );
    }
    pages.push(continuationPage);
  }

  return pages;
};
