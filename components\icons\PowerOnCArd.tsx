import * as React from 'react';
import Svg, {
  SvgProps,
  Path,
  Defs,
  LinearGradient,
  Stop,
} from 'react-native-svg';
const PowerOnCArd = (props: SvgProps) => (
  <Svg width={40} height={40} fill='none' {...props}>
    <Path
      fill='url(#a)'
      d='M20 35c7.352 0 13.333-5.982 13.333-13.334 0-5.583-3.453-10.368-8.333-12.351v3.705a9.999 9.999 0 0 1 5 8.646c0 5.515-4.485 10-10 10s-10-4.485-10-10a9.998 9.998 0 0 1 5-8.646V9.315c-4.88 1.983-8.333 6.768-8.333 12.351C6.667 29.018 12.648 35 20 35Z'
    />
    <Path fill='url(#b)' d='M18.333 3.333h3.334V20h-3.334V3.333Z' />
    <Defs>
      <LinearGradient
        id='a'
        x1={6.666}
        x2={33.334}
        y1={19.167}
        y2={19.167}
        gradientUnits='userSpaceOnUse'
      >
        <Stop offset={0.004} stopColor='#75532F' />
        <Stop offset={0.23} stopColor='#8B6539' />
        <Stop offset={0.395} stopColor='#9F7A49' />
        <Stop offset={0.506} stopColor='#AF8B55' />
        <Stop offset={0.741} stopColor='#DCB992' />
        <Stop offset={0.869} stopColor='#DAB890' />
        <Stop offset={0.916} stopColor='#D5B289' />
        <Stop offset={0.949} stopColor='#CCA87C' />
        <Stop offset={0.975} stopColor='#BF9B6B' />
        <Stop offset={0.998} stopColor='#AF8B56' />
        <Stop offset={0.999} stopColor='#AF8B55' />
        <Stop offset={1} stopColor='#E9D6B8' />
      </LinearGradient>
      <LinearGradient
        id='b'
        x1={6.666}
        x2={33.334}
        y1={19.167}
        y2={19.167}
        gradientUnits='userSpaceOnUse'
      >
        <Stop offset={0.004} stopColor='#75532F' />
        <Stop offset={0.23} stopColor='#8B6539' />
        <Stop offset={0.395} stopColor='#9F7A49' />
        <Stop offset={0.506} stopColor='#AF8B55' />
        <Stop offset={0.741} stopColor='#DCB992' />
        <Stop offset={0.869} stopColor='#DAB890' />
        <Stop offset={0.916} stopColor='#D5B289' />
        <Stop offset={0.949} stopColor='#CCA87C' />
        <Stop offset={0.975} stopColor='#BF9B6B' />
        <Stop offset={0.998} stopColor='#AF8B56' />
        <Stop offset={0.999} stopColor='#AF8B55' />
        <Stop offset={1} stopColor='#E9D6B8' />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default PowerOnCArd;
