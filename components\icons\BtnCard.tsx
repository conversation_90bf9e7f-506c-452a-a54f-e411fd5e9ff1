import * as React from "react"
import Svg, { SvgProps, Path } from "react-native-svg"
const BtnCard = (props: SvgProps) => (
    <Svg
        width={29}
        height={29}
        fill="none"
        {...props}
    >
        <Path
            stroke="#F4F5FB"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M26 13.834V21.5a2.333 2.333 0 0 1-2.333 2.333H8.5A2.333 2.333 0 0 1 6.167 21.5v-1.75M26 13.834v-1.668a2.333 2.333 0 0 0-2.333-2.333H22.5m3.5 4h-3.5"
        />
        <Path
            stroke="#F4F5FB"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M22.5 9.833v7.584a2.333 2.333 0 0 1-2.333 2.333H5a2.333 2.333 0 0 1-2.333-2.333V8.083A2.333 2.333 0 0 1 5 5.75h15.167A2.333 2.333 0 0 1 22.5 8.083v1.75Zm0 0H6.75"
        />
    </Svg>
)
export default BtnCard
