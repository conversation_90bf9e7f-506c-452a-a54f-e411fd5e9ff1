// hook que maneja la autenticación biométrica
import { useState } from 'react';
import * as SecureStore from 'expo-secure-store';
import getBiometricMethodsTypes from '@/shared/getBiometricMethodsTypes';
import { router } from 'expo-router';
import { loginWithBiometricToken } from '@/services/biometrics/biometricService';
import { removeSecureValues } from '@/shared/removeSecureValues';
import * as LocalAuthentication from 'expo-local-authentication';
import { updateExpoPushTokenIfNeeded } from '@/shared/updateExpoPushToken';
import { useNotification } from '@/context/NotificationContext';

// obtener el estado de la autenticación biométrica

const useBiometricsAuthentication = () => {
  const [loading, setLoading] = useState(false);
  const { expoPushToken } = useNotification();

  const handleFailure = async () => {
    // eliminar el token de autenticación biométrica del almacenamiento seguro
    await removeSecureValues();
  };

  const enableBiometricsStore = async () => {
    const hasHardwareAsync = await LocalAuthentication.hasHardwareAsync();
    const isEnrolledAsync = await LocalAuthentication.isEnrolledAsync();
    if (!hasHardwareAsync || !isEnrolledAsync) {
      // borramos los datos de biometricos del secure store
      await handleFailure();
      return;
    }

    const hasMetods = await getBiometricMethodsTypes();
    if (hasMetods.length === 0) {
      return;
    }

    const biometricsEnabledStore = await SecureStore.getItemAsync(
      'biometricsEnabled',
    );

    return biometricsEnabledStore;
  };

  const getBiometricsSetting = async () => {
    const biometricsEnabledStore = await enableBiometricsStore();

    const authBToken = await SecureStore.getItemAsync('authBToken', {
      requireAuthentication: true,
      authenticationPrompt: 'Iniciar sesión',
    });

    const enabled = biometricsEnabledStore === 'true' && authBToken !== null;
    if (!enabled) {
      await handleFailure();
      return;
    }

    if (authBToken === null) {
      // eliminar el token de autenticación biométrica
      handleFailure();
      return;
    }
    // todo: iniciar sesion con la el token de autenticación biométrica
    // si no hay token de autenticación biométrica, redirigir al usuario a la pantalla de inicio de sesión
    // si hay token de autenticación biométrica, redirigir al usuario al home
    try {
      setLoading(true);
      const response = await loginWithBiometricToken(authBToken);
      const isAccountEnabled = response.user.enabled;
      if (!isAccountEnabled) {
        // redirigir a la pantalla de InactiveScreen
        router.replace('/(homeStack)/InactiveScreen');
        return;
      }
      // si el token d biometria es distinto al almacenado, redirigir al usuario a la pantalla de inicio de sesión
      const responseBiometricToken = response.user.biometricToken;
      if (responseBiometricToken !== authBToken) {
        handleFailure();
        setLoading(false);
        return;
      }
      const loginToken = response.token;
      const email = response.user.email;
      await SecureStore.setItemAsync('userToken', loginToken);
      await SecureStore.setItemAsync('userEmail', email);
      // Actualizar el Expo Push Token después de un login exitoso
      await updateExpoPushTokenIfNeeded(response.user.id, expoPushToken);
      setLoading(false);
      // router.dismissAll();
      router.replace({
        pathname: '/(homeStack)/home',
        params: { email: email },
      });
    } catch (error) {
      console.error(
        `Error al iniciar sesión con el token de autenticación biométrica: ${error}`,
      );
      setLoading(false);
    }
  };
  // funcion para saber si puede inicar sesión con biométricos
  const canAuthenticateWithBiometrics = async () => {
    const biometricsEnabledStore = await enableBiometricsStore();

    if (biometricsEnabledStore === 'true') {
      return true;
    }

    return false;
  };

  return { loading, getBiometricsSetting, canAuthenticateWithBiometrics };
};

export default useBiometricsAuthentication;
