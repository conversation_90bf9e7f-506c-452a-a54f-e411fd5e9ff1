import React from 'react';
import PageBaseTwiceBlackWith from '@/components/templates/PageBaseTwiceBlackWhite';
import NewTransferSetAmountHeader from '@/components/organisms/tranfers/NewTransferSetAmountHeader';
import NewTransferSetAmountForm from '@/components/organisms/tranfers/NewTransferSetAmountForm';
import { router, useLocalSearchParams } from 'expo-router';
import { useTransferContext } from '@/context/TransferContext';
import { useInactivity } from '@/context/InactivityContext';

const TransferAmount = () => {
  const { resetInactivityTimer } = useInactivity();

  const { name, num_clabe, bank, contact, beneficiaryBank } = useLocalSearchParams<{
    name: string;
    num_clabe: string;
    bank: string;
    contact: string;
    beneficiaryBank: string;
  }>();

  const {
    rawAmount,
    formattedAmount,
    concept,
    transferData,
    errorAmount,
    errorConcept,
    isAmountValid,
    handleChangeAmount,
    handleSetConcept,
    setTransferData,
    clearTransferData,
  } = useTransferContext();

  const parseAmount = (raw: string): number => {
    return parseFloat(raw) / 100 || 0;
  };

  const handleNext = () => {
    const amountNum = parseAmount(rawAmount);

    if (!isAmountValid) {
      return;
    }

    if (amountNum > 0) {
      setTransferData({
        ...transferData,
        amount: formattedAmount,
        concept,
        name,
        number: num_clabe,
        bank,
        beneficiaryBank,
      });

      router.push('/(homeStack)/transfers/TransferReview');
    }
  };

  const handleCancel = () => {
    clearTransferData()
    if (contact === 'true') {
      router.dismiss(1);
    } else {
      router.dismiss(2);
    }
  };

  const handleBack = () => router.back();

  const disabledNextBtn =
    formattedAmount === '' ||
    concept === '' ||
    !!errorAmount ||
    !!errorConcept ||
    !isAmountValid;

  return (
    <PageBaseTwiceBlackWith
      topChildren={
        <NewTransferSetAmountHeader
          destination={name}
          bankName={bank}
          numberAccount={num_clabe}
          onBack={handleBack}
        />
      }
      bottomChildren={
        <NewTransferSetAmountForm
          concept={concept}
          amountValue={formattedAmount}
          disabledNextBtn={disabledNextBtn}
          errorAmount={errorAmount}
          errorConcept={errorConcept}
          onSetAmount={(v) => {
            resetInactivityTimer();
            handleChangeAmount(v);
          }}
          onSetConcept={(v) => {
            resetInactivityTimer();
            handleSetConcept(v);
          }}
          onNext={handleNext}
          onCancel={handleCancel}
        />
      }
    />
  );
};

export default TransferAmount;
