import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { ThemedText } from '@/components/ui/ThemedText';
import HorizontalFilterList from '@/components/molecules/movements/HorizontalFilterList';

// Componente principal del encabezado del dashboard
const MovementsHeader = ({
  selectedFilter,
  setSelectedFilter,
}: {
  selectedFilter: string;
  setSelectedFilter: React.Dispatch<React.SetStateAction<string>>;
}) => {
  // Lista de opciones para los botones
  const filterOptions = ['90 días', '60 días', '30 días', '15 días'];

  //Rango de fechas de prueba
  //const filterOptions = ['1 días', '2 días', '3 días', '4 días', '90 días'];

  return (
    <View style={styles.topContainer}>
      <View style={styles.header}>
        {/* Título de bienvenida */}
        <Text style={styles.title}>Movimientos</Text>
        {/* Título de bienvenida */}
        <ThemedText lightColor='#F4F5FB' type='defaultSemiBold'>
          Ve tus ultimos movimientos
        </ThemedText>
        {/* Contenedor de los botones */}
      </View>
      <View style={styles.buttonContainer}>
        <HorizontalFilterList
          filterOptions={filterOptions}
          selectedFilter={selectedFilter}
          setSelectedFilter={setSelectedFilter}
        />
      </View>
    </View>
  );
};

export default MovementsHeader;

// Estilos del componente
const styles = StyleSheet.create({
  topContainer: {
    backgroundColor: '#000', // Fondo negro para el contenedor superior
    paddingBottom: 16, // Relleno inferior del encabezado
    gap: 24,
  },
  title: {
    fontSize: 24, // Tamaño de fuente del título
    color: '#F4F5FB', // Color del texto del título
    fontFamily: 'Inter', // Familia de fuente del título
    lineHeight: 29.05, // Altura de línea del título
    textRendering: 'geometricPrecision', // Renderizado de texto preciso
    fontWeight: '400', // Peso de la fuente del título
    marginBottom: 24, // Margen inferior del título
    marginTop: 24, // Margen superior del título
  },
  header: {
    paddingHorizontal: 24, // Relleno horizontal del encabezado
  },
  text: {
    color: '#FFF', // Color del texto
    fontSize: 16, // Tamaño de fuente del texto
  },
  divider: {
    height: 0.5, // Altura de la línea divisoria
    backgroundColor: '#F4F5FB', // Color de la línea divisoria
  },
  buttonContainer: {
    flexDirection: 'row', // Botones en una fila
    justifyContent: 'space-around', // Espaciado uniforme entre botones
    alignItems: 'center', // Alineación vertical de los botones
  },
});
