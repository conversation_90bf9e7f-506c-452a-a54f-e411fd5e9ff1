import React from 'react';
import { ScrollView, View, StyleSheet } from 'react-native';
import FilterButton from '@/components/atoms/FilterButton';

interface HorizontalFilterListProps {
  filterOptions: string[];
  selectedFilter: string;
  setSelectedFilter: (filter: string) => void;
}

const HorizontalFilterList: React.FC<HorizontalFilterListProps> = ({
  filterOptions,
  selectedFilter,
  setSelectedFilter,
}) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.scrollView}
    >
      <View style={styles.buttonContainer}>
        {filterOptions.map((option, index) => (
          <FilterButton
            key={index}
            title={option}
            isSelected={selectedFilter === option}
            width={0}
            onPress={() => setSelectedFilter(option)}
          />
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flexDirection: 'row',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 24,
  },
});

export default HorizontalFilterList;
