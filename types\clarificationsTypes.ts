/* eslint-disable no-unused-vars */
export enum CLARIFICATION_TYPES {
  FRAUDES = 'Fraudes',
  COMPRAS_NO_RECONOCIDAS = 'Compras no reconocidas',
  PROBLEMA_EN_CAJEROS = 'Problema en cajeros',
  TRANSFERENCIAS_O_TRASPASOS_NO_ENVIADOS = 'Transferencias o traspasos no enviados',
  OTROS = 'Otros',
}

export type RNFile = {
  uri: string;
  fileName: string;
  type?: string;
};

export type FormDataClarification = {
  type: CLARIFICATION_TYPES | null;
  description: string;
  images?: RNFile[];
};

export interface ClarificationData {
  type: string;
  description: string;
  status: string;
  createdBy?: string;
  email?: string;
}

export interface ClarificationResponseData {
  trackingNumber: string;
  type: string;
  description: string;
  status: string;
  createdAt: string;
  createdBy?: {
    id: string;
    fullName?: string;
    email?: string;
  };
}
