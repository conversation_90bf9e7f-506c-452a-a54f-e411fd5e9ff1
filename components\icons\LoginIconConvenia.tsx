import * as React from "react"
import Svg, {
    SvgProps,
    G,
    Path,
    Defs,
    LinearGradient,
    Stop,
    ClipPath,
} from "react-native-svg"
const LoginIconConvenia = (props: SvgProps) => (
    <Svg
        width={342}
        height={149}
        fill="none"
        {...props}
    >
        <G clipPath="url(#a)">
            <Path
                fill="url(#b)"
                d="M260.993 41.628c.005 9.34-1.507 17.23-5.618 24.475-5.857 10.323-14.883 16.21-26.543 18.184-7.833 1.327-15.587.867-23.08-1.928-9.57-3.567-16.64-10-20.731-19.248-6.127-13.854-6.192-27.966.05-41.77 5.323-11.766 14.953-18.42 27.661-20.6 8.332-1.428 16.574-.883 24.437 2.427 11.75 4.944 18.78 13.945 22.123 25.968 1.242 4.435 1.776 8.98 1.701 12.492Zm-6.057 1.08c.025-4.52-.479-8.975-1.756-13.325-2.834-9.631-8.432-17.017-17.887-21.1-6.835-2.95-14.005-3.475-21.294-2.26-10.937 1.827-19.119 7.553-23.55 17.725-5.209 11.952-5.204 24.207-.394 36.286 3.538 8.89 10.089 14.909 19.329 17.84 5.987 1.902 12.134 2.17 18.31 1.115 10.388-1.77 18.196-7.15 22.991-16.569 3.153-6.195 4.236-12.845 4.251-19.712Z"
            />
            <Path
                fill="url(#c)"
                d="M156.038 14.651c-1.567 1.347-3.068 2.634-4.605 3.96-.22-.206-.399-.337-.529-.504-6.132-7.825-14.379-11.488-24.178-12.068-5.897-.348-11.725.025-17.353 1.967-9.939 3.426-16.64 10.126-19.892 20.015-3.408 10.358-3.318 20.782.783 30.933 4.615 11.413 13.386 17.987 25.505 20 8.956 1.488 17.722.52 26.005-3.37 4.43-2.084 8.137-5.152 11.32-9.072 1.442 1.307 2.864 2.598 4.381 3.976-3.213 3.325-6.721 6.205-10.707 8.476-6.8 3.875-14.185 5.67-21.993 5.938-7.349.252-14.434-.853-21.095-4.031-10.832-5.171-17.667-13.713-20.795-25.1-2.744-9.98-2.55-20.005.963-29.788 4.87-13.577 14.758-21.61 28.848-24.425 10.033-2.003 19.977-1.437 29.522 2.604 5.149 2.174 11.076 6.73 13.82 10.49Z"
            />
        </G>
        <Path
            fill="url(#d)"
            d="M84.096 115.5h-3.963c-.234-1.14-.644-2.141-1.23-3.004a8.196 8.196 0 0 0-2.11-2.174 8.958 8.958 0 0 0-2.732-1.342 10.923 10.923 0 0 0-3.133-.447c-1.981 0-3.776.5-5.385 1.502-1.598 1.001-2.87 2.477-3.819 4.426-.938 1.95-1.406 4.342-1.406 7.175 0 2.834.468 5.226 1.406 7.175.948 1.95 2.221 3.426 3.82 4.427 1.608 1.001 3.403 1.502 5.385 1.502 1.086 0 2.13-.149 3.132-.447a9.181 9.181 0 0 0 2.732-1.327 8.408 8.408 0 0 0 2.11-2.189c.585-.874.996-1.875 1.23-3.004h3.963c-.298 1.672-.842 3.169-1.63 4.49a12.278 12.278 0 0 1-2.94 3.372 12.87 12.87 0 0 1-3.947 2.093c-1.45.48-3 .719-4.65.719-2.792 0-5.274-.681-7.447-2.045-2.173-1.364-3.883-3.303-5.13-5.817-1.246-2.514-1.87-5.497-1.87-8.949 0-3.451.624-6.434 1.87-8.948 1.247-2.515 2.957-4.454 5.13-5.817 2.173-1.364 4.655-2.046 7.447-2.046 1.65 0 3.2.24 4.65.719a12.7 12.7 0 0 1 3.947 2.11 12.053 12.053 0 0 1 2.94 3.356c.788 1.31 1.332 2.807 1.63 4.49Zm34.018 6.136c0 3.452-.623 6.435-1.87 8.949-1.246 2.514-2.956 4.453-5.129 5.817-2.174 1.364-4.656 2.045-7.447 2.045-2.791 0-5.274-.681-7.447-2.045-2.173-1.364-3.883-3.303-5.13-5.817-1.246-2.514-1.87-5.497-1.87-8.949 0-3.451.624-6.434 1.87-8.948 1.247-2.515 2.957-4.454 5.13-5.817 2.173-1.364 4.656-2.046 7.447-2.046 2.791 0 5.273.682 7.447 2.046 2.173 1.363 3.883 3.302 5.129 5.817 1.247 2.514 1.87 5.497 1.87 8.948Zm-3.835 0c0-2.833-.474-5.225-1.423-7.175-.937-1.949-2.21-3.425-3.819-4.426-1.598-1.002-3.388-1.502-5.369-1.502-1.982 0-3.777.5-5.386 1.502-1.597 1.001-2.87 2.477-3.819 4.426-.937 1.95-1.406 4.342-1.406 7.175 0 2.834.469 5.226 1.406 7.175.948 1.95 2.222 3.426 3.82 4.427 1.608 1.001 3.403 1.502 5.385 1.502 1.981 0 3.771-.501 5.369-1.502 1.609-1.001 2.882-2.477 3.819-4.427.949-1.949 1.423-4.341 1.423-7.175Zm36.45-16.363V138h-3.835l-17.834-25.696h-.319V138h-3.963v-32.727h3.835l17.897 25.76h.32v-25.76h3.899Zm9.273 0 9.716 27.549h.383l9.716-27.549h4.155L171.955 138h-4.091l-12.017-32.727h4.155ZM189.07 138v-32.727h19.751v3.515h-15.788v11.059h14.765v3.515h-14.765v11.122h16.044V138H189.07Zm52.846-32.727V138h-3.835l-17.834-25.696h-.32V138h-3.963v-32.727h3.835l17.898 25.76h.32v-25.76h3.899Zm11.893 0V138h-3.963v-32.727h3.963ZM263.097 138h-4.154l12.017-32.727h4.091L287.068 138h-4.155l-9.78-27.55h-.256l-9.78 27.55Zm1.534-12.784h16.748v3.516h-16.748v-3.516Z"
        />
        <Defs>
            <LinearGradient
                id="b"
                x1={180.406}
                x2={260.999}
                y1={42.499}
                y2={42.499}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.022} stopColor="#93663A" />
                <Stop offset={0.086} stopColor="#805A33" />
                <Stop offset={0.118} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.697} stopColor="#DCB992" />
                <Stop offset={0.789} stopColor="#C9A678" />
                <Stop offset={0.904} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
            <LinearGradient
                id="c"
                x1={81.005}
                x2={157.475}
                y1={42.679}
                y2={42.679}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.022} stopColor="#93663A" />
                <Stop offset={0.086} stopColor="#805A33" />
                <Stop offset={0.118} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.697} stopColor="#DCB992" />
                <Stop offset={0.789} stopColor="#C9A678" />
                <Stop offset={0.904} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
            <LinearGradient
                id="d"
                x1={-0.005}
                x2={342.004}
                y1={122.002}
                y2={122.002}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
            <ClipPath id="a">
                <Path fill="#fff" d="M81 0h180v85H81z" />
            </ClipPath>
        </Defs>
    </Svg>
)
export default LoginIconConvenia
