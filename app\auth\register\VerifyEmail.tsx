import React, { useState } from 'react';
import { useLocalSearchParams, router } from 'expo-router';
import VerifyEmailNumber from '@/components/organisms/login/VerifyEmailNumber';
import { generateOTPCode, verifyOTPCode } from '@/services/userService';

// Componente funcional VerifyEmail
const VerifyEmail = () => {
  // Obtiene el parámetro de búsqueda local 'email'
  const { email, fullName, phone, affiliationNumber, token, dialing_code, country_code } =
    useLocalSearchParams();
  // Estado para almacenar el número de verificación
  const [number, setNumber] = useState('');
  // Estado para almacenar el nuevo token
  const [newToken, setNewToken] = useState(Array.isArray(token) ? token[0] : token);

  const [hasError, setHasError] = useState(false);

  const hanldeSetCode = (number: string) => {
    // Función para establecer el código
    setNumber(number);
    setHasError(false);
  }

  // Función para manejar el evento de continuar
  const handleContinue = async () => {
    // Crear un código OTP
    const response = await verifyOTPCode({
      email: Array.isArray(email) ? email[0] : email,
      code: number,
      access: newToken,
    });
    // Si el código de estado es 200
    if (response.statusCode === 200) {
      // Navega a la ruta para generar una nueva contraseña
      router.push({
        pathname: '/auth/register/GeneratePassword',
        params: { email, fullName, phone, affiliationNumber, dialing_code, country_code },
      });
    } else {
      // Actualiza el estado de error
      setHasError(true);
    }
  };

  const handleResendCode = async () => {
    // Función para reenviar el código OTP
    const response = await generateOTPCode(Array.isArray(email) ? email[0] : email);
    setNewToken(response.data.token)
  }

  return (
    // Renderiza el componente VerifyEmailNumber con las propiedades necesarias
    <VerifyEmailNumber
      email={email}
      hasError={hasError}
      number={number}
      onSetNumber={hanldeSetCode}
      onContinue={handleContinue}
      onResendCode={handleResendCode} // Función para reenviar el código
    />
  );
};

export default VerifyEmail;
