const getRemainTimeCvv = (expirationTime: string) => {
    const expirationTimeMs = new Date(expirationTime).getTime();
    
    const currentTimeMs = Date.now();
    
    const remainTimeMilliseconds = expirationTimeMs - currentTimeMs;
    
    const remainTimeMs = Math.max(0, remainTimeMilliseconds);
    
    // Convert milliseconds to seconds
    const remainTimeSeconds = Math.floor(remainTimeMs / 1000);
    
    return remainTimeSeconds;
}

export default getRemainTimeCvv