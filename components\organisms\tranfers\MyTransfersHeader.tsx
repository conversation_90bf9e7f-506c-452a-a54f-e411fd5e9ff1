import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import React from 'react';
import GoldGradientText from '@/components/atoms/GoldGradientText';
import { router } from 'expo-router';
import { useAccountContext } from '@/context/AccountContext';
import Plus from '@/components/icons/Plus';

interface MyTransfersHeaderProps {
  isEnabledToTransfers?: boolean;
}

const MyTransfersHeader: React.FC<MyTransfersHeaderProps> = ({ isEnabledToTransfers }) => {
  const { setTransferCalculate } = useAccountContext();

  return (
    <View style={{ gap: 18, padding: 24 }}>
      <View style={{ gap: 24 }}>
        <Text style={styles.title}>Transferencias</Text>
        <Text style={styles.subtitle}>Realizar nueva transferencia</Text>
      </View>
      <View style={styles.addCardContainer}>
        <GoldGradientText text={isEnabledToTransfers ? 'Nuevo envió' : 'No puedes hacer transferencias'} style={styles.addCardText} widthWrapper={isEnabledToTransfers ? 114 : 215} />
        {isEnabledToTransfers && (
          <TouchableOpacity
            onPress={() => {
              // limopiamos el estado del transferCalculate para que no se muestre la informacion el formulario de transferencia en el componente NewTransfer
              // ya que se comienza una transferencia nueva y el estado del transferCalculate se mantiene con la informacion de la transferencia anterior
              setTransferCalculate(null);
              router.push('/(homeStack)/transfers/NewTransfer')
            }
            }
          >
            <Plus />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default MyTransfersHeader;

// Estilos para el componente
const styles = StyleSheet.create({
  title: {
    fontFamily: 'Inter',
    fontWeight: '400',
    fontSize: 24,
    lineHeight: 29,
    color: '#F4F5FB',
  },
  subtitle: {
    fontFamily: 'Inter',
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 19.46,
    color: '#F4F5FB',
    textRendering: 'geometricPrecision',
  },
  addCardText: {
    textAlign: 'left',
    width: '100%',
  },
  addCardContainer: {
    flexDirection: 'row',
    maxWidth: 144,
  },
});
