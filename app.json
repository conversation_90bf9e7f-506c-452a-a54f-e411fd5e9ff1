{"expo": {"name": "Con<PERSON>ia <PERSON>", "slug": "finberryApp", "owner": "finberry", "version": "2.4.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "mx.convenia.conveniawallet", "infoPlist": {"NSFaceIDUsageDescription": "This app uses Face ID to authentication", "ITSAppUsesNonExemptEncryption": false}}, "android": {"package": "mx.convenia.conveniawallet", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#000000"}, "permissions": ["android.permission.USE_BIOMETRIC", "android.permission.USE_FINGERPRINT"], "edgeToEdgeEnabled": false, "googleServicesFile": "./google-services.json"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-secure-store", ["expo-splash-screen", {"image": "./assets/images/logo.png", "imageWidth": 300, "resizeMode": "contain", "backgroundColor": "#000000"}], ["expo-local-authentication", {"faceIDPermission": "Allow $(PRODUCT_NAME) to use Face ID."}], ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#000000"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "84d9830c-1095-4586-bb1a-a426c110e5ec"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/84d9830c-1095-4586-bb1a-a426c110e5ec"}}}