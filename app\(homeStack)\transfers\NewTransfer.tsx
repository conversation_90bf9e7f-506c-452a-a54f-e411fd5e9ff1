import React, { useState } from 'react';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import NewTransferHeader from '@/components/organisms/tranfers/NewTransferHeader';
import NewTransferContent from '@/components/organisms/tranfers/NewTransferContent';
import LoadingScreen from '@/components/molecules/LoadingScreen';

const NewTransfer = () => {
  const [loading, setLoading] = useState(false);

  return (
    <>
      <PageBaseBWReturnButton
        topChildren={<NewTransferHeader />}
        bottomChildren={<NewTransferContent setLoading={setLoading} />}
      />
      {loading && <LoadingScreen />}
    </>
  );
};

export default NewTransfer;
