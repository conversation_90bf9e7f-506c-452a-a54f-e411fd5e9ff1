import React, { useState } from 'react';
import { router } from 'expo-router';
import PageBaseWithBackButton from '@/components/templates/PageBaseWithBackButton';
import * as LocalAuthentication from 'expo-local-authentication';
import FingerBottomModal from '@/components/organisms/home/<USER>';
import LoginScreen from '@/components/organisms/login/LoginScreen';
import validateEmail from '@/shared/validateEmail';
import { signin } from '@/services/auth';
import * as SecureStore from 'expo-secure-store';
import { StatusBar } from 'react-native';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import { removeSecureValues } from '@/shared/removeSecureValues';
import { useNotification } from '@/context/NotificationContext';
import { updateExpoPushTokenIfNeeded } from '@/shared/updateExpoPushToken';

const Login = () => {
  const [email, setEmail] = useState(''); // Estado para almacenar el email
  const [password, setPassword] = useState(''); // Estado para almacenar la contraseña
  const [showFingerprintModal, setShowFingerprintModal] = useState(false);
  const [errorEmail, setErrorEmail] = useState(false); // Estado para manejar errores
  const [errorPass, setErrorPass] = useState(false); // Estado para manejar errores
  const [errorPasswordText, setErrorPasswordText] = useState(''); // Estado para manejar errores
  const [isLoading, setIsLoading] = useState(false);
  const { expoPushToken } = useNotification();

  // Llamamos al hook que maneja la autenticación biométrica
  // Función para manejar el cambio de texto en el campo de email
  const handleEmailChange = (text: string) => {
    setEmail(text.trim());
    setErrorEmail(false); // Reiniciar el error de email al cambiar el texto
  };

  const handlePasswordChange = (text: string) => {
    setPassword(text);
    setErrorPass(false); // Reiniciar el error de contraseña al cambiar el texto
  };

  // cerrar modal de huella
  const onCloseModalFingerprint = async () => {
    // cancelar la autenticación
    await LocalAuthentication.cancelAuthenticate();
    setShowFingerprintModal(false);
  };

  // Función para manejar el evento de presionar el botón de iniciar sesión
  const handlePressLogin = async () => {
    // add login logic
    // set user
    setIsLoading(true);
    // quitamos espacios al inicio y al final del email y pasword
    const emailTrimmed = email.trim();
    const passwordTrimmed = password.trim();
    const login = await signin(emailTrimmed, passwordTrimmed);
    if (login.token) {
      const isAccountEnabled = login.user.enabled;
      if (!isAccountEnabled) {
        // redirigir a la pantalla de InactiveScreen
        router.dismissAll();
        router.replace('/(homeStack)/InactiveScreen');
        setIsLoading(false);
        return;
      }
      const lastEmail = await SecureStore.getItemAsync('userEmail');
      const currentEmail = login.user.email;
      if (lastEmail !== currentEmail) {
        // eliminar datos de sesión de secure store
        await removeSecureValues()
      }

      // guardar token en secure store
      await SecureStore.setItemAsync('userToken', login.token);
      await SecureStore.setItemAsync('userEmail', login.user.email);
      // Actualizar el Expo Push Token después de un login exitoso
      await updateExpoPushTokenIfNeeded(login.user.id, expoPushToken);

      router.dismissAll();
      router.replace({ pathname: '/(homeStack)/home', params: { email: email } });
    } else if (login.statusCode && login.statusCode === 404) {
      setErrorEmail(true);
    } else if (login.statusCode && login.statusCode === 409) {
      const errorMesage = login.message
      setErrorPass(true);
      setErrorPasswordText(errorMesage)
    }
    setIsLoading(false);
  };

  return (
    <>
      <PageBaseWithBackButton invert onGoBack={() => router.back()}>
        <LoginScreen
          disableBtn={!validateEmail(email) || password.length < 3 || isLoading}
          onSetEmail={handleEmailChange}
          onSetPassword={handlePasswordChange}
          onHandlePressLogin={handlePressLogin}
          errorEmail={errorEmail}
          errorPass={errorPass}
          errorPasswordText={errorPasswordText}
        />

        <FingerBottomModal
          showFingerprintModal={showFingerprintModal}
          onCloseModalFingerprint={onCloseModalFingerprint}
        />
        <StatusBar backgroundColor='#000000' barStyle='light-content' translucent={false} />
      </PageBaseWithBackButton>
      {(isLoading) && <LoadingScreen />}
    </>
  );
};

export default Login;
