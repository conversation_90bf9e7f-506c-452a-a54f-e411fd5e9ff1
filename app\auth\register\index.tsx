import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  StatusBar,
  View,
} from 'react-native';
import React, { useState } from 'react';
import PageBaseWithBackButton from '@/components/templates/PageBaseWithBackButton';
import { ThemedText } from '@/components/ui/ThemedText';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import { router } from 'expo-router';
import Input from '@/components/atoms/Input';
import validateEmail from '@/shared/validateEmail';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import useIsTablet from '@/hooks/useIsTablet';
import validateOnlyNumbers from '@/shared/validateOnlyNumbers';
import { findUserByEmail, generateOTPCode } from '@/services/userService';
import { findAdminByMemebershipNumber } from '@/services/adminService';
import IconTooltip from '@/components/molecules/IconTooltip';
import { Country } from '@/components/atoms/CountrySelecter';
import { parsePhoneNumberFromString } from 'libphonenumber-js';

type ErrorProps = {
  error: boolean;
  message?: string;
};

type ErrorsProps = {
  email: ErrorProps;
  fullName: ErrorProps;
  phone: ErrorProps;
  affiliationNumber: ErrorProps;
};
const initialErrorsState = {
  email: {
    error: false,
    message: undefined,
  },
  fullName: {
    error: false,
    message: undefined,
  },
  phone: {
    error: false,
    message: undefined,
  },
  affiliationNumber: {
    error: false,
    message: undefined,
  },
};

const Register = () => {
  const isTablet = useIsTablet();
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fullName, setFullName] = useState('');
  const [phone, setPhone] = useState('');
  const [affiliationNumber, setAffiliationNumber] = useState('');
  const [errors, setErrors] = useState<ErrorsProps>(initialErrorsState);
  const [countryCodes, setCountryCodes] = useState<Country | null>(null)

  const updateError = (
    error: boolean,
    field: keyof ErrorsProps,
    message: string | undefined,
  ) => {
    setErrors(prevErrors => ({
      ...prevErrors,
      [field]: { error, message },
    }));
  };

  const isValidPhoneNumber = (phone: string, countryCode: string): boolean => {
    try {
      const phoneNumber = parsePhoneNumberFromString(phone, countryCode as any);
      return phoneNumber?.isValid() || false;
    } catch {
      return false;
    }
  };

  const handleEmailChange = (text: string) => {
    updateError(false, 'email', undefined);
    const isValidEmail = validateEmail(text);
    if (!isValidEmail) {
      updateError(true, 'email', 'Ingresa un correo valido');
    }

    setEmail(text);
  };

  const handleAffiliationNumberChange = (affiliationNumber: string) => {
    updateError(false, 'affiliationNumber', undefined);
    setAffiliationNumber(affiliationNumber.toString());
  };

  const handleRegister = async () => {
    const isValidEmail = validateEmail(email);
    const isValidPhone = isValidPhoneNumber(phone, countryCodes?.code || 'MX');
    const isValidNumberAffiliation = true;
    if (!isValidEmail) {
      updateError(true, 'email', 'Ingresa un correo valido');
      return;
    }

    if (!isValidPhone) {
      updateError(true, 'phone', 'Número de teléfono inválido para el país seleccionado');
      return;
    }

    if (!isValidNumberAffiliation) {
      updateError(true, 'affiliationNumber', 'No existe ese número de afiliación');
      return;
    }

    setLoading(true);
    try {
      // Buscar usuario por correo electrónico
      const userData = await findUserByEmail(email);
      const adminData = await findAdminByMemebershipNumber(Number(affiliationNumber));

      if (adminData.statusCode === 404) {
        updateError(true, 'affiliationNumber', 'No existe ese número de afiliación');
        setLoading(false);
      }

      if (userData) {
        updateError(true, 'email', 'El correo ya está registrado');
        setLoading(false);
      }

      if (!userData && adminData.statusCode !== 404) {

        // Crear un código OTP
        const response = await generateOTPCode(email);

        if (response.statusCode === 200) {
          setLoading(false);
          if (!emailError && email) {
            router.push({
              pathname: '/auth/register/VerifyEmail',
              params: {
                email,
                fullName,
                phone,
                affiliationNumber,
                token: response.data.token,
                dialing_code: countryCodes?.callingCode || '52', // Default to Mexico if no country code is provided
                country_code: countryCodes?.code || 'MX', // Default to Mexico if no country code is provided
              },
            });
          } else {
            setEmailError(true);
          }
        } else {
          setLoading(false);
          updateError(true, 'email', 'Error al generar el código OTP');
        }
      }
    } catch (error) {
      setLoading(false);
      updateError(true, 'email', 'Error, por favor intenta más tarde');
      console.error('Error al querer buscar un usuario, buscar numero de afiliación o generar el OTP:', error);
    }
  };

  const handleInputNumber = (text: string) => {
    updateError(false, 'phone', undefined);
    const numericText = validateOnlyNumbers(text);
    setPhone(numericText);
  };

  const disableBtn =
    !email ||
    !fullName ||
    !phone ||
    !affiliationNumber ||
    errors.email.error ||
    errors.fullName.error ||
    errors.phone.error ||
    errors.affiliationNumber.error;

  return (
    <>
      <StatusBar backgroundColor='#ffffff' barStyle='dark-content' translucent={false} />
      <PageBaseWithBackButton>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={[
            {
              flex: 1,
            },
          ]}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 120 : 0}
        >
          <ScrollView
            contentContainerStyle={styles.container}
            bounces={false}

          >
            <View style={[styles.wrapper]}>
              <View style={styles.content}>
                <ThemedText
                  type='title'
                  lightColor='#232429'
                  style={{
                    width: isTablet ? '100%' : 342,
                  }}
                >
                  Completa la información para acceder a tu App
                </ThemedText>
                <Input
                  label='Correo electrónico'
                  value={email}
                  placeholder='Ingresa correo eléctrico'
                  placeholderTextColor='#9093A5'
                  icon={emailError ? 'alert-circle-outline' : undefined}
                  isError={errors.email.error}
                  errorText={errors.email.message}
                  keyboardType='email-address'
                  onChangeText={handleEmailChange}
                />
                <Input
                  label='Nombre completo'
                  value={fullName}
                  placeholder='Ingresa tu nombre completo'
                  placeholderTextColor='#9093A5'
                  isError={errors.fullName.error}
                  onChangeText={setFullName}
                />
                <Input
                  label='Teléfono'
                  value={phone}
                  placeholder='Ingresa tu teléfono'
                  placeholderTextColor='#9093A5'
                  keyboardType='number-pad'
                  returnKeyType='done'
                  isError={errors.phone.error}
                  errorText={errors.phone.message}
                  onChangeText={handleInputNumber}
                  maxLength={13}
                  setCountryCodes={setCountryCodes}
                  countryCodes={countryCodes ?? undefined}
                  isPhone
                />
                <Input
                  label='Número de afiliación'
                  value={affiliationNumber}
                  placeholder='Ingresa tu número de afiliación'
                  placeholderTextColor='#9093A5'
                  keyboardType='number-pad'
                  returnKeyType='done'
                  isError={errors.affiliationNumber.error}
                  errorText={errors.affiliationNumber.message}
                  onChangeText={handleAffiliationNumberChange}
                  onRenderRight={() => (
                    <IconTooltip
                      isError={errors.affiliationNumber.error}
                      text='El número de afiliación te lo compartirá tu empresa'
                    />
                  )}
                />
              </View>
            </View>
            <View
              style={{
                flex: 1,
                justifyContent: 'flex-end',
              }}
            >
              <PrimaryButton
                title='Registrarme'
                disable={disableBtn}
                onPress={handleRegister}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </PageBaseWithBackButton>
      {/* {showHelp && <HelpModalRegister closeModal={() => setShowHelp(false)} />} */}
      {loading && <LoadingScreen />}
    </>
  );
};

export default Register;

const styles = StyleSheet.create({
  wrapper: {
    justifyContent: 'space-between',
  },
  content: {
    gap: 24,
  },
  container: {
    flexGrow: 1,
    justifyContent: 'space-between',
    gap: 50,
    paddingBottom: Platform.OS === 'ios' ? 0 : 24,
    paddingHorizontal: 24,
  },
  centerContent: {
    justifyContent: 'center',
  },
});
