import { StyleSheet, Text, Platform, Animated } from 'react-native';
import React, { useEffect, useRef } from 'react';
import AlertCheck from '../icons/AlertCheck';

type Props = {
  text: string;
  timeToHide: number;
  isVisible: boolean;
  onSetShowAlert: (value: boolean) => void;
  onExecuteCallbackFinally?: () => void;
};

const AlertSucess = ({
  text,
  timeToHide,
  isVisible,
  onSetShowAlert,
  onExecuteCallbackFinally: onExecuteCallbackThen,
}: Props) => {
  const translateYAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        Animated.timing(translateYAnim, {
          toValue: 100, // Adjust this value as needed
          duration: 500,
          useNativeDriver: true,
        }).start(() => {
          onSetShowAlert(false);
          onExecuteCallbackThen?.(); // Execute additional function after hiding alert
        });
      }, timeToHide);

      return () => clearTimeout(timer);
    } else {
      Animated.timing(translateYAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }
  }, [translateYAnim, timeToHide, isVisible, onSetShowAlert, onExecuteCallbackThen]);

  if (!isVisible) {
    return null;
  }

  return (
    <Animated.View
      style={[styles.wrapper, { transform: [{ translateY: translateYAnim }] }]}
    >
      <AlertCheck />
      <Text style={styles.text}>{text}</Text>
    </Animated.View>
  );
};

export default AlertSucess;

const styles = StyleSheet.create({
  wrapper: {
    position: 'absolute',
    bottom: 0,
    backgroundColor: '#08A045',
    minHeight: Platform.OS === 'ios' ? 100 : 84,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    gap: 11,
    paddingVertical: 16,
  },
  text: {
    fontSize: 14,
    lineHeight: 16.94,
    color: '#ffffff',
    fontWeight: '500',
    fontFamily: 'Inter',
    textAlign: 'center',
  },
});
