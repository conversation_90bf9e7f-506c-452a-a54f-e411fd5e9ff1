import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import React, { useCallback, useState } from 'react';
import Feather from '@expo/vector-icons/Feather';
import { ThemedText } from '../ui/ThemedText';

type OptionItem = {
  value: string;
  label: string;
};

interface DropDownProps {
  data: OptionItem[];
  onChange: (item: OptionItem) => void;
  placeholder: string;
  value?: string; // Manejar valor
}

export default function Dropdown({
  data,
  onChange,
  placeholder,
  value, // Manejar valor
}: DropDownProps) {
  const [expanded, setExpanded] = useState(false);

  const toggleExpanded = useCallback(() => setExpanded(!expanded), [expanded]);

  const [internalValue, setInternalValue] = useState('');

  const displayValue = value || internalValue;

  const onSelect = useCallback(
    (item: OptionItem) => {
      onChange(item);
      if (!value) setInternalValue(item.label); // solo actualiza si es no controlado
      setExpanded(false);
    },
    [onChange, value],
  );
  return (
    <View>
      <TouchableOpacity
        style={styles.button}
        activeOpacity={0.8}
        onPress={toggleExpanded}
      >
        <ThemedText type='subtitle' lightColor={value ? '#000000' : '#9093A5'}>
          {displayValue || placeholder}
        </ThemedText>
        <Feather
          name={expanded ? 'chevron-up' : 'chevron-down'}
          size={24}
          color='#6F7280'
        />
      </TouchableOpacity>
      {expanded ? (
        <Modal visible={expanded} transparent>
          <TouchableWithoutFeedback onPress={() => setExpanded(false)}>
            <View style={styles.backdrop}>
              <View style={styles.modalContainer}>
                <View style={styles.options}>
                  <FlatList
                    keyExtractor={item => item.value}
                    data={data}
                    renderItem={({ item }) => (
                      <TouchableOpacity
                        activeOpacity={0.8}
                        style={styles.optionItem}
                        onPress={() => onSelect(item)}
                      >
                        <Text>{item.label}</Text>
                      </TouchableOpacity>
                    )}
                    ItemSeparatorComponent={() => (
                      <View style={styles.separator} />
                    )}
                  />
                </View>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      ) : null}
    </View>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    overflow: 'hidden',
  },
  optionItem: {
    height: 40,
    justifyContent: 'center',
  },
  separator: {
    height: 4,
  },
  options: {
    backgroundColor: 'white',
    width: '100%',
    padding: 10,
    borderRadius: 6,
    maxHeight: 250,
  },
  button: {
    height: 48,
    justifyContent: 'space-between',
    backgroundColor: '#fff',
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    padding: 12,
    borderRadius: 10,
    borderColor: '#C3C6D8',
    borderWidth: 1,
  },
});
