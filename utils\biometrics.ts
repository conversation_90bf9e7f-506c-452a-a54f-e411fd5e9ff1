import { Alert, Platform } from 'react-native';
import getBiometricMethodsTypes from '@/shared/getBiometricMethodsTypes';
import * as LocalAuthentication from 'expo-local-authentication';
import { AuthenticationType } from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { activateAuthenticateWithBiometrics } from '@/services/biometrics/biometricService';

interface BiometricAuthResult {
  success: boolean;
  message?: string;
  authenticationType?: AuthenticationType;
}

export const activateBiometricAuth = async (
  userUuid: string,
): Promise<BiometricAuthResult> => {
  const hasMethods = await getBiometricMethodsTypes();

  if (!hasMethods.length) {
    return {
      success: false,
      message: 'No hay métodos de autenticación soportados',
    };
  }

  const hasData = await LocalAuthentication.isEnrolledAsync();

  if (!hasData) {
    return {
      success: false,
      message: 'No hay datos de autenticación guardados',
    };
  }

  // Simular obtención del token

  try {
    if (Platform.OS === 'ios') {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Autenticación requerida',
      });
      if (!result.success) {
        return {
          success: false,
          message: 'Autenticación biométrica fallida',
        };
      }
    }
    const authBToken = await activateAuthenticateWithBiometrics(userUuid, true);
    await SecureStore.setItemAsync('authBToken', authBToken, {
      requireAuthentication: true,
      authenticationPrompt: 'Activar biometricos',
    });
    await SecureStore.setItemAsync('biometricsEnabled', 'true');
    let authenticationType;
    if (hasMethods.includes(AuthenticationType.FINGERPRINT)) {
      authenticationType = AuthenticationType.FINGERPRINT;
    } else if (hasMethods.includes(AuthenticationType.FACIAL_RECOGNITION)) {
      authenticationType = AuthenticationType.FACIAL_RECOGNITION;
    }

    return {
      success: true,
      authenticationType,
    };
  } catch (error) {
    return {
      success: false,
      message: `Error al guardar la autenticación biométrica: ${error}`,
    };
  }
};

export const showAlertBiometricsSettings = () => {
  Alert.alert(
    'No hay datos de autenticación guardados',
    'Por favor, active Face ID o el desbloqueo por huella en la configuración de su dispositivo.',
    [
      {
        text: 'Aceptar',
        style: 'default',
      },
    ],
  );
};

// alerta para decir que no se permitio el acceso a face id o huella
export const showAlertBiometricsNotAllowed = () => {
  Alert.alert(
    'Acceso denegado',
    'No se ha permitido el acceso a Face ID o huella. Por favor, active la autenticación biométrica en la configuración de su dispositivo.',
    [
      {
        text: 'Aceptar',
        style: 'default',
      },
    ],
  );
};
