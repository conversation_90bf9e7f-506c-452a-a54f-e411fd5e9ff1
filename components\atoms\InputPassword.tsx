import { TextInputProps } from 'react-native';
import React, { useState } from 'react';
import Input from './Input';

interface Props extends TextInputProps {
  label: string;
  errorText?: string;
  isError: boolean;
  lightLabel?: boolean;
  onChangeText: (text: string) => void;
}

const InputPassword = ({
  label,
  errorText,
  isError,
  lightLabel,
  onChangeText,
  ...props
}: Props) => {
  // mostrar u ocultar la contraseña
  const [showPassword, setShowPassword] = useState(false);

  return (
    <Input
      label={label}
      {...props}
      secureTextEntry={!showPassword}
      isError={isError}
      lightLabel={lightLabel}
      icon={
        isError
          ? 'alert-circle-outline'
          : showPassword
            ? 'eye-outline'
            : 'eye-off-outline'
      }
      onPressIcon={() => setShowPassword(!showPassword)}
      onChangeText={text => {
        onChangeText(text);
      }}
      errorText={errorText}
    />
  );
};

export default InputPassword;
