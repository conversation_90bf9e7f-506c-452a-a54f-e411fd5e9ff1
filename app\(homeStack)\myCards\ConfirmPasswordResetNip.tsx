import React, { useState } from 'react';
import PageBaseEnterPassword from '@/components/templates/PageBaseEnterPassword';
import { router, useLocalSearchParams } from 'expo-router';
import { useAppContext } from '../../../context/AppProvider';
import changeNip from '@/services/cards/changeNip';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import { validatePasswordToTransfer } from '@/services/transfersService';
import { useUserContext } from '@/context/UserContext';

const ConfirmPasswordResetNip = () => {
  const { showAlert, setShowChangeNipModal } = useAppContext();
  const { cardId, newNip } = useLocalSearchParams<{
    cardId: string;
    newNip: string;
  }>();
  const [password, setPassword] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { user } = useUserContext();

  const handleBack = () => router.back();

  const handleNext = async () => {
    setError(null);
    // todo: llamar a la api para validar la contraseña
    if (password.length === 0) {
      setError('Ingresa tu contraseña');
    }

    try {
      setIsLoading(true);
      const isValid = await validatePasswordToTransfer(user?.email!, password);

      if (isValid) {
        changeNip(cardId, newNip).then(() => {
          showAlert('EL NIP se ha cambiado correctamente recuerda acudir a un cajero para confirmar el cambio haciendo una consulta de saldo');
          setShowChangeNipModal(true);
          router.back();
        }).catch((err) => {
          setIsLoading(false);

          console.error(err);
        });
      } else {
        setError('Contraseña incorrecta, intenta de nuevo');
      }
      // setIsLoading(false);
      // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    } catch (error) {
      setError('Error al validar la contraseña');
      setIsLoading(false);
    }
  };

  return (
    <>
      <PageBaseEnterPassword
        title='Por seguridad, ingresa tu contraseña para validar tu identidad'
        titlePrimaryButton='Cambiar NIP'
        titleSecondaryButton='Cancelar'
        password={password}
        passwordError={error}
        loading={isLoading}
        onContinue={handleNext}
        onGoBack={handleBack}
        onSetPassword={setPassword}
        onSetPasswordError={setError}
      />
      {
        isLoading && <LoadingScreen />
      }
    </>
  );
};

export default ConfirmPasswordResetNip;
