import * as SecureStore from 'expo-secure-store';

export const removeSecureValues = async (): Promise<void> => {
  try {
    await SecureStore.deleteItemAsync('authBToken');
    await SecureStore.deleteItemAsync('userToken');
    await SecureStore.deleteItemAsync('biometricsEnabled');
    await SecureStore.deleteItemAsync('userEmail');
    await SecureStore.deleteItemAsync('expoPushToken');
  } catch (error) {
    console.error('Error al eliminar los valores seguros:', error);
  }
};
