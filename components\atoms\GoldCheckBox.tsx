import { StyleSheet, TouchableOpacity } from 'react-native';
import React from 'react';
import {
  gradientColor,
  gradietColorLocations,
} from '@/constants/GoldGradientColor';
import { LinearGradient } from 'expo-linear-gradient';
import Feather from '@expo/vector-icons/Feather';

type Props = {
  checked: boolean;
  onPress: () => void;
};

const GoldCheckBox = ({ checked, onPress }: Props) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.wrapper,
        {
          borderWidth: checked ? 0 : 1,
        },
      ]}
    >
      {checked && (
        <LinearGradient
          colors={gradientColor}
          locations={gradietColorLocations}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradientContent}
        >
          <Feather name='check' size={23} color='#ffffff' />
        </LinearGradient>
      )}
    </TouchableOpacity>
  );
};

export default GoldCheckBox;

const styles = StyleSheet.create({
  wrapper: {
    width: 32,
    height: 32,
    borderRadius: 6,
    borderColor: '#ACAFC2',
  },
  gradientContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
  },
});
