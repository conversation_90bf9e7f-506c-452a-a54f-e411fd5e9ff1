import { StyleSheet, View } from 'react-native';
import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import BottomModal from '../BottomModal';
import { ThemedText } from '../../ui/ThemedText';
import SecondaryButton from '../../atoms/SecondaryButton';

type Props = {
  closeModal: () => void;
};

const HelpModalRegister = ({ closeModal }: Props) => {
  return (
    <BottomModal isVisible={true} onClose={() => closeModal()}>
      <View style={styles.modalHelp}>
        <View style={styles.modalHeader}>
          <Ionicons name='close-circle' size={12} color='#D62D2D' />
          <View style={styles.helpTitle}>
            <ThemedText
              type='title'
              lightColor='#F4F5FB'
              style={{
                fontSize: 24,
                lineHeight: 24,
              }}
            >
              Datos incorrectos
            </ThemedText>
            <ThemedText lightColor='#F4F5FB'>
              Datos no válidos. Por favor, contáctenos en [<EMAIL>]
              para soporte.
            </ThemedText>
          </View>
        </View>
        <SecondaryButton
          width='100%'
          title='Necesito ayuda'
          onPress={() => closeModal()}
        />
      </View>
    </BottomModal>
  );
};

export default HelpModalRegister;

const styles = StyleSheet.create({
  modalHelp: {
    paddingHorizontal: 32,
    paddingTop: 8,
    paddingBottom: 32,
    width: '100%',
    alignItems: 'center',
    gap: 24,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  helpTitle: {
    gap: 24,
  },
});
