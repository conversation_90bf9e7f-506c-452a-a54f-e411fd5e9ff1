import * as React from "react"
import Svg, {
    SvgProps,
    Path,
    Defs,
    LinearGradient,
    Stop,
} from "react-native-svg"
const NotificationIcon = (props: SvgProps) => (
    <Svg
        width={30}
        height={30}
        fill="none"
        {...props}
    >
        <Path
            stroke="url(#a)"
            strokeMiterlimit={10}
            strokeWidth={2}
            d="M26.25 15c0-6.21-5.04-11.25-11.25-11.25S3.75 8.79 3.75 15 8.79 26.25 15 26.25 26.25 21.21 26.25 15Z"
        />
        <Path
            fill="url(#b)"
            d="M21.398 18.34c-.956-1.133-1.632-1.61-1.632-4.735 0-2.862-1.51-3.88-2.754-4.375a.668.668 0 0 1-.371-.391C16.423 8.12 15.813 7.5 15 7.5c-.814 0-1.425.621-1.642 1.34a.664.664 0 0 1-.37.39c-1.245.497-2.754 1.513-2.754 4.376 0 3.125-.677 3.601-1.633 4.734-.397.47-.038 1.348.655 1.348h11.485c.69 0 1.051-.88.656-1.348Zm-8.493 2.285a.234.234 0 0 0-.224.162c-.01.031-.014.064-.01.097.131 1.097 1.105 1.616 2.33 1.616 1.21 0 2.175-.536 2.323-1.613a.234.234 0 0 0-.235-.262h-4.184Z"
        />
        <Defs>
            <LinearGradient
                id="a"
                x1={3.75}
                x2={26.25}
                y1={15.001}
                y2={15.001}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
            <LinearGradient
                id="b"
                x1={8.437}
                x2={21.562}
                y1={15.001}
                y2={15.001}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
        </Defs>
    </Svg>
)
export default NotificationIcon
