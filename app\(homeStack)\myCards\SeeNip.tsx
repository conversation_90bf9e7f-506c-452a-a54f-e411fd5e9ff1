import React, { useEffect, useState } from 'react';
import SeeNipDetail from '@/components/organisms/cards/SeeNipDetail';
import { useLocalSearchParams } from 'expo-router';
import getPinCard from '@/services/cards/getPinCard';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import { DecryptAsync } from '@/shared/decrypted';

const SeeNip = () => {
  const { cardId } = useLocalSearchParams<{
    cardId: string;
  }>();

  const [pin, setPin] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // get pin card
  useEffect(() => {
    getPinCard(cardId).then((response) => {
      DecryptAsync(response).then((decrypted) => {
        setLoading(false);
        setPin(decrypted);
      });
    }).catch((err) => {
      setLoading(false);
    });
  }, [cardId, pin]);

  return (
    <>
      <SeeNipDetail pin={pin} cardId={cardId} />
      {loading && <LoadingScreen />}
    </>
  );
};

export default SeeNip;
