import * as SecureStore from 'expo-secure-store';
import { updateExpoToken } from '@/services/notifications/notificationService';

export async function updateExpoPushTokenIfNeeded(
  userId: string | null,
  expoPushToken: string | null,
) {
  if (!userId) return;
  if (!expoPushToken) return;

  const storedExpoPushToken = await SecureStore.getItemAsync('expoPushToken');

  if (expoPushToken !== storedExpoPushToken) {
    try {
      await updateExpoToken(userId, expoPushToken);
      await SecureStore.setItemAsync('expoPushToken', expoPushToken);
    } catch (error) {
      console.error('Failed to sync Expo Push Token:', error);
    }
  }
}
