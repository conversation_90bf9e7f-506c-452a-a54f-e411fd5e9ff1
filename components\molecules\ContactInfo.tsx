import { StyleSheet, Text, View, Linking } from 'react-native';
import PrimaryButton from '../atoms/PrimaryButton';

const ContactInfo = () => {
  const openWhatsApp = () => {
    const url = 'https://wa.me/522441462680';
    Linking.openURL(url).catch(err =>
      console.error('Error opening WhatsApp', err),
    );
  };
  const sendEmail = () => {
    const email = '<EMAIL>';
    const url = `mailto:${email}`;
    Linking.openURL(url).catch(err =>
      console.error('Error opening email client', err),
    );
  };

  return (
    <View style={styles.bottomContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>Contacto por WhatsApp</Text>
        <Text style={styles.info}>+52 244 146 2680</Text>
        <PrimaryButton title={'Enviar mensaje'} onPress={openWhatsApp} />
        <Text style={styles.title}>Correo electrónico</Text>
        <Text style={styles.info}><EMAIL></Text>
        <PrimaryButton title={'Enviar correo'} onPress={sendEmail} />
      </View>
    </View>
  );
};

export default ContactInfo;

const styles = StyleSheet.create({
  bottomContainer: {
    flexGrow: 1, // Permite que el contenido ocupe todo el espacio disponible
    backgroundColor: '#E6E8F4',
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  title: {
    fontSize: 24,
    color: '#000',
    fontFamily: 'Inter',
    lineHeight: 29.05,
    textRendering: 'geometricPrecision',
    fontWeight: 'bold',
    marginTop: 24,
    marginBottom: 12,
  },
  info: {
    fontSize: 14,
    color: '#000',
    fontFamily: 'Inter',
    lineHeight: 29.05,
    textRendering: 'geometricPrecision',
    fontWeight: '400',
    marginBottom: 14,
  },
});
