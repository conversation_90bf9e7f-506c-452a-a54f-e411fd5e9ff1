import { StyleSheet, View, StatusBar } from 'react-native';
import React, { useState } from 'react';
import PageBaseWithBackButton from '@/components/templates/PageBaseWithBackButton';
import { router } from 'expo-router';
import Input from '@/components/atoms/Input';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import { ThemedText } from '@/components/ui/ThemedText';
import useIsTablet from '@/hooks/useIsTablet';
import validateEmail from '@/shared/validateEmail';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import { findUserByEmail, generateOTPCode } from '@/services/userService';

const ResetPassWord = () => {
  // Hook para determinar si el dispositivo es una tablet
  const isTablet = useIsTablet();
  // Estado para almacenar el correo electrónico ingresado
  const [email, setEmail] = useState('');
  // Estado para manejar errores de correo electrónico
  const [emailError, setEmailError] = useState(false);
  // Estado para manejar el mensaje de error de correo electrónico
  const [emailErrorMessage, setEmailErrorMessage] = useState('Ingresa un correo valido');
  // Estado para manejar la pantalla de carga
  const [loading, setLoading] = useState(false);

  // Función para manejar el cambio de texto en el campo de correo electrónico
  const handleEmailChange = (text: string) => {
    if (emailError) setEmailError(false);
    setEmail(text.trim());
  };

  // Función para enviar la solicitud de restablecimiento de contraseña
  const submitResetRequest = async () => {
    const isValidEmail = validateEmail(email);
    if (!isValidEmail) {
      setEmailError(true);
      setEmailErrorMessage('Ingresa un correo válido');
      return;
    }
    setLoading(true);

    // Buscar usuario por correo electrónico
    const userData = await findUserByEmail(email);
    if (!userData) {
      setEmailError(true);
      setEmailErrorMessage('El correo electrónico no está registrado');
      setLoading(false);
      return;
    }
    // Crear un código OTP
    const response = await generateOTPCode(email);
    setLoading(false);
    if (response.statusCode === 200) {
      router.push({
        pathname: '/auth/resetPassword/VerifyEmail',
        params: { email, token: response.data.token },
      });
    } else {
      setEmailError(true);
      setEmailErrorMessage('Error al enviar el código, inténtalo de nuevo más tarde');
    }
  };

  // Deshabilitar el botón si el correo electrónico está vacío o hay un error
  const disableBtn = email === '' || emailError;

  return (
    <>
      <StatusBar backgroundColor='#ffffff' barStyle='dark-content' translucent={false} />
      <PageBaseWithBackButton onGoBack={() => router.back()}>
        <View style={styles.wrapper}>
          <View style={styles.content}>
            <View style={styles.contentHeader}>
              <ThemedText
                type='title'
                lightColor='#232429'
                style={{
                  width: isTablet ? '100%' : 342,
                }}
              >
                ¿Olvidaste tu contraseña?
              </ThemedText>
              <ThemedText type='subtitle' lightColor='#4A4B55'>
                Te enviaremos por correo electrónico un código para restablecer
                su contraseña
              </ThemedText>
            </View>
            <Input
              label='Correo electrónico'
              placeholder='Ingresa correo eléctrico'
              placeholderTextColor='#9093A5'
              icon={emailError ? 'alert-circle-outline' : undefined}
              isError={emailError}
              errorText={emailErrorMessage}
              keyboardType='email-address'
              onChangeText={handleEmailChange}
              returnKeyType='done'
            />
          </View>
          <PrimaryButton
            title='Enviar código'
            disable={disableBtn}
            onPress={submitResetRequest}
          // onPress={() => setShowHelp(true)}
          />
        </View>
      </PageBaseWithBackButton>
      {loading && <LoadingScreen />}
    </>
  );
};

export default ResetPassWord;

const styles = StyleSheet.create({
  wrapper: {
    justifyContent: 'space-between',
    flex: 1,
    paddingBottom: 10,
    paddingHorizontal: 24,
  },
  content: {
    gap: 24,
  },
  contentHeader: {
    gap: 8,
  },
});
