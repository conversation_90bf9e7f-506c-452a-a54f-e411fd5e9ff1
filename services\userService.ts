import api from './api/api';

interface CreateUserPayload {
  name: string;
  email: string;
  phone: number;
  membership_number: number;
  password: string;
  created_by: string;
  dialing_code?: string;
  country_code?: string;
}

interface VerifyOTPCodePayload {
  email: string;
  code: string;
  access: string;
}

interface ContactToTransfer {
  name: string;
  num_clabe: string;
  bank_institution: string;
  userId: string;
}

interface ChangePassword {
  email: string;
  oldPassword: string;
  newPassword: string;
}

export const createUser = async (data: CreateUserPayload) => {
  try {
    const response = await api.post('/user/app/create', data);
    return response.data;
  } catch (error: any) {
    console.error('Error al crear el usuario:', error?.response?.data || error);
    throw new Error(
      error?.response?.data?.message || 'Error al crear el usuario',
    );
  }
};

export const generateOTPCode = async (email: string) => {
  try {
    const response = await api.post('/otp/generate', { email });
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al crear el código otp:',
      error?.response?.data || error,
    );
    return {
      statusCode: error?.response?.status || 500,
      message: error?.response?.data?.message || 'Error al crear el código otp',
    };
  }
};

export const verifyOTPCode = async (data: VerifyOTPCodePayload) => {
  try {
    const response = await api.post('/otp/verify', data);
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al crear el código otp:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message || 'Error al crear el código otp',
    );
  }
};

export const findUserByEmail = async (email: string) => {
  try {
    const response = await api.get(`/user/findUserWithAdmin/${email}`);
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al buscar usuario por email:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message || 'Error al buscar usuario por email',
    );
  }
};

export const resetPassword = async (email: string, password: string) => {
  try {
    const response = await api.patch(`/user/resetPass/`, { email, password });
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al actualizar contraseña:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message || 'Error al buscar usuario por email',
    );
  }
};

export const changePassword = async (data: ChangePassword) => {
  try {
    const response = await api.patch(`/user/changePass/`, data);
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al actualizar contraseña:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message || 'Error al actualizar contraseña',
    );
  }
};

export const getContactsToTransfer = async (id?: string) => {
  try {
    const response = await api.get(`/contact/findByUser`, {
      params: { idUser: id },
    });
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al obtener los contactos:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message || 'Error al obtener los contactos',
    );
  }
};

export const saveContactsToTransfer = async (data: ContactToTransfer) => {
  try {
    const response = await api.post(`/contact/save`, data);
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al guardar contacto de transferencia:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message ||
        'Error al guardar contacto de transferencia',
    );
  }
};
