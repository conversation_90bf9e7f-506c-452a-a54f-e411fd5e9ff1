import { View } from 'react-native';
import React from 'react';
import { ThemedText } from '@/components/ui/ThemedText';

const NotificationsHeader = () => {
  return (
    <View
      style={{
        paddingHorizontal: 24,
        paddingTop: 24,
        paddingBottom: 48,
        gap: 24,
      }}
    >
      <ThemedText type='text24' lightColor='#F4F5FB'>
        Notificaciones
      </ThemedText>
      <ThemedText type='defaultSemiBold' lightColor='#F4F5FB'>
        Configura aquí los tipos de mensajes que deseas recibir, puede ser por
        correo electrónico o en tu aplicación.
      </ThemedText>
    </View>
  );
};

export default NotificationsHeader;
