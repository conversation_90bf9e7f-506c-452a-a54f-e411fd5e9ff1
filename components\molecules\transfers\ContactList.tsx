import { FlatList, StyleSheet, Text, View } from 'react-native';
import React, { useEffect } from 'react';
import ContactItem from './ContactItem';
import { Contact } from '@/app/(homeStack)/transfers';
import { useBankContext } from '@/context/BankContext';

// Componente principal que muestra la lista de transacciones
type Props = {
  contacts: Contact[];
  onDelete: (uuid: string) => void;
  isEnabledToTransfer?: boolean;
};
const ContactList = ({ contacts = [], onDelete, isEnabledToTransfer }: Props) => {
  const { fetchBanks } = useBankContext()
  useEffect(() => {
    fetchBanks()
  }, [fetchBanks])

  return (
    // Contenedor principal de la lista
    <View style={styles.bottomContainer}>
      <FlatList
        style={{ paddingHorizontal: 24 }}
        // contentContainerStyle={{ gap: 16 }}
        data={contacts} // Muestra solo las primeras 5 transacciones
        keyExtractor={item => item.uuid} // Función para extraer la clave única de cada elemento
        ItemSeparatorComponent={() => <View style={styles.separator} />} // Agrega un separador entre cada elemento
        ListHeaderComponent={() => (
          // Componente de cabecera de la lista
          <View style={styles.titleListContainer}>
            <Text style={styles.titleList}>Mis contactos</Text>
          </View>
        )}
        renderItem={({ item }) => (
          <ContactItem item={item} onDelete={() => onDelete(item.uuid)} isEnabledToTransfer={isEnabledToTransfer} />
        )} // Renderiza cada elemento de la lista
        removeClippedSubviews={false}
      />
    </View>
  );
};

export default ContactList;

// Estilos para los componentes
const styles = StyleSheet.create({
  bottomContainer: {
    backgroundColor: '#E6E8F4',
    flex: 1,
  },
  titleListContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 24,
  },
  titleList: {
    fontSize: 22,
    color: '#000',
    fontFamily: 'Inter',
    fontWeight: '600',
    lineHeight: 26.63,
  },
  whiteButton: {
    backgroundColor: '#E6E8F4',
    borderColor: '#000',
    borderWidth: 1,
    paddingVertical: 10.5,
    paddingHorizontal: 22.5,
    borderRadius: 8,
    height: 40,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  whiteButtonText: {
    color: '#000',
    fontSize: 16,
    fontFamily: 'Inter',
    fontWeight: '600',
    lineHeight: 19.36,
  },
  separator: {
    height: 1,
    backgroundColor: '#9093A5',
    marginVertical: 24,
  },
});
