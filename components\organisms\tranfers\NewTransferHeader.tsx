import { StyleSheet, Text, View } from 'react-native';
import React from 'react';

const NewTransferHeader = () => {
  return (
    <View style={{ gap: 18, padding: 24 }}>
      <View style={{ gap: 24 }}>
        <Text style={styles.title}>Transferencias</Text>
        <Text style={styles.subtitle}>Nuevo destinatario</Text>
      </View>
    </View>
  );
};

export default NewTransferHeader;

// Estilos para el componente
const styles = StyleSheet.create({
  title: {
    fontFamily: 'Inter',
    fontWeight: '400',
    fontSize: 24,
    lineHeight: 29,
    color: '#F4F5FB',
  },
  subtitle: {
    fontFamily: 'Inter',
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 19.46,
    color: '#F4F5FB',
    textRendering: 'geometricPrecision',
  },
  addCardText: {
    textAlign: 'left',
    width: '100%',
  },
  addCardContainer: {
    flexDirection: 'row',
    // justifyContent: 'center',
    // alignItems: 'center',
    // width: '100%',
    maxWidth: 144,
  },
});
