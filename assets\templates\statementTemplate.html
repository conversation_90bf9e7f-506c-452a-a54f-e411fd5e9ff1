<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Estado de Cuenta</title>
    <style>
      @page {
        size: A4 portrait;
        margin: 0;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', Arial, sans-serif;
        font-size: 11px;
        line-height: 1.4;
        color: #333;
        background: white;
      }

      .container {
        width: 210mm;
        min-height: 297mm;
        position: relative;
        page-break-after: always;
      }

      /* HEADER */
      .statement-header {
        position: relative;
        width: 100%;
        height: 110px;
        overflow: hidden;
        page-break-inside: avoid;
      }

      .header-golden-block {
        position: absolute;
        top: 0;
        left: 0;
        width: auto;
        height: 93px;
        object-fit: cover;
        z-index: 2;
      }

      .header-pattern {
        position: absolute;
        top: 0;
        right: 0;
        width: calc(100% - 150px);
        object-fit: cover;
        z-index: 1;
      }

      .company-address {
        position: absolute;
        bottom: 0;
        right: 7mm;
        width: 290px;

        color: #000;
        text-align: right;
        font-family: Inter;
        font-size: 8px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        text-align: right;
        z-index: 4;
      }

      /* COMPANY INFO */
      .company-info {
        padding: 15px 30px;
        position: relative;
      }

      .company-info::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 30px;
        right: 30px;
        width: (100% - 60px);
        height: 2px;
        background-color: #d6d8e8;
      }

      .company-details {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .company-name {
        color: #000;
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 5px;
      }

      .account-summary {
        display: flex;
        flex-direction: column;
        gap: 5px;
        font-size: 10px;
      }

      .account-summary .summary-row {
        display: flex;
        justify-content: flex-start;
        gap: 40px;
        /* min-width: 180px;  */
      }

      .account-summary .label {
        width: 60px;
        flex-shrink: 0;
        color: #000;
        font-family: Inter;
        font-size: 8px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      .account-summary .value {
        text-align: left;
        color: #333;
        font-family: Inter;
        font-size: 8px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      /* CONTENT */
      .content {
        padding: 30px;
      }

      .section {
        margin-bottom: 30px;
      }

      .section-title {
        color: #000;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 20px;
        padding-bottom: 8px;
        border-bottom: 2px solid #d6d8e8;
      }

      /* INFORMACIÓN GENERAL */
      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 20px;
      }

      .info-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
      }

      .info-item .label {
        color: #000;
        font-family: Inter;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      .info-item .value {
        color: #333;
        font-family: Inter;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      /* INFORMACIÓN FINANCIERA */
      .financial-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 20px;
      }

      .financial-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
      }

      .financial-item .label {
        color: #000;
        font-family: Inter;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      .financial-item .value {
        color: #333;
        font-family: Inter;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      /* DETALLE DE CARGOS */
      .charges-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 20px;
      }

      .charge-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
      }

      .charge-item .label {
        color: #000;
        font-family: Inter;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      .charge-item .value {
        color: #333;
        font-family: Inter;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      /* TABLA DE MOVIMIENTOS */
      .movements-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        margin-top: 20px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .movements-table thead {
        background: linear-gradient(
          90deg,
          #dcb992 1.9%,
          #dab890 24.9%,
          #d5b289 45.9%,
          #cca87c 68.4%,
          #bf9b6b 97.51%,
          #af8b56 99.77%,
          #af8b55 99.85%,
          #e9d6b8 100%
        );
      }

      .movements-table th {
        background: transparent;
        color: #000;
        font-family: Inter;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        text-align: left;
        padding: 12px 15px;
        border: none;
        border-right: none;
        border-left: none;
        margin: 0;
        white-space: nowrap;
      }

      .movements-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #f0f0f0;
        color: #333;
        font-family: Inter;
        font-size: 10px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      .movements-table tr:nth-child(even) {
        background: #fafafa;
      }

      .movements-table tr:hover {
        background: #f5f5f5;
      }

      .currency {
        font-weight: bold;
      }

      .empty-row {
        color: #999;
        font-style: italic;
        text-align: center;
      }

      /* FOOTER */
      .footer {
        position: absolute;
        bottom: 0;
        width: 100%;
        background: #000;
        color: white;
        text-align: center;
        padding: 3mm;
        font-size: 8px;
      }

      /* PAGINATION */
      .page-number {
        position: absolute;
        top: 10mm;
        right: 10mm;
        font-size: 8px;
        color: #666;
      }
    </style>
  </head>

  <body>
    <div class="container">
      <!-- Header  -->
      <header class="statement-header">
        <img
          src="/header-golden-block.svg"
          alt="Bloque dorado Convenia"
          class="header-golden-block"
        />

        <img
          src="/header-pattern.svg"
          alt="Estado de cuenta"
          class="header-pattern"
        />

        <p class="company-address">
          CONVENIA S.A.P.I de C.V. S.O.F.O.M. ENR Berna 11, Lomas de
          Angelópolis, San Andrés Cholula, Puebla. C.P. 72930
        </p>
      </header>

      <!-- Company Info -->
      <div class="company-info">
        <div class="company-details">
          <div>
            <div class="company-name">{{denominacionSocial}}</div>
          </div>
          <div class="account-summary">
            <div class="summary-row">
              <span class="label">Período:</span>
              <span class="value">{{fecha}}</span>
            </div>
            <div class="summary-row">
              <span class="label">CLABE:</span>
              <span class="value">{{numeroClabe}}</span>
            </div>
            <div class="summary-row">
              <span class="label">RFC:</span>
              <span class="value">{{rfc}}</span>
            </div>
            <div class="summary-row">
              <span class="label">No. de cuenta:</span>
              <span class="value">{{numeroCuenta}}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="content">
        <!-- Información General -->
        <div class="section">
          <div class="section-title">Información general</div>
          <div class="info-grid">
            <div>
              <div class="info-item">
                <span class="label">Nombre o razón social:</span>
                <span class="value">{{nombreComercial}}</span>
              </div>
              <div class="info-item">
                <span class="label">Período:</span>
                <span class="value">{{fecha}}</span>
              </div>
              <div class="info-item">
                <span class="label">CLABE:</span>
                <span class="value">{{numeroClabe}}</span>
              </div>
            </div>
            <div>
              <div class="info-item">
                <span class="label">No. de Cuenta:</span>
                <span class="value">{{numeroCuenta}}</span>
              </div>
              <div class="info-item">
                <span class="label">RFC:</span>
                <span class="value">{{rfc}}</span>
              </div>
              <div class="info-item">
                <span class="label">Dirección:</span>
                <span class="value">{{direccionFiscal}}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Información Financiera -->
        <div class="section">
          <div class="section-title">Información financiera</div>
          <div class="financial-grid">
            <div>
              <div class="financial-item">
                <span class="label">Saldo anterior:</span>
                <span class="value currency">{{saldoAnterior}}</span>
              </div>
              <div class="financial-item">
                <span class="label">Depósitos/abonos:</span>
                <span class="value currency">{{depositos}}</span>
              </div>
              <div class="financial-item">
                <span class="label">Retiros/Cargos:</span>
                <span class="value currency">{{retiros}}</span>
              </div>
            </div>
            <div>
              <div class="financial-item">
                <span class="label">Saldo final:</span>
                <span class="value currency">{{saldoFinal}}</span>
              </div>
              <div class="financial-item">
                <span class="label">Promedio saldos diarios:</span>
                <span class="value currency">{{promedioAbonos}}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Detalle de Cargos -->
        <div class="section">
          <div class="section-title">Detalle de cargos</div>
          <div class="charges-grid">
            <div>
              <div class="charge-item">
                <span class="label">Comisiones cobradas:</span>
                <span class="value">No aplica</span>
              </div>
              <div class="charge-item">
                <span class="label">Impuestos retenidos:</span>
                <span class="value">No aplica</span>
              </div>
            </div>
            <div>
              <div class="charge-item">
                <span class="label">Cargos objetados:</span>
                <span class="value">No aplica</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Movimientos Realizados -->
        <div class="section">
          <div class="section-title">Movimientos realizados</div>

          <table class="movements-table">
            <thead>
              <tr>
                <th>Fecha operación</th>
                <th>Fecha liquidación</th>
                <th>Concepto</th>
                <th>Clave Rastreo</th>
                <th>Cargo</th>
                <th>Abono</th>
                <th>Saldo</th>
              </tr>
            </thead>
            <tbody>
              {{movimientos}}
            </tbody>
          </table>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <div>El equipo de CONVENIA</div>
        <div>© 2025. DERECHOS RESERVADOS. CONVENIA</div>
      </div>
    </div>
  </body>
</html>
