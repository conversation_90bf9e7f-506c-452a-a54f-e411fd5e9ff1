import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import React, { useEffect, useRef } from 'react';
import LoginForm from './LoginForm';
import useIsTablet from '@/hooks/useIsTablet';
import LoginIconConvenia from '@/components/icons/LoginIconConvenia';

type Props = {
  disableBtn: boolean;
  onSetEmail: (email: string) => void;
  onSetPassword: (password: string) => void;
  onHandlePressLogin: () => void;
  errorEmail: boolean;
  errorPass: boolean;
  errorPasswordText: string;
};

const LoginScreen = ({
  disableBtn,
  onSetEmail,
  onSetPassword,
  onHandlePressLogin,
  errorEmail,
  errorPass,
  errorPasswordText,
}: Props) => {
  const isTablet = useIsTablet(); // Hook para determinar si el dispositivo es una tablet

  // Crear una referencia para el ScrollView
  const scrollViewRef = useRef<ScrollView>(null);

  // useEffect para activar o desactivar el scroll en el ScrollView cuando el teclado se muestra o se oculta
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        scrollViewRef.current?.scrollTo({ y: 100, animated: true }); // Desplazar al final cuando el teclado se muestra
      },
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        scrollViewRef.current?.scrollTo({ y: 0, animated: false }); // Desplazar al inicio cuando el teclado se oculta
      },
    );

    // Limpiar los listeners cuando el componente se desmonte
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          justifyContent: 'center',
          paddingHorizontal: 24,
        }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps='handled'
        ref={scrollViewRef}
        bounces={false}
      >
        <View
          style={[
            styles.container,
            {
              paddingTop: isTablet ? 0 : 0,
              gap: isTablet ? 50 : 42,
            },
          ]}
        >
          <LoginIconConvenia style={styles.logo} />
          <LoginForm
            disableBtn={disableBtn}
            onSetEmail={onSetEmail}
            onSetPassword={onSetPassword}
            onHandlePressLogin={onHandlePressLogin}
            errorEmail={errorEmail}
            errorPass={errorPass}
            errorPasswordText={errorPasswordText}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default LoginScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  logo: {
    width: 390,
    height: 149,
    objectFit: 'contain',
  },

  btnContainer: {
    gap: 24,
    height: 100,
    alignItems: 'center',
  },
  welcome: {
    color: 'white',
    fontSize: 24,
    marginBottom: 20,
  },
});
