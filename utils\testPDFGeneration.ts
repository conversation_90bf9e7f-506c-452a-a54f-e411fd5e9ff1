import {
  generateStatementPDFFromTransactions,
  Transaction,
  AccountInfo,
} from './generateStatementPDF';

// Datos de prueba
const mockTransactions: Transaction[] = [
  {
    id: '1',
    date: '2025-01-15T10:00:00Z',
    dateFormatted: '15/01/2025',
    description: 'De<PERSON><PERSON><PERSON> inicial',
    amount: '+$1,000.00',
    type: 'creditor',
    key: 'DEP001',
  },
  {
    id: '2',
    date: '2025-01-14T14:30:00Z',
    dateFormatted: '14/01/2025',
    description: 'Transferencia recibida',
    amount: '+$500.00',
    type: 'creditor',
    key: 'TRF001',
  },
  {
    id: '3',
    date: '2025-01-13T09:15:00Z',
    dateFormatted: '13/01/2025',
    description: 'Pago de servicios',
    amount: '-$150.00',
    type: 'debtor',
    key: 'PAY001',
  },
  {
    id: '4',
    date: '2025-01-12T16:45:00Z',
    dateFormatted: '12/01/2025',
    description: 'Co<PERSON>ra en línea',
    amount: '-$75.50',
    type: 'debtor',
    key: 'PUR001',
  },
  {
    id: '5',
    date: '2025-01-11T11:20:00Z',
    dateFormatted: '11/01/2025',
    description: 'Transferencia enviada',
    amount: '-$200.00',
    type: 'debtor',
    key: 'TRF002',
  },
];

const mockAccountInfo: AccountInfo = {
  name: 'Usuario de Prueba',
  email: '<EMAIL>',
  phone: '+52 55 1234 5678',
  accountBalance: '$1,074.50',
  accountNumberConvenia: '**********',
  accountNumberTransfer: '0**********1234567',
  membershipNumber: 'MEM123456',
  companyAlias: 'Empresa de Prueba S.A.',
  admin: {
    companyName: 'Empresa de Prueba S.A. de C.V.',
    rfc: 'EPR123456789',
  },
};

// Función de prueba
export const testPDFGeneration = async (): Promise<void> => {
  try {
    console.log('🧪 Iniciando prueba de generación de PDF...');
    console.log('📸 Cargando imágenes del header...');

    await generateStatementPDFFromTransactions(
      mockAccountInfo,
      mockTransactions,
      true,
    );

    console.log('✅ PDF generado exitosamente con imágenes del header!');
  } catch (error) {
    console.error('❌ Error en la prueba de PDF:', error);
    throw error;
  }
};

// Función para probar con datos vacíos
export const testPDFGenerationEmpty = async (): Promise<void> => {
  try {
    console.log('🧪 Iniciando prueba de generación de PDF con datos vacíos...');

    await generateStatementPDFFromTransactions(mockAccountInfo, [], true);

    console.log('✅ PDF con datos vacíos generado exitosamente!');
  } catch (error) {
    console.error('❌ Error en la prueba de PDF con datos vacíos:', error);
    throw error;
  }
};

// Función para probar con muchos datos (paginación)
export const testPDFGenerationPagination = async (): Promise<void> => {
  try {
    console.log('🧪 Iniciando prueba de generación de PDF con paginación...');

    // Crear 20 transacciones para probar la paginación
    const manyTransactions: Transaction[] = [];
    for (let i = 1; i <= 20; i++) {
      manyTransactions.push({
        id: `${i}`,
        date: `2025-01-${String(i).padStart(2, '0')}T10:00:00Z`,
        dateFormatted: `${String(i).padStart(2, '0')}/01/2025`,
        description: `Transacción ${i}`,
        amount: i % 2 === 0 ? `+$${i * 10}.00` : `-$${i * 5}.00`,
        type: i % 2 === 0 ? 'creditor' : 'debtor',
        key: `TXN${String(i).padStart(3, '0')}`,
      });
    }

    await generateStatementPDFFromTransactions(
      mockAccountInfo,
      manyTransactions,
      true,
    );

    console.log('✅ PDF con paginación generado exitosamente!');
  } catch (error) {
    console.error('❌ Error en la prueba de PDF con paginación:', error);
    throw error;
  }
};
