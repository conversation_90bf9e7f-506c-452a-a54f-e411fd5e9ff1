import React, { useState, useEffect } from 'react';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import { router, useFocusEffect } from 'expo-router';
import MenuHeader from '@/components/molecules/home/<USER>';
import MenuBody from '@/components/organisms/home/<USER>';
import { useAppContext } from '../../../../context/AppProvider';
import * as SecureStore from 'expo-secure-store';
import AlertSucess from '@/components/atoms/AlertSucess';
import { AuthenticationType } from 'expo-local-authentication';
import { getBiometricPreferences, updateBiometricPreferences } from '@/services/biometrics/biometricService';
import { useUserContext } from '@/context/UserContext';
import { activateBiometricAuth } from '@/utils/biometrics';
import * as LocalAuthentication from 'expo-local-authentication';
import CustomAlert from '@/components/ui/CustomAlert';

const Menu = () => {
  const { user } = useUserContext();
  const [biometricsEnabled, setBiometricsEnabled] = useState(false);
  const { showSuccessAlert, setShowSuccessAlert } = useAppContext();
  const [showAlertChangePassword, setShowAlertChangePassword] = useState(false);
  const [showSuccesAlertBiometrics, setShowSuccesAlertBiometrics] = useState(false);
  const [showBiometricsSettingsAlert, setShowBiometricsSettingsAlert] = useState(false);
  const [showBiometricsNotAllowedAlert, setShowBiometricsNotAllowedAlert] = useState(false);

  const [messageSuccessBiometrics, setMessageSuccessBiometrics] = useState('');

  // Obtener las preferencias biométricas al montar el componente
  useEffect(() => {
    const fetchBiometricPreferences = async () => {
      if (user?.id) {
        try {
          const preferences = await getBiometricPreferences(user?.id);
          // Verificar si el usuario tiene habilitada la biometría en las preferencias de la base de datos
          const userBiometricsEnabled = (preferences.fingerprint_enabled || preferences.face_id_enabled) ?? false;
          // Verificar si la biometría está habilitada en el almacenamiento seguro
          const isBiometricEnabledStore = await SecureStore.getItemAsync('biometricsEnabled');
          // Determinar si la biometría está habilitada
          // en función de las preferencias del usuario y el almacenamiento seguro
          const isBiometricEnabled: boolean = isBiometricEnabledStore === 'true' && userBiometricsEnabled;

          // si isBiometricEnabled es true, guardamos el valor en el estado, esto se usara para e vitar que el usuario tenga que activar la biometría de nuevo al dar click en el botón de activar biometría
          setBiometricsEnabled(isBiometricEnabled);
        } catch (error) {
          console.error('Error fetching biometric preferences:', error);
        }
      }
    };

    fetchBiometricPreferences();
  }, [user?.id]);

  const handleOnCloseSession = async () => {
    if (!user?.id) return;
    // eliminar datos de sesión

    // // seteamos a false biometricService
    // await activateAuthenticateWithBiometrics(user.id, false);

    // eliminar datos de sesión de secure store
    // await removeSecureValues()
    await SecureStore.deleteItemAsync('userToken');


    router.dismissTo('/');
  };

  const handleActiveBiometrics = async () => {
    setMessageSuccessBiometrics('');
    if (!user?.id) return;
    const resultBiometrics = await activateBiometricAuth(user.id);
    if (!resultBiometrics.success) {
      if (resultBiometrics.message === 'No hay datos de autenticación guardados') {
        setShowBiometricsSettingsAlert(true);
      }
      if (resultBiometrics.message === 'Autenticación biométrica fallida') {
        setShowBiometricsNotAllowedAlert(true);
      }
      return;
    }
    // actualizamos el mensaje de éxito según el tipo de autenticación
    if (resultBiometrics.authenticationType === AuthenticationType.FINGERPRINT) {
      setMessageSuccessBiometrics('El ingreso con la huella se ha activado correctamente');
      await updateBiometricPreferences(user?.id!, {
        fingerprint_enabled: true,
      });
    } else if (resultBiometrics.authenticationType === AuthenticationType.FACIAL_RECOGNITION) {
      setMessageSuccessBiometrics('El ingreso con la Face ID se ha activado correctamente');
      await updateBiometricPreferences(user?.id!, {
        face_id_enabled: true,
      });
    } else {
      setMessageSuccessBiometrics('El ingreso con la autenticación biométrica se ha activado correctamente');
    }
    setBiometricsEnabled(true);
    setShowSuccesAlertBiometrics(true);
  };

  const handlePressActiveBiometrics = async () => {
    // verificamos si hay biometricos activados en el dispositivo
    const hasHardwareAsync = await LocalAuthentication.hasHardwareAsync();
    const isEnrolledAsync = await LocalAuthentication.isEnrolledAsync();
    if (!hasHardwareAsync || !isEnrolledAsync) {
      setShowBiometricsSettingsAlert(true);
      // borramos los datos de biometricos del secure store
      await SecureStore.deleteItemAsync('biometricsEnabled');
      await SecureStore.deleteItemAsync('authBToken');
      setBiometricsEnabled(false);
      return;
    }
    // Verificar si la biometría ya está habilitada, mostrar un mensaje inidicando que ya está activa
    if (biometricsEnabled) {
      setMessageSuccessBiometrics('La autenticación biométrica ya está activada');
      setShowSuccesAlertBiometrics(true);
    } else {
      // Si no está habilitada, proceder a activarla
      await handleActiveBiometrics();
    }
  }

  // mostrar alerta de éxito al cambiar la contraseña
  useFocusEffect(() => {
    if (showSuccessAlert) {
      setShowAlertChangePassword(true);
      setShowSuccessAlert(false);
    }
  });

  return (
    <>
      <PageBaseBWReturnButton
        onGoBack={() => router.back()}
        topChildren={<MenuHeader />}
        bottomChildren={
          <MenuBody
            onCloseSession={handleOnCloseSession}
            onToggleBiometricts={handlePressActiveBiometrics}
          />
        }
      />

      <AlertSucess
        text='La contraseña se ha cambiado correctamente'
        timeToHide={2000}
        isVisible={showAlertChangePassword}
        onSetShowAlert={setShowAlertChangePassword}
      />
      <AlertSucess
        text={messageSuccessBiometrics}
        timeToHide={2000}
        isVisible={showSuccesAlertBiometrics}
        onSetShowAlert={setShowSuccesAlertBiometrics}
      />

      <CustomAlert
        visible={showBiometricsSettingsAlert}
        title="No hay datos de autenticación guardados"
        message="Por favor, active Face ID o el desbloqueo por huella en la configuración de su dispositivo."
        onClose={() => setShowBiometricsSettingsAlert(false)}
        buttonText="Entendido"
        showCancelButton={false}
      />

      <CustomAlert
        visible={showBiometricsNotAllowedAlert}
        title="Acceso denegado"
        message="No se ha permitido el acceso a Face ID o huella. Por favor, active la autenticación biométrica en la configuración de su dispositivo."
        onClose={() => setShowBiometricsNotAllowedAlert(false)}
        buttonText="Entendido"
        showCancelButton={false}
      />
    </>
  );
};

export default Menu;
