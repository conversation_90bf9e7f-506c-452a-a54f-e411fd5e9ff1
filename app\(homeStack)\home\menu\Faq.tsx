import { View, Text, StyleSheet } from 'react-native';
import React from 'react';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import { router } from 'expo-router';
import FaqInfo from '@/components/molecules/FaqInfo';

const Faq = () => {
  return (
    <PageBaseBWReturnButton
      onGoBack={() => router.back()}
      topChildren={
        <View style={styles.topContainer}>
          <View style={styles.header}>
            <Text style={styles.title}>Preguntas frecuentes</Text>
          </View>
        </View>
      }
      bottomChildren={<FaqInfo />}
    />
  );
};

export default Faq;

const styles = StyleSheet.create({
  topContainer: {
    backgroundColor: '#000',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    color: '#F4F5FB',
    fontFamily: 'Inter',
    lineHeight: 29.05,
    textRendering: 'geometricPrecision',
    fontWeight: '400',
    marginTop: 24,
  },
  header: {
    paddingHorizontal: 24,
  },
});
