import api from '../api/api';

// Servicio para obtener las preferencias de notificaciones
export const getNotificationPreferences = async (idUser: string) => {
  try {
    const response = await api.get(`/user-notifications/preferences/${idUser}`);
    if (response.data.statusCode === 200) {
      return response.data.data.notificationPreferences;
    }
  } catch (error: any) {
    console.error(
      'Error fetching notification preferences:',
      error?.response?.data || error,
    );
    throw new Error('Failed to fetch notification preferences');
  }
};

// Servicio para actualizar las preferencias de notificaciones
export const updateNotificationPreferences = async (
  idUser: string,
  preferences: {
    receive_transaction_notifications?: boolean;
    receive_reminder_notifications?: boolean;
  },
) => {
  try {
    const response = await api.put(
      `/user-notifications/preferences/${idUser}`,
      preferences,
    );
    return response.data;
  } catch (error: any) {
    console.error(
      'Error updating notification preferences:',
      error?.response?.data || error,
    );
    throw new Error('Failed to update notification preferences');
  }
};

// Servicio para actualizar el Expo Push Token del usuario
export const updateExpoToken = async (
  idUser: string,
  expoPushToken: string,
) => {
  try {
    const response = await api.patch(
      `/user-notifications/preferences/${idUser}/expo-push-token`,
      { expoPushToken },
    );
    return response.data;
  } catch (error: any) {
    console.error(
      'Error updating expo push token:',
      error?.response?.data || error,
    );
    throw new Error('Failed to update expo push token');
  }
};
