import * as React from "react"
import Svg, {
    SvgProps,
    Path,
    Defs,
    LinearGradient,
    Stop,
} from "react-native-svg"
const MenuIcon = (props: SvgProps) => (
    <Svg
        width={26}
        height={26}
        fill="none"
        {...props}
    >
        <Path
            fill="url(#a)"
            d="M8 6.73a1.25 1.25 0 0 0 0 2.5h10a1.25 1.25 0 0 0 0-2.5H8Zm-1.25 6.272A1.25 1.25 0 0 1 8 11.752h10a1.25 1.25 0 0 1 0 2.5H8a1.25 1.25 0 0 1-1.25-1.25ZM8 16.773a1.25 1.25 0 0 0 0 2.5h10a1.25 1.25 0 0 0 0-2.5H8Z"
        />
        <Path
            fill="url(#b)"
            fillRule="evenodd"
            d="M25.5 13c0 6.904-5.596 12.5-12.5 12.5S.5 19.904.5 13 6.096.5 13 .5 25.5 6.096 25.5 13ZM23 13a10 10 0 1 1-20 0 10 10 0 0 1 20 0Z"
            clipRule="evenodd"
        />
        <Defs>
            <LinearGradient
                id="a"
                x1={6.75}
                x2={19.25}
                y1={13.002}
                y2={13.002}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
            <LinearGradient
                id="b"
                x1={0.5}
                x2={25.5}
                y1={13.001}
                y2={13.001}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
        </Defs>
    </Svg>
)
export default MenuIcon
