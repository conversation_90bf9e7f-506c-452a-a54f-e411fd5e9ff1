// FILE: CustomSwitch.tsx
import React from 'react';
import { Switch, SwitchProps } from 'react-native';

interface CustomSwitchProps extends SwitchProps {
  isEnabled: boolean;
  toggleSwitch: () => void;
}

const CustomSwitch: React.FC<CustomSwitchProps> = ({
  isEnabled,
  toggleSwitch,
  ...props
}) => {
  return (
    <Switch
      trackColor={{ false: '#0000007b', true: '#08a04572' }}
      thumbColor={isEnabled ? '#08A045' : '#ffffff'}
      ios_backgroundColor={isEnabled ? undefined : '#0000007b'}
      onValueChange={toggleSwitch}
      value={isEnabled}
      {...props}
    />
  );
};

export default CustomSwitch;
