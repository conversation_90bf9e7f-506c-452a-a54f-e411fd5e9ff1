import { useState, useRef } from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Animated,
} from 'react-native';
import PrimaryButton from '../atoms/PrimaryButton';
import { router } from 'expo-router';
import ArrowDown from '../icons/ArrowDown';

const sections = [
  {
    id: 1,
    title: '¿Qué es Convenia Wallet?',
    content:
      'Convenia Wallet es una tarjeta de crédito que te brinda control, flexibilidad y beneficios exclusivos para que administres tus finanzas personales o empresariales de forma simple y segura.',
  },
  {
    id: 2,
    title: '¿Dónde puedo usar mi tarjeta Convenia Wallet?',
    content:
      'Puedes utilizarla en cualquier comercio físico u online que acepte tarjetas de crédito, tanto en México como en el extranjero.',
  },
  {
    id: 3,
    title: '¿Cómo puedo consultar mi saldo y movimientos?',
    content:
      'Desde la app de Convenia Wallet podrás ver tu saldo, movimientos, así como descargar tu estado de cuenta.',
  },
  {
    id: 4,
    title: '¿Qué pasa si pierdo mi tarjeta?',
    content:
      'Puedes bloquearla de inmediato desde la app. Posteriormente podrás solicitar el reemplazo desde la sección de aclaraciones de la aplicación.',
  },
  {
    id: 5,
    title: '¿La tarjeta incluye protección contra fraudes?',
    content:
      'Sí. Contamos con tecnología de seguridad avanzada y protección contra cargos no reconocidos, siempre que se reporten dentro del plazo establecido.',
  },
  {
    id: 6,
    title: '¿Puedo usar Convenia Wallet en el extranjero?',
    content:
      'Sí, la tarjeta funciona internacionalmente en todos los establecimientos que acepten tarjetas de crédito.',
  },
  {
    id: 7,
    title: '¿Qué debo hacer si identifico un cargo no reconocido?',
    content:
      'Debes reportarlo de inmediato a través de nuestro modulo de aclaraciones dentro de la aplicación. Iniciaremos una investigación y, de ser procedente, se hará la aclaración correspondiente.',
  },
  {
    id: 8,
    title: '¿Cómo contacto al servicio al cliente?',
    content:
      'Puedes comunicarte con nosotros a través del boton que redirige a whatsapp en la app o por correo electrónico.',
  },
];

const FaqInfo = () => {
  const [expandedSections, setExpandedSections] = useState<{
    [key: number]: boolean;
  }>({});
  const [isNavigating, setIsNavigating] = useState(false);

  // Crear un objeto para almacenar las animaciones de rotación para cada sección
  const rotationAnimations = useRef<{
    [key: number]: Animated.Value;
  }>({});

  // Inicializar las animaciones para cada sección si no existen
  sections.forEach(section => {
    if (!rotationAnimations.current[section.id]) {
      rotationAnimations.current[section.id] = new Animated.Value(0);
    }
  });

  const toggleSection = (id: number) => {
    // Actualizar el estado de expandedSections
    setExpandedSections(prev => {
      const newState = {
        ...prev,
        [id]: !prev[id],
      };

      // Iniciar la animación
      Animated.timing(rotationAnimations.current[id], {
        toValue: newState[id] ? 1 : 0,
        duration: 150, // duración de la animación en ms
        useNativeDriver: true,
      }).start();

      return newState;
    });
  };

  const handleSupportPress = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.push('/(homeStack)/movements/Contact');
    // Reestablecemos el estado después de 1 segundo para permitir futuras navegaciones
    setTimeout(() => {
      setIsNavigating(false);
    }, 1000);
  };

  return (
    <View style={styles.wrapper}>
      <View style={styles.wrapper}>
        <ScrollView
          contentContainerStyle={styles.scrollViewContent}
          bounces={false}
          showsVerticalScrollIndicator={false}
        >
          <View
            style={{
              marginBottom: 40,
              gap: 24,
            }}
          >
            {sections.map(section => {
              // Crear la interpolación para la rotación
              const rotateInterpolation = rotationAnimations.current[section.id].interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '180deg'],
              });

              return (
                <View key={section.id} style={styles.header}>
                  <View style={styles.containerDown}>
                    <Text style={styles.title}>{section.title}</Text>
                    <TouchableOpacity onPress={() => toggleSection(section.id)}>
                      <Animated.View
                        style={{
                          transform: [{ rotate: rotateInterpolation }],
                        }}
                      >
                        <ArrowDown style={{ flex: 1 }} />
                      </Animated.View>
                    </TouchableOpacity>
                  </View>
                  {expandedSections[section.id] && (
                    <Text style={styles.info}>{section.content}</Text>
                  )}
                </View>
              );
            })}
          </View>
          <View
            style={{
              paddingHorizontal: 24,
            }}
          >
            <PrimaryButton
              title='Contactar a soporte'
              onPress={handleSupportPress}
              disable={isNavigating}
            />
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

export default FaqInfo;

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: '#E6E8F4',
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
    paddingVertical: 24,
  },
  header: {
    paddingHorizontal: 24,
    height: 'auto',
    gap: 10,
  },
  title: {
    fontSize: 16,
    color: '#000',
    fontFamily: 'Inter',
    lineHeight: 18,
    textRendering: 'geometricPrecision',
    fontWeight: 'bold',
    flex: 1,
  },
  info: {
    fontSize: 14,
    color: '#4A4B55',
    fontFamily: 'Inter',
    lineHeight: 18.94,
    textRendering: 'geometricPrecision',
    fontWeight: '400',
  },
  containerDown: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
