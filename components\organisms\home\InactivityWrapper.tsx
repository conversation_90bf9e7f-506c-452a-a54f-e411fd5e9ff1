import { useInactivity } from '@/context/InactivityContext';
import { View } from 'react-native';

export const InactivityWrapper = ({ children }: { children: React.ReactNode }) => {
    const { resetInactivityTimer } = useInactivity();

    return (
        <View
            style={{ flex: 1 }}
            onTouchStart={resetInactivityTimer}
            onTouchMove={resetInactivityTimer}
        >
            {children}
        </View>
    );
};
