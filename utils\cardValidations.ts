// Función para identificar el tipo de tarjeta
export const getCardType = (cardNumber: string) => {
  const cardTypes = {
    visa: /^4/,
    mastercard: /^5[1-5]/,
    amex: /^3[47]/,
    discover: /^6(?:011|5)/,
    diners: /^3(?:0[0-5]|[68])/,
    jcb: /^(?:2131|1800|35)/,
  };

  for (const [type, pattern] of Object.entries(cardTypes)) {
    if (pattern.test(cardNumber)) {
      return type;
    }
  }
  return 'unknown';
};

// Función para validar el CVV
export const validateCVV = (cvv: string) => {
  return /^\d{3,4}$/.test(cvv);
};

// Función para validar la fecha de expiración en formato MM/YY
export const validateExpirationDate = (expirationDate: string) => {
  return /^(0[1-9]|1[0-2])\/\d{2}$/.test(expirationDate);
};

// Función para validar los 16 dígitos de la tarjeta
export const validateCardNumber = (cardNumber: string) => {
  return /^\d{16}$/.test(cardNumber);
};
