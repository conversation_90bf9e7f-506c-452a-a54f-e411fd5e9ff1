import React, { useCallback, useState } from 'react';
import ChangeNipForm from '@/components/organisms/cards/ChangeNipForm';
import { router, useLocalSearchParams } from 'expo-router';
import validateOnlyNumbers from '@/shared/validateOnlyNumbers';
import { useInactivity } from '@/context/InactivityContext';

const ResetNip = () => {
  const { cardId } = useLocalSearchParams<{
    cardId: string;
  }>();
  const [newNip, setNewNip] = useState('');
  const [confirmNip, setConfirmNip] = useState('');
  const { resetInactivityTimer } = useInactivity();

  const validateNip = useCallback(
    (nip: string) => {
      // Agrega aquí la lógica de validación del NIP
      const isValid =
        nip.length === 4 &&
        !/(\d)\1{2}/.test(nip) &&
        !/1234|2345|3456|4567|5678|6789|7890/.test(nip);
      return isValid;
    },
    [],
  );

  const handleSetNip = (nip: string) => {
    const numbers = validateOnlyNumbers(nip);
    setNewNip(numbers);
    resetInactivityTimer(); // Reiniciar el temporizador de inactividad

  };

  const handleSetConfirmNip = (confirmNip: string) => {
    const numbers = validateOnlyNumbers(confirmNip);
    setConfirmNip(numbers);
    resetInactivityTimer(); // Reiniciar el temporizador de inactividad
  }

  const handleSave = () => {
    // si el nip cumple con las validaciones, se sigue con el flujo
    router.replace({
      pathname: '/(homeStack)/myCards/ConfirmPasswordResetNip',
      params: {
        cardId,
        newNip,
      }
    });
  };

  const isEqual = newNip === confirmNip
  const enableSaveBtn = isEqual && validateNip(newNip) && validateNip(confirmNip)

  const isError = (() => {
    if (newNip === "" && confirmNip === "") {
      return false
    }

    const isError = (newNip !== confirmNip) ||
      !validateNip(newNip) ||
      !validateNip(confirmNip)
    return isError
  })()

  return (
    <ChangeNipForm
      nip={newNip}
      confirmNip={confirmNip}
      error={
        !isError ? null : 'El NIP no coincide, revisa de nuevo'
      }
      enableSaveBtn={!enableSaveBtn}
      onSetNip={handleSetNip}
      onSetConfirmNip={handleSetConfirmNip}
      onSaved={handleSave}
    />
  );
};

export default ResetNip;
