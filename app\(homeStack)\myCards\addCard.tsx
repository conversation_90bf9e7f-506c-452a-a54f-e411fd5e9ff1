import AddCard from '@/components/organisms/cards/AddCard';
import validateOnlyNumbers from '@/shared/validateOnlyNumbers';
import { router, useLocalSearchParams } from 'expo-router';
import { useState } from 'react';
import registerCard from '@/services/cards/registerCard';
import { setUpdateErrors } from '@/shared/setUpdateErrors';
import type { ErrorProps } from '@/types';
import { getCardType, validateCardNumber, validateExpirationDate } from '@/utils/cardValidations';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import { useAppContext } from '../../../context/AppProvider';
import { StatusBar } from 'react-native';

type Errors = {
  cardNumber: ErrorProps;
  expirationDate: ErrorProps;
}

// Componente principal para generar una contraseña
const AddCardComponent = () => {
  const { email } = useLocalSearchParams<{ email: string }>();
  const { setCardsLoading } = useAppContext();

  const [loading, setLoading] = useState(false);
  const [cardNumber, setCardNumber] = useState('');
  const [expirationDate, setExpirationDate] = useState('');
  const [errors, setErrors] = useState<Errors>({
    cardNumber: {
      message: undefined,
      error: false,
    },
    expirationDate: {
      message: undefined,
      error: false,
    }
  })

  const [cardType, setCardType] = useState('unknown');

  // Validar todos los campos
  const isFormValid =
    validateCardNumber(cardNumber) &&
    validateExpirationDate(expirationDate)

  // validar si existe un error en el formulario
  const isFormError =
    errors.cardNumber.error || errors.expirationDate.error

  const handlePress = () => {
    setLoading(true);
    // cada vez que se presiona el boton "Agregar" se limpian los errores
    setUpdateErrors(false, 'cardNumber', undefined, setErrors)
    setUpdateErrors(false, 'expirationDate', undefined, setErrors)

    // Validar los campos, en teoria ya se validaron en el isFormValid pero por si acaso
    if (isFormValid) {
      // todo: enviar la informacion a la API
      // se ejecuta registerCard y se manejan sus errores
      // quitar diagonal '/' de expirationDate
      const formatExpirationDate = expirationDate.replace('/', '');
      // llamar a la API para registrar la tarjeta y guardar la informacio
      registerCard({
        email,
        card_number: cardNumber,
        card_expiration: formatExpirationDate,
      })
        .then(() => {
          setLoading(false);
          setCardsLoading(true);
          router.replace('/(homeStack)/myCards/SuccessAddCard');
        }).catch((error: string) => {
          setLoading(false);
          console.error(error);
        })
      // Redirigir a la página de éxito
    }
  };

  const handleInputChangeCardNumber = (text: string) => {
    // Filtra el texto para aceptar solo números
    const numericText = validateOnlyNumbers(text);
    setCardNumber(numericText);

    if (numericText.length > 16) {
      setUpdateErrors(true, 'cardNumber', 'Número de tarjeta inválido', setErrors)
    } else {
      setUpdateErrors(false, 'cardNumber', undefined, setErrors)
    }

    // Identificar el tipo de tarjeta
    setCardType(getCardType(numericText));
  };

  const handleInputChangeExpirationDate = (text: string) => {
    // Filtra el texto para aceptar solo números
    const numericText = text.replace(/[^0-9]/g, '').slice(0, 4);

    // Formatear como MM/ automáticamente después de ingresar el segundo dígito
    let formattedDate = numericText;
    if (numericText.length > 2) {
      formattedDate = `${numericText.slice(0, 2)}/${numericText.slice(2)}`;
    } else if (numericText.length === 2 && !text.includes('/')) {
      formattedDate = `${numericText}/`;
    } else {
      formattedDate = numericText;
    }

    // Validar el formato MM/YY
    let isValid = false;
    if (formattedDate.length === 5) {
      const [month, year] = formattedDate.split('/');
      const monthNum = parseInt(month, 10);
      const yearNum = parseInt(year, 10);
      if (monthNum >= 1 && monthNum <= 12 && yearNum >= 0 && yearNum <= 99) {
        isValid = true;
      }
    }

    setExpirationDate(formattedDate);
    setUpdateErrors(!isValid, 'expirationDate', 'Fecha de expiración inválida', setErrors)
  };

  return (
    <>
      <StatusBar backgroundColor='#ffffff' barStyle='dark-content' translucent={false} />
      <AddCard
        data={{ cardNumber, expirationDate }}
        disable={!isFormValid || isFormError}
        loading={false}
        cardNumberError={errors.cardNumber}
        expirationDateError={errors.expirationDate}
        cardType={cardType}
        onPress={handlePress}
        handleInputChangeCardNumber={handleInputChangeCardNumber}
        handleInputChangeExpirationDate={handleInputChangeExpirationDate}
      />
      {loading && <LoadingScreen />}
    </>
  );
};

export default AddCardComponent;
