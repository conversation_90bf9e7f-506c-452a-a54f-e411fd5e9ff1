import { View, Text, StyleSheet } from 'react-native';
import React, { useEffect, useState } from 'react';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import SeeAccountDetails from '@/components/organisms/home/<USER>';
import CopyCLABEAlertSucess from '@/components/molecules/home/<USER>';
import CopyAccountConveniaAlertSucess from '@/components/molecules/home/<USER>';
import { useTransactionContext } from '@/context/TransactionContext';
import LoadingScreen from '@/components/molecules/LoadingScreen';

// Componente funcional SeeAccount
const SeeAccount = () => {
  const { fetchTransactions } = useTransactionContext();
  const [showCopyCLABEAlert, setShowCopyCLABEAlert] = useState(false);
  const [showCopyAccountAlert, setShowCopyAccountAlert] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true); // Activar el loader
        await Promise.all([fetchTransactions()]); // Esperar a que ambas funciones terminen
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false); // Desactivar el loader
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    // Renderiza el componente PageBaseBWReturnButton
    // topChildren: Contenido superior, en este caso un View con un Text
    // bottomChildren: Contenido inferior, en este caso el SeeAccountDetails
    <>
      <PageBaseBWReturnButton
        topChildren={
          <View style={{ padding: 24 }}>
            <Text style={styles.title}>Mi cuenta</Text>
          </View>
        }
        bottomChildren={
          <SeeAccountDetails
            onSetShowCopyCLABEAlert={() => setShowCopyCLABEAlert(true)}
            onSetShowCopyAccountAlert={() => setShowCopyAccountAlert(true)}
          />
        }
      />
      <CopyCLABEAlertSucess
        showCopyCLABEAlert={showCopyCLABEAlert}
        setShowCopyCLABEAlert={() => setShowCopyCLABEAlert(false)}
      />
      <CopyAccountConveniaAlertSucess
        showCopyCLABEAlert={showCopyAccountAlert}
        setShowCopyAlert={() => setShowCopyAccountAlert(false)}
      />
      {loading && <LoadingScreen />}
    </>
  );
};

// Exporta el componente SeeAccount como el valor por defecto
export default SeeAccount;

// Estilos para el componente
const styles = StyleSheet.create({
  title: {
    fontFamily: 'Inter',
    fontWeight: '400',
    fontSize: 24,
    lineHeight: 29,
    color: '#F4F5FB',
  },
});
