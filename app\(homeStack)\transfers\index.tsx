import React, { useCallback, useEffect, useState } from 'react';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import MyTransfersHeader from '@/components/organisms/tranfers/MyTransfersHeader';
import NoContactsContent from '@/components/organisms/tranfers/NoContactsContent';
import ContactList from '@/components/molecules/transfers/ContactList';
import { deleteTransferContact } from '@/services/transfersService';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import { useBankContext } from '@/context/BankContext';
import { useFocusEffect } from 'expo-router';
import { useAccountContext } from '@/context/AccountContext';
import { useTransferContext } from '@/context/TransferContext';
import CustomAlert from '@/components/ui/CustomAlert';

export type Contact = {
  uuid: string;
  name: string;
  icon: any;
  bank: string;
  typeAccount: string;
  last4Digits: string;
  num_clabe: string;
};

const Transfers = () => {
  const { fetchBanks } = useBankContext();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isEnabledToTransfer, setIsEnabledToTransfer] = useState(false);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [selectedContactId, setSelectedContactId] = useState<string>('');
  const { setTransferCalculate, } = useAccountContext();
  const { clearTransferData, listContact, fetchContacts, isLoadingContacts } = useTransferContext();

  const handleDelete = (uuid: string) => {
    setSelectedContactId(uuid);
    setShowDeleteAlert(true);
  };

  const handleConfirmDelete = async () => {
    try {
      await deleteTransferContact(selectedContactId);
      setContacts(prevContacts =>
        prevContacts.filter(contact => contact.uuid !== selectedContactId),
      );
    } catch (error) {
      console.error('Error al eliminar el contacto:', error);
    }
    setShowDeleteAlert(false);
  };

  useEffect(() => {
    if (isLoadingContacts) return
    try {
      setIsEnabledToTransfer(true);
      if (listContact && listContact.length > 0) {
        const formattedContacts = listContact.map((contact: any) => ({
          uuid: contact.id, // Mapea 'id' a 'uuid'
          name: contact.name,
          icon:
            contact.bank_institution === 'CONVENIA'
              ? require('@/assets/images/store.png')
              : require('@/assets/images/sendtr.png'), // Icono por defecto
          bank: contact.bank_institution, // Mapea 'bank_institution' a 'bank'
          typeAccount: 'CLABE', // Puedes definirlo si siempre es CLABE
          last4Digits: contact.num_clabe.slice(-4), // Extrae los últimos 4 dígitos
          num_clabe: contact.num_clabe, // Mapea 'num_clabe' a 'num_clabe'
        }));

        setContacts(formattedContacts);
      } else {
        setContacts([]);
      }
    } catch (error) {
      console.error('Error al obtener los contactos:', error);
    }
  }, [listContact, isLoadingContacts]);

  useEffect(() => {
    const fetch = async () => {
      try {
        await fetchBanks();
      } catch (error) {
        console.error('Error al obtener los bancos:', error);
      }
    };

    fetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // limpiar con useFocusEffect setTransferCalculate(null)
  // limpiamos el monto original - comision, del contexto
  useFocusEffect(
    useCallback(() => {
      setTransferCalculate(null);
      clearTransferData();
    }, [clearTransferData, setTransferCalculate]),
  )

  useEffect(() => {
    // Solo queremos cargar los contactos una vez al montar el componente

    fetchContacts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <PageBaseBWReturnButton
        topChildren={<MyTransfersHeader isEnabledToTransfers={isEnabledToTransfer} />}
        bottomChildren={
          contacts.length > 0 ? (
            <ContactList contacts={contacts} onDelete={handleDelete} isEnabledToTransfer={isEnabledToTransfer} />
          ) : (
            <NoContactsContent />
          )
        }
      />

      <CustomAlert
        visible={showDeleteAlert}
        title="Eliminar contacto"
        message="¿Estás seguro de que deseas eliminar este contacto?"
        onClose={() => setShowDeleteAlert(false)}
        onAccept={handleConfirmDelete}
        buttonText="Eliminar"
        cancelButtonText="Cancelar"
      />

      {isLoadingContacts && <LoadingScreen />}
    </>
  );
};

export default Transfers;