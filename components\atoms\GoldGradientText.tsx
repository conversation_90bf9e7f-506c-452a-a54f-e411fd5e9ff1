import { StyleSheet, Text, View, type TextProps } from 'react-native';
import React from 'react';
import MaskedView from '@react-native-masked-view/masked-view';

type Props = TextProps & {
  text: string;
  widthWrapper?: number;
};
// widthWrapper para ajustar el ancho del texto, y de su contenedor padre

const GoldGradientText = ({ text, style, widthWrapper }: Props) => {

  return (
    <MaskedView
      style={{
        flexDirection: 'row',
        height: 17,
        width: widthWrapper ? widthWrapper : 114,
      }}
      maskElement={
        <View
          style={{
            backgroundColor: 'transparent',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Text style={[styles.subtitle, style]}>{text}</Text>
        </View>
      }
    >
      {/* Definición de las diferentes secciones de colores que se muestran detrás del texto */}
      <View
        style={{ flex: 0.04, height: '100%', backgroundColor: '#75532F' }}
      />
      <View
        style={{ flex: 0.19, height: '100%', backgroundColor: '#8B6539' }}
      />
      <View
        style={{ flex: 0.16, height: '100%', backgroundColor: '#9F7A49' }}
      />
      <View
        style={{ flex: 0.11, height: '100%', backgroundColor: '#AF8B55' }}
      />
      <View
        style={{ flex: 0.24, height: '100%', backgroundColor: '#DCB992' }}
      />
      <View
        style={{ flex: 0.13, height: '100%', backgroundColor: '#DAB890' }}
      />
      <View
        style={{ flex: 0.05, height: '100%', backgroundColor: '#D5B289' }}
      />
      <View
        style={{ flex: 0.03, height: '100%', backgroundColor: '#CCA87C' }}
      />
      <View
        style={{ flex: 0.03, height: '100%', backgroundColor: '#BF9B6B' }}
      />
      <View
        style={{ flex: 0.02, height: '100%', backgroundColor: '#AF8B56' }}
      />
      <View
        style={{ flex: 0.01, height: '100%', backgroundColor: '#AF8B55' }}
      />
      <View
        style={{ flex: 0.01, height: '100%', backgroundColor: '#E9D6B8' }}
      />
    </MaskedView>
  );
};

export default GoldGradientText;

const styles = StyleSheet.create({
  subtitle: {
    color: '#F4F5FB',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Inter',
    lineHeight: 16.94,
  },
});
