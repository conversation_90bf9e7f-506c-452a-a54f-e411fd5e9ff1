import React from 'react';
import AlertSucess from '@/components/atoms/AlertSucess';

type Props = {
  showCopyCLABEAlert: boolean;
  setShowCopyCLABEAlert: (value: boolean) => void;
};
const CopyCLABEAlertSucess = ({
  showCopyCLABEAlert,
  setShowCopyCLABEAlert,
}: Props) => {
  return (
    <AlertSucess
      isVisible={showCopyCLABEAlert}
      text='El número CLABE ha sido copiado al portapapeles.'
      timeToHide={2000}
      onSetShowAlert={() => setShowCopyCLABEAlert(false)}
    />
  );
};

export default CopyCLABEAlertSucess;
