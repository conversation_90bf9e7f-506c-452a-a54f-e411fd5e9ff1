import api
 from "../api/api";
const changeNip = async (cardId: string, pin: string) => {
    try {
        const response = await api.put('/dock-cards/change-card-pin', {
            card_dock_id: cardId,
            pin: pin
        });
        const responseStatus = response.data.statusCode
        if (responseStatus!== 200) {
            throw new Error(response.data.message);
        }
        return response.data;
    }
    catch (error: unknown) {
        if (error instanceof Error) {
            throw error; // Si ya es un Error, lo lanzamos directamente
        } else {
            // Si es otro tipo (objeto, string, etc.), lo convertimos a string apropiadamente
            throw new Error(String(error));
        }
    }
}
export default changeNip;