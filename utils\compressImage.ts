import * as ImageManipulator from 'expo-image-manipulator';

/**
 * Comprime una imagen ajustando su calidad y tamaño.
 * @param uri URI original de la imagen
 * @param options Opcional: calidad (0-1), ancho, alto
 * @returns Nueva imagen comprimida
 */
export const compressImage = async (
  uri: string,
  options?: {
    compress?: number; // default: 0.5
    width?: number;
    height?: number;
  },
): Promise<{ uri: string; width: number; height: number; size?: number }> => {
  const { compress = 0.5, width, height } = options || {};

  const result = await ImageManipulator.manipulateAsync(
    uri,
    width || height ? [{ resize: { width, height } }] : [],
    {
      compress,
      format: ImageManipulator.SaveFormat.JPEG,
    },
  );

  return result;
};
