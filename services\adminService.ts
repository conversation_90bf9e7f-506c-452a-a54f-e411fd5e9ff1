import axios from 'axios';
import BASE_URL from './config';

// Instancia de Axios con configuración base
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const findAdminByMemebershipNumber = async (
  memebership_number: number,
) => {
  try {
    const response = await api.get(
      `admin/findAdminByMemebershipNumber/${memebership_number}`,
    );
    return response.data;
  } catch (error: any) {
    console.error(
      'Error al buscar número de membresia:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message || 'Error al buscar número de membresia',
    );
  }
};
