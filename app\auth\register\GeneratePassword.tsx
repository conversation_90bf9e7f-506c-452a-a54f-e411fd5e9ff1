import CreatePassword from '@/components/organisms/login/CreatePassword';
import { createUser } from '@/services/userService';
import { router, useLocalSearchParams } from 'expo-router';
import { useState } from 'react';

// Componente principal para generar una contraseña
const GeneratePassword = () => {
  // Obtiene los parámetros de búsqueda locales
  const { email, fullName, phone, affiliationNumber, dialing_code, country_code } = useLocalSearchParams();

  // Estado para manejar el estado de carga
  const [loading, setLoading] = useState(false);
  // Estado para manejar los errores
  const [error, setError] = useState<string | null>(null);

  // Función para manejar el guardado de la contraseña
  const hadleSave = async (password: string, confirmPassword: string) => {
    // Reiniciar el error
    setError(null);
    // Iniciar el estado de carga
    setLoading(true);
    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden');
      setLoading(false);
      return;
    }

    try {
      // Crear un nuevo usuario
      const response = await createUser({
        name: Array.isArray(fullName) ? fullName.join(' ') : fullName,
        email: Array.isArray(email) ? email[0] : email,
        phone: Number(phone),
        membership_number: Number(Array.isArray(affiliationNumber)
          ? affiliationNumber[0]
          : affiliationNumber),
        password,
        created_by: 'app',
        dialing_code: Array.isArray(dialing_code) ? dialing_code[0].replace('+', '') : dialing_code.replace('+', '') || '52', // Default to Mexico if no country code is provided
        country_code: Array.isArray(country_code) ? country_code[0] : country_code || 'MX', // Default to Mexico if no country code is provided
      });

      if (response.statusCode === 201) {
        router.dismissAll()
        router.push('/auth/login');
      } else {
        setError(response.message);
      }
    } catch (err: any) {
      console.error('Error al crear el usuario:', err);
    } finally {
      setLoading(false);
      setError('Error al crear el usuario, por favor intenta más tarde');
    }
  };

  // Renderizar el componente CreatePassword con las propiedades necesarias
  return (
    <CreatePassword
      title='Genera tu contraseña'
      subTitle='Recuerda lo siguiente:'
      textButton='Guardar'
      loading={loading}
      error={error}
      onPress={hadleSave}
      setError={setError}
    />
  );
};

export default GeneratePassword;
