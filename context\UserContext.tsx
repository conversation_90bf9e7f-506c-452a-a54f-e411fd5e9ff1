import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
  useRef,
  Dispatch,
  SetStateAction
} from 'react';
import * as SecureStore from 'expo-secure-store';
import { findUserByEmail } from '@/services/userService';

interface AdminSettings {
  spei_in: number;
  spei_out: number;
  target_refound: number;
  ambassador: string;
  managerId?: string;
  isManager?: boolean;
}

interface User {
  id: string;
  email: string;
  fullName: string;
  phone: string;
  affiliationNumber: string;
  token: string;
  createdAt: string;
  enabled?: boolean;
  adminSettings?: AdminSettings;
  convenia_account?: string;
  membership_number?: string;
}

interface UserContextType {
  user: User | null;
  isLoading: boolean;
  loadUser: (force?: boolean) => Promise<void>;
  isAccountActivated: boolean;
  showActivationModal: boolean;
  setShowActivationModal: Dispatch<SetStateAction<boolean>>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showActivationModal, setShowActivationModal] = useState(false);
  const hasLoaded = useRef(false); // 👈 bloqueo de carga múltiple

  const loadUser = async (force = false) => {
    if (hasLoaded.current && !force) return; // ❌ Evita múltiple ejecución

    const email = await SecureStore.getItemAsync('userEmail');
    const token = await SecureStore.getItemAsync('userToken');

    if (!email || !token) return;

    setIsLoading(true);
    const data = await findUserByEmail(email);

    setUser({
      id: data.id,
      email: data.email,
      fullName: data.name,
      phone: data.phone,
      affiliationNumber: data.affiliation_number,
      token,
      enabled: data.enabled,
      createdAt: data.created_at,
      convenia_account: data.convenia_account,
      membership_number: data.membership_number,
      adminSettings: {
        spei_in: data?.spei_in || 0,
        spei_out: data?.spei_out || 0,
        target_refound: data?.target_refound || 0,
        ambassador: data?.ambassador || '',
        managerId: data?.manager_id,
        isManager: data?.is_manager,
      },
    });
    setShowActivationModal(!data.enabled);

    hasLoaded.current = true;
    setIsLoading(false);
  };

  useEffect(() => {
    loadUser();
  }, []);

  const isAccountActivated = !!user?.enabled;

  return (
    <UserContext.Provider
      value={{ user, loadUser, isLoading, isAccountActivated, showActivationModal, setShowActivationModal }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUserContext = () => {
  const context = useContext(UserContext);
  if (!context) throw new Error('useUser debe usarse dentro de un UserProvider');
  return context;
};
