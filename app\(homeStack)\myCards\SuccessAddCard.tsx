import { StyleSheet, Image, Text, View, StatusBar } from 'react-native';
import React from 'react';
import { router } from 'expo-router';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import AddCarSuccess from '@/components/icons/AddCarSuccess';

const SuccessAddCard = () => {
  return (
    <View style={styles.wrapper}>
      <StatusBar backgroundColor='#000000' barStyle='light-content' translucent={false} />
      {/* Imagen de desbloqueo */}
      <AddCarSuccess />
      <View style={styles.content}>
        {/* Imagen de éxito */}
        <Image source={require('@/assets/images/checkSuccessPassword.png')} style={{ width: 60, height: 60 }} />
        {/* Texto de confirmación de restablecimiento de contraseña */}
        <Text style={styles.firstText}>Tarjeta agregada correctamente</Text>
        <Text style={styles.secondText}>
          Ya la puedes visualizar en tu app.
        </Text>
      </View>
      <SecondaryButton
        title='Ir al inicio'
        onPress={() => router.back()}
        width='100%'
      />
    </View>
  );
};

export default SuccessAddCard;

const styles = StyleSheet.create({
  // Estilo para el contenedor principal
  wrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
    gap: 40,
    paddingHorizontal: 24,
  },
  // Estilo para el contenido
  content: {
    gap: 8,
    alignItems: 'center',
  },
  // Estilo para el primer texto
  firstText: {
    color: '#F4F5FB',
    fontWeight: '500',
    fontSize: 32,
    lineHeight: 38,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
  // Estilo para el segundo texto
  secondText: {
    color: '#F4F5FB',
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 19,
    textAlign: 'center',
    fontFamily: 'Inter',
  },
});
