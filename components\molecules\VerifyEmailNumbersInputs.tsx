import { StyleSheet, View, Text } from 'react-native';
import React, { useState, useRef } from 'react';
import RoundedInputNumber, { RoundedInputNumberRef } from '../atoms/RoundedInputNumber';
import * as Clipboard from "expo-clipboard";

// Definición de los tipos de propiedades que el componente acepta
type Props = {
  hasError: boolean;
  onNumberChange: (number: string) => void;
};

// Componente principal que maneja la verificación de números de correo electrónico
const VerifyEmailNumbersInputs = ({ hasError, onNumberChange }: Props) => {
  // Estado para almacenar los números ingresados
  const [numbers, setNumbers] = useState(['', '', '', '']);
  // Referencias a los inputs para manejar el enfoque
  const inputRefs = useRef<RoundedInputNumberRef[]>([]);

  // Función que maneja el cambio de número en una posición específica
  const handleNumberChange = (position: number, number: string) => {
    // verificamos si en el primer input se ingrean 4 digitos
    if (position === 0 && number.length === 4) {
      checkClipboard();
      return;
    }
    const newNumbers = [...numbers];
    newNumbers[position] = number;
    setNumbers(newNumbers);
    onNumberChange(newNumbers.join(''));

    // Enfocar el siguiente input si hay un número y no es el último input
    if (number && position < inputRefs.current.length - 1) {
      inputRefs.current[position + 1]?.focus();
    }
  };

  // Función para manejar el retroceso (backspace) cuando un input está vacío
  const handleBackspace = (position: number) => {
    if (position > 0) {
      inputRefs.current[position - 1]?.focus();
    }
  };

  // Función para manejar el pegado de texto
  const handlePaste = async (position: number, text: string) => {
    let pastedText = text;

    // Si no se proporciona texto, intentar obtenerlo del portapapeles
    if (!pastedText) {
      try {
        pastedText = await Clipboard.getStringAsync();
      } catch (error) {
        console.error('Error al acceder al portapapeles:', error);
        return;
      }
    }

    // Filtrar solo números
    const numericValue = pastedText.replace(/[^0-9]/g, '');

    // Si hay números para pegar
    if (numericValue) {
      const newNumbers = [...numbers];

      // Distribuir los dígitos pegados en los inputs disponibles
      for (let i = 0; i < Math.min(numericValue.length, 4); i++) {
        const targetPosition = position + i;
        if (targetPosition < 4) {
          newNumbers[targetPosition] = numericValue.charAt(i);
        }
      }

      setNumbers(newNumbers);
      onNumberChange(newNumbers.join(''));

      // Enfocar el siguiente input vacío o el último si todos están llenos
      const nextEmptyIndex = newNumbers.findIndex(n => n === '');
      if (nextEmptyIndex !== -1) {
        inputRefs.current[nextEmptyIndex]?.focus();
      } else if (inputRefs.current.length > 0) {
        inputRefs.current[inputRefs.current.length - 1]?.focus();
      }
    }
  };

  // Efecto para detectar pegar desde el portapapeles cuando el componente se monta
  const checkClipboard = async () => {
    try {
      const clipboardContent = await Clipboard.getStringAsync();
      const numericValue = clipboardContent.replace(/[^0-9]/g, '');

      // Si hay exactamente 4 dígitos en el portapapeles, pegarlos automáticamente
      if (numericValue.length === 4 && numbers.every(n => n === '')) {
        handlePaste(0, numericValue);
      }
    } catch (error) {
      console.error('Error al verificar el portapapeles:', error);
    }
  };

  return (
    <View style={styles.wrapper}>
      <View style={styles.content}>
        <View style={styles.contentNumber}>
          {numbers.map((number, index) => (
            <RoundedInputNumber
              key={index}
              hasError={hasError}
              value={number}
              onChange={num => handleNumberChange(index, num)}
              onBackspace={() => handleBackspace(index)}
              maxLength={(index === 0 && number === '') ? undefined : 1}
              ref={ref => {
                if (ref) inputRefs.current[index] = ref;
              }}
            />
          ))}
        </View>
        {hasError && <Text style={styles.textError}>Código incorrecto</Text>}
      </View>
    </View>
  );
};

export default VerifyEmailNumbersInputs;

// Estilos para el componente
const styles = StyleSheet.create({
  wrapper: {
    gap: 8,
  },
  content: {
    marginHorizontal: 'auto',
    gap: 8,
  },
  contentNumber: {
    flexDirection: 'row',
    gap: 8,
  },
  textError: {
    color: '#F04438',
    fontSize: 14,
    lineHeight: 20,
    letterSpacing: 0.25,
    fontWeight: '400',
    fontFamily: 'Inter',
  },
});
