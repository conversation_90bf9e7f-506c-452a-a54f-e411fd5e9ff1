import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import BottomModal from '../BottomModal';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import AlertYellow from '@/components/icons/AlertYellow';

interface AccountActivationModalProps {
  isVisible: boolean;
  onConfirm: () => void;
  onClose: () => void;
}

const ClarificationsBottomModal: React.FC<AccountActivationModalProps> = ({
  isVisible,
  onConfirm,
  onClose,
}) => {
  return (
    <BottomModal isVisible={isVisible} onClose={onClose}>
      <View style={styles.body}>
        <View style={styles.rowContainer}>
          <View style={styles.textContent}>
            <AlertYellow />
            <Text style={styles.title}>
              ¿Estas seguro que deseas enviar tu aclaración?
            </Text>
          </View>
          <Text style={styles.text}>
            Recibirás un correo electrónico con el numero de aclaración.
          </Text>
        </View>
        <PrimaryButton title='Aceptar' onPress={onConfirm} />
        <SecondaryButton
          title='Cancelar'
          onPress={onClose}
        />
      </View>
    </BottomModal>
  );
};

export default ClarificationsBottomModal;

const styles = StyleSheet.create({
  body: {
    width: '100%',
    paddingVertical: 24,
    paddingHorizontal: 32,
    gap: 24,
    marginBottom: 24,
  },
  rowContainer: {
    gap: 24,
  },
  textContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 11,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter',
    lineHeight: 29.05,
    fontWeight: '400',
    color: '#ffffff',
  },
  text: {
    fontSize: 14,
    fontFamily: 'Inter',
    lineHeight: 16.94,
    fontWeight: '400',
    color: '#ffffff',
    paddingLeft: 21,
  },
});
