import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ImageSourcePropType,
  Animated,
  Platform,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { Image } from 'expo-image';
import React, { useState, useEffect, useRef } from 'react';
import GoldGradientText from '@/components/atoms/GoldGradientText';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import { ThemedText } from '@/components/ui/ThemedText';
import { router, useFocusEffect } from 'expo-router';
import CustomSwitch from '@/components/atoms/CustomSwitch';
import { CARD_TYPES } from '@/constants/typeCard';
import DinamicCvv from './DinamicCvv';
import type { Card } from '@/types/services/cards';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import useIsTablet from '@/hooks/useIsTablet';
import NipSee from '@/components/icons/NipSee';
import DeleteCardBottomModal from './DeleteCardBottomModal';

type Props = {
  isEnabled: boolean;
  urlImageCard: ImageSourcePropType | undefined;
  card: Omit<Card, 'card_last_4' | 'expiration_date'>;
  cvv: string | null;
  timeLeft: number | null;
  toggleSwitch: () => void;
  onSetCvv: (value: string | null) => void;
  onResetTimeLeft: () => void;
  onGetCvv: () => void;
};

const CardDetails: React.FC<Props> = ({
  isEnabled,
  urlImageCard,
  card,
  cvv,
  timeLeft,
  onResetTimeLeft,
  onSetCvv,
  onGetCvv,
  toggleSwitch,
}) => {
  const isTablet = useIsTablet();
  const [showModal, setShowModal] = useState(false);
  const [showCvv, setShowCvv] = useState(false);
  const [isNavigatingNip, setIsNavigatingNip] = useState(false);

  // Resetear el estado al volver a la pantalla (cuando recupera el foco)
  useFocusEffect(
    React.useCallback(() => {
      setIsNavigatingNip(false);
    }, [])
  );

  // para manejar la animacio en android en lugar de animar el height se animara la escala y la tra
  // const scaleAnim = useRef(new Animated.Value(1)).current;
  const heightAnim = useRef(new Animated.Value(150)).current; // Initial height of the card
  const opacityAnim = useRef(new Animated.Value(0)).current; // Initial opacity of the CVV
  const buttonOpacityAnim = useRef(new Animated.Value(1)).current; // Initial opacity of the button
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    Animated.timing(opacityAnim, {
      toValue: showCvv ? 1 : 0,
      duration: 500,
      useNativeDriver: true,
    }).start();

    Animated.timing(buttonOpacityAnim, {
      toValue: showCvv ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    const duration = Platform.OS === 'android' ? 100 : 300;

    Animated.timing(heightAnim, {
      toValue: showCvv ? 100 : 150, // Adjust height when CVV is shown
      duration: duration,
      useNativeDriver: false,
    }).start();
  }, [buttonOpacityAnim, heightAnim, opacityAnim, showCvv]);

  const handleDeleteCard = () => {
    setShowModal(true);
  };

  const handleConfirmDeleteCard = () => {
    setShowModal(false);
    router.push({
      pathname: '/(homeStack)/myCards/PassDeleteCard',
      params: {
        cardId: card.id,
      },
    });
  };

  const handleShowCvv = () => {
    if (showCvv) {
      setShowCvv(false)
      onSetCvv(null)
    } else {
      setShowCvv(true)
      onGetCvv()
    }
  };


  const handleEndCounterCVV = () => {
    if (showCvv) {
      onSetCvv(null)
      onGetCvv()
      onResetTimeLeft()
    }
  }

  const handlePressNip = async () => {
    if (isNavigatingNip) return;
    setIsNavigatingNip(true);
    try {
      await router.push({
        pathname: '/(homeStack)/myCards/PasswordSeeNip',
        params: {
          cardId: card.id,
        }
      });
    } finally {
      // Si quieres desbloquear después de navegar, puedes usar un callback o useFocusEffect
      // setIsNavigatingNip(false);
    }
  };

  const isDigitalCard = card.card_type === CARD_TYPES.VIRTUAL;

  const textTypeCard = isDigitalCard
    ? 'Tarjeta digital Convenia'
    : 'Tarjeta física Convenia';

  if (!card.id || !card.pan || !card.format_expiration_date) {
    return <LoadingScreen />;
  }

  return (
    <View style={styles.content}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
        contentContainerStyle={{
          flexGrow: 1,
          justifyContent: 'space-between',
          paddingVertical: 24,
        }}
      >

        <View
          style={{
            flex: 1,
          }}
        >
          <Animated.View
            style={{
              height: isTablet ? 180 : heightAnim,
              maxHeight: isTablet ? 250 : 150,
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 18,
            }}
          >
            <Image
              source={urlImageCard}
              contentFit='contain'
              style={[
                styles.imageCard,
              ]}
            />
          </Animated.View>
          <View style={styles.actionsContainer}>
            {
              card.card_type === CARD_TYPES.PHYSICAL && (
                <TouchableOpacity
                  onPress={handlePressNip}
                  disabled={showCvv || isNavigatingNip}
                  style={{
                    opacity: showCvv || isNavigatingNip ? 0.6 : 1,
                  }}
                >
                  <View style={styles.nipContainer}>
                    <NipSee />
                    <GoldGradientText text='Ver/Cambiar NIP' style={styles.nipText} />
                  </View>
                </TouchableOpacity>
              )
            }
            <View style={styles.action2Container}>
              <CustomSwitch
                isEnabled={isEnabled}
                toggleSwitch={toggleSwitch}
                value={isEnabled}
                disabled={showCvv}
              />
              <ThemedText
                type='caption'
                style={{ minWidth: 65 }}
                darkColor='#000000'
                lightColor='#000000'
              >
                {isEnabled ? 'Apagar' : 'Encender'}
              </ThemedText>
            </View>
          </View>
          <ThemedText type='defaultSemiBold' lightColor='#000000'>
            Datos de tarjeta
          </ThemedText>
          <View style={styles.detailsContainer}>
            <View style={styles.detailContainer}>
              <Text style={styles.label}>Producto</Text>
              <ThemedText type='subtitle' lightColor='#000000'>
                {textTypeCard}
              </ThemedText>
            </View>
            <View style={styles.detailContainer}>
              <Text style={styles.label}>Número de tarjeta</Text>
              <ThemedText type='subtitle' lightColor='#000000'>
                {card.pan.replace(/(\d{4})(?=\d)/g, '$1 ')}
              </ThemedText>
            </View>
            <View style={styles.detailContainer}>
              <Text style={styles.label}>Fecha de expiración</Text>
              <ThemedText type='subtitle' lightColor='#000000'>
                {card.format_expiration_date}
              </ThemedText>
            </View>
            {card.card_type === CARD_TYPES.VIRTUAL && (
              <View style={styles.cvvWrapper}>
                <Text style={styles.labelCvv}>CVV dinámico</Text>
                <TouchableOpacity
                  style={{
                    width: showCvv ? 50 : 30,
                    overflow: 'hidden',
                  }}
                  onPress={handleShowCvv}
                >
                  <GoldGradientText
                    text={showCvv ? 'Ocultar' : 'Ver'}
                    style={styles.viewText}
                  />
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
        {!showCvv && (
          <Animated.View style={{ opacity: buttonOpacityAnim }}>
            <SecondaryButton
              title='Eliminar tarjeta'
              inverted
              onPress={handleDeleteCard}
            />
          </Animated.View>
        )}
        {!!cvv && card.card_type === CARD_TYPES.VIRTUAL && timeLeft && (
          <Animated.View
            style={{
              opacity: opacityAnim,
              justifyContent: 'center',
            }}
          >
            <DinamicCvv
              cvv={cvv}
              unixTimeLeft={timeLeft}
              onEndCounter={handleEndCounterCVV}
            />
          </Animated.View>
        )}
        {showCvv && !cvv && (
          <View
            style={{
              paddingBottom: 24,
            }}
          >
            <ActivityIndicator
              size='small'
              color='#B8860B'
              style={{
                marginVertical: 24,
              }}
            />
          </View>
        )}
      </ScrollView>

      <DeleteCardBottomModal
        showModal={showModal}
        setShowModal={setShowModal}
        handleConfirmDeleteCard={handleConfirmDeleteCard}
      />
    </View>
  );
};

export default CardDetails;

const styles = StyleSheet.create({
  content: {
    paddingHorizontal: 24,
    backgroundColor: '#E6E8F4',
    flex: 1,
    justifyContent: 'space-between',
  },
  imageCard: {
    width: '100%',
    height: '100%',
    marginHorizontal: 'auto',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // marginTop: 18,
    marginBottom: 24,
    // backgroundColor: 'blue',
  },
  action2Container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  detailsContainer: {
    gap: 24,
    flexGrow: 1,
    // justifyContent: 'space-between',
    paddingVertical: 24,
    maxHeight: 270,
  },
  detailContainer: {
    gap: 4,
    flex: 0,
  },
  label: {
    fontSize: 14,
    lineHeight: 16.94,
    fontFamily: 'Inter',
    color: '#6F7280',
  },
  labelCvv: {
    fontSize: 14,
    lineHeight: 16.94,
    fontFamily: 'Inter',
    color: '#6F7280',
    width: '50%',
  },
  nipContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    maxWidth: 144,
    gap: 8,
  },
  nipText: { textAlign: 'left', width: '100%' },
  viewText: {
    color: 'yellow',
    fontSize: 14,
    lineHeight: 16.94,
    fontFamily: 'Inter',
    fontWeight: '500',
    width: '100%',
    textAlign: 'left',
  },
  cvvWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
