import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import {
  gradientColor,
  gradietColorLocations,
} from '@/constants/GoldGradientColor';
interface DigitProps {
  value: string;
}
const DigitCount: React.FC<DigitProps> = ({ value }) => {
  return (
    <LinearGradient
      colors={gradientColor}
      locations={gradietColorLocations}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={styles.gradientContent}
    >
      <View style={styles.whiteCount}>
        <Text style={styles.text}>{value}</Text>
      </View>
    </LinearGradient>
  );
};

export default DigitCount;

const styles = StyleSheet.create({
  gradientContent: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    width: 30,
    height: 30,
    padding: 1,
  },
  whiteCount: {
    width: 26,
    height: 26,
    backgroundColor: 'white',
    borderRadius: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 20,
    lineHeight: 24,
    fontFamily: 'Noto Sans',
    color: '#000000',
  },
});
