import { StyleSheet, View } from 'react-native';
import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import {
  gradientColor,
  gradietColorLocations,
} from '@/constants/GoldGradientColor';

const LineGradient = () => {
  return (
    <View style={styles.line}>
      <LinearGradient
        colors={gradientColor}
        locations={gradietColorLocations}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientContent}
      ></LinearGradient>
    </View>
  );
};

export default LineGradient;

const styles = StyleSheet.create({
  line: {
    height: 1,
    width: '100%',
  },
  gradientContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
  },
});
