import { StyleSheet, Text, View } from 'react-native';
import React, { useState, useEffect } from 'react';
import DigitCount from '@/components/atoms/DigitCount';

type Props = {
  cvv: string;
  unixTimeLeft: number;
  onEndCounter: () => void;
};
const DinamicCvv: React.FC<Props> = ({ cvv, unixTimeLeft, onEndCounter }) => {
  const [timeLeft, setTimeLeft] = useState<number>(unixTimeLeft);

  useEffect(() => {
    if (timeLeft === 0) {
      onEndCounter();
      return;
    }
    const intervalId = setInterval(() => {
      setTimeLeft(prevTime => prevTime - 1);
    }, 1000);
    return () => clearInterval(intervalId);
  }, [onEndCounter, timeLeft]);

  const minutes: number = Math.floor(timeLeft / 60);
  const seconds: number = timeLeft % 60;

  const formatTime = (time: number): string => time.toString().padStart(2, '0');

  return (
    <View style={styles.wrapper}>
      <Text style={styles.label}>
        Tu código de seguridad (CVV) es válido por 5 minutos.
      </Text>
      <Text style={styles.cvv}>{cvv}</Text>
      <View style={styles.countWrapper}>
        <DigitCount value={formatTime(minutes)[0]} />
        <DigitCount value={formatTime(minutes)[1]} />
        <Text>:</Text>
        <DigitCount value={formatTime(seconds)[0]} />
        <DigitCount value={formatTime(seconds)[1]} />
      </View>
    </View>
  );
};

export default DinamicCvv;

const styles = StyleSheet.create({
  wrapper: {
    // backgroundColor: 'antiquewhite',
    height: 150,
    justifyContent: 'center',
  },
  label: {
    fontSize: 14,
    lineHeight: 16.94,
    fontFamily: 'Inter',
    color: '#6F7280',
  },
  cvv: {
    fontSize: 28,
    lineHeight: 33.89,
    fontFamily: 'Inter',
    color: '#232429',
    textAlign: 'center',
    marginTop: 4,
    marginBottom: 6,
  },
  countWrapper: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5,
    width: '100%',
  },
});
