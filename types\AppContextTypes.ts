import { ReactNode } from 'react';

/**
 * Interface for the App Context properties
 */
export interface AppContextProps {
  // Alert related states
  showSuccessAlert: boolean; // Estado que indica si hay un cambio de NIP
  setShowSuccessAlert: (value: boolean) => void; // Función para actualizar el estado de cambio de NIP
  alertText: string; // Texto de la alerta
  setAlertText: (text: string) => void; // Función para actualizar el texto de la alerta
  
  // Card related states
  cardsLoading: boolean; // estado que indica que se estan cargando las tarjetas
  setCardsLoading: (value: boolean) => void; // Función para actualizar el estado de carga de tarjetas
  
  // Helper methods
  showAlert: (text: string) => void; // Método auxiliar para mostrar alertas
  hideAlert: () => void; // Método auxiliar para ocultar alertas

  // helper para mostrar el modal de cambio de NIP
  showChangeNipModal: boolean;
  setShowChangeNipModal: (value: boolean) => void;
}

/**
 * Interface for the App Provider props
 */
export interface AppProviderProps {
  children: ReactNode; // Los componentes hijos que estarán envueltos por el proveedor
}