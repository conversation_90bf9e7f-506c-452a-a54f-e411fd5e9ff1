import * as React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';
const PowerOffCard = (props: SvgProps) => (
  <Svg width={40} height={40} fill='none' {...props}>
    <Path
      fill='#F4F5FB'
      d='M20 35c7.352 0 13.333-5.982 13.333-13.334 0-5.583-3.453-10.368-8.333-12.351v3.705a9.999 9.999 0 0 1 5 8.646c0 5.515-4.485 10-10 10s-10-4.485-10-10a9.998 9.998 0 0 1 5-8.646V9.315c-4.88 1.983-8.333 6.768-8.333 12.351C6.667 29.018 12.648 35 20 35Z'
    />
    <Path fill='#F4F5FB' d='M18.333 3.333h3.334V20h-3.334V3.333Z' />
    <Path stroke='#F4F5FB' strokeWidth={2} d='M31.771 5.636 6.316 36.491' />
  </Svg>
);
export default PowerOffCard;
