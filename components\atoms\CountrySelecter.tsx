import { useState } from 'react'
import { View, StyleSheet } from 'react-native'
import CountryPickerComponent from './CountryPicker'

export type Country = {
  code: string; // Código del país (ej. 'MX')
  emoji: string; // Emoji del país (ej. '🇲🇽
  callingCode: string; // Código de marcación del país (ej. '+52')
  name: string; // Nombre del país (ej. 'México')
}

type CountryCodeSelectorProps = {
  setCountryCodes?: (country: Country) => void; // Función opcional para establecer el código del país
  countryCodes?: Country; // Código del país para el selector de país
}

export const CountryCodeSelector = ({ setCountryCodes, countryCodes }: CountryCodeSelectorProps) => {
  const [selectedCountry, setSelectedCountry] = useState({
    code: 'MX',
    callingCode: '+52',
    emoji: '🇲🇽',
    name: 'México',
  });

  return (
    <View style={styles.container}>
      <CountryPickerComponent
        selectedCountry={countryCodes?.code || selectedCountry.code} // Usa el código del país proporcionado o el estado local
        onSelect={setCountryCodes || setSelectedCountry} // Usa la función proporcionada o el estado local
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    height: 48,
    // minWidth: 90, // ← asegura que no se colapse
    borderRightWidth: 1,
    borderColor: '#C3C6D8',
  },
})
