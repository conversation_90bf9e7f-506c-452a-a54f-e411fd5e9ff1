import PageBaseBWReturnButton from "@/components/templates/PageBaseBWReturnButton";
import { router } from "expo-router";
import ClarificationsHeader from "@/components/molecules/home/<USER>";
import ClarificationsBody from "@/components/organisms/home/<USER>";
import ClarificationsBottomModal from "@/components/organisms/home/<USER>";
import LoadingScreen from "@/components/molecules/LoadingScreen";

import { useState } from "react";
import * as ImagePicker from 'expo-image-picker';
import { useUserContext } from "@/context/UserContext";
import { FormDataClarification } from "@/types/clarificationsTypes";
import { createClarification, saveClarificationFiles } from "@/services/clarifications/createClarification";
import { compressImage } from "@/utils/compressImage";
import { AxiosError } from "axios";

const Clarifications = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const { user } = useUserContext();

  const [clarificationForm, setClarificationForm] = useState<FormDataClarification>({
    type: null,
    description: '',
    images: [],
  });

  const onSubmitForm = () => {
    if (!clarificationForm.type || !clarificationForm.description) return;
    setShowModal(true);
  };

  const handleConfirmSubmission = async () => {
    setIsLoading(true);

    try {
      let response;
      try {
        const clarificationPayload = {
          type: clarificationForm.type!,
          description: clarificationForm.description,
          status: 'Abierto',
          email: user?.email,
        };

        response = await createClarification(clarificationPayload);
      } catch (error) {
        console.error('❌ Error creando la aclaración:', error);
        throw new Error('No se pudo crear la aclaración');
      }

      if (!response?.trackingNumber) {
        throw new Error('No se obtuvo un ID válido de aclaración.');
      }

      const clarificationId = response.trackingNumber;
      const images = clarificationForm.images || [];

      if (images.length > 0) {
        console.info('📤 Subiendo archivos:', { clarificationId, images });

        try {
          await saveClarificationFiles(clarificationId, images);
        } catch (error) {
          if (error instanceof AxiosError && error.response?.status === 413) {
            throw new Error('Las imágenes son demasiado pesadas. Intenta con archivos más ligeros.');
          }
          console.error('❌ Error subiendo archivos:', error);
          throw new Error('Aclaración creada, pero hubo un error al subir los archivos.');
        }
      }

      router.replace('/home/<USER>/SuccessClarifications');
    } catch (error) {
      console.error('⚠️ Error en el flujo de envío:', error);
    } finally {
      setIsLoading(false);
      setShowModal(false);
    }
  };


  const handleImageChange = async (index: number) => {
    const result = await ImagePicker.launchImageLibraryAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
    });

    if (!result.canceled) {
      const asset = result.assets[0];
      const { uri: originalUri } = asset;
      const fileName = originalUri.split('/').pop() || `evidence_${index + 1}.jpg`;

      try {
        const compressedUri = await compressImage(originalUri);

        const updatedImages = [...(clarificationForm.images || [])];
        updatedImages[index] = {
          uri: compressedUri.uri,
          fileName: fileName,
          type: 'image/jpeg',

        };

        setClarificationForm({
          ...clarificationForm,
          images: updatedImages,
        });
      } catch (error) {
        console.error('Error al comprimir imagen:', error);
      }
    }
  };


  const handleRemoveImage = (index: number) => {
    const updatedImages = [...(clarificationForm.images || [])];
    updatedImages.splice(index, 1);
    setClarificationForm({ ...clarificationForm, images: updatedImages });
  };

  return (
    <>
      <PageBaseBWReturnButton
        onGoBack={() => router.back()}
        topChildren={<ClarificationsHeader />}
        bottomChildren={
          <ClarificationsBody
            formData={clarificationForm}
            onSetFormData={setClarificationForm}
            handleRemoveImage={handleRemoveImage}
            handleImageChange={handleImageChange}
            handleSubmit={onSubmitForm}
          />
        }
      />
      <ClarificationsBottomModal
        isVisible={showModal}
        onConfirm={handleConfirmSubmission}
        onClose={() => setShowModal(false)}
      />
      {isLoading && <LoadingScreen />}
    </>
  );
};

export default Clarifications;
