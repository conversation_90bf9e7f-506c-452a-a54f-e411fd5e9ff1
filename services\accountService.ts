import { Transaction } from '@/shared/types/transaction/Transaction';
import api from './api/api';

export const getAccountDetails = async (email: string) => {
  try {
    const response = await api.get(`/account/details/${email}`);
    return response.data;
  } catch (error: any) {
    console.error(
      '<PERSON><PERSON><PERSON> al buscar el saldo actual por email:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message ||
        'Error al buscar el saldo actual por email',
    );
  }
};

export const getTransactions = async (
  email: string,
): Promise<Transaction[]> => {
  const response = await api.get(`/account/list-transfers/${email}`);
  return response.data;
};

export const getListTransactions = async (
  email: string,
  options: {
    initialDate: string;
    endDate: string;
    page: number;
    limit: number;
  },
): Promise<Transaction[]> => {
  const response = await api.get(`/account/list-movements/${email}`, {
    params: {
      initial_date: options.initialDate, // Parámetro inicial_date
      end_date: options.endDate, // Parámetro end_date
      page: options.page, // Parámetro page
      limit: options.limit, // Parámetro limit
    },
  });
  return response.data;
};
