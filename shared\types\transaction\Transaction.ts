export interface Transaction {
  id: string; // Identificador único de la transacción
  date: string; // Fecha de la transacción, en formato ISO
  dateFormatted: string; // Fecha formateada
  description: string; // Descripción de la transacción
  amount: string; // <PERSON><PERSON> de la transacción
  key: string;
  type: string;
  movementType: string; // Tipo de movimiento, puede ser "creditor" o "debtor"
  operationDate: string; // Fecha de la operación
  applicationDate: string; // Fecha de aplicación
  beneficiary_name: string;
  payment_type: string;
  beneficiary_account: string;
  bank_name: string;
}
