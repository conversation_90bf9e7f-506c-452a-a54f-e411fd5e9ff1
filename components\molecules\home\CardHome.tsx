import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import React from 'react';
import Eye<PERSON>hite from '@/components/icons/EyeWhite';
import CopyWhite from '@/components/icons/CopyWhite';
import useIsTablet from '@/hooks/useIsTablet';
import { Image } from 'expo-image';

type Props = {
  numberAccount: string;
  state: 'inactive' | 'active';
  onPressEye: () => void;
  onPressCopy: () => void;
};

const CardHome: React.FC<Props> = ({
  numberAccount,
  state,
  onPressEye,
  onPressCopy,
}) => {
  const isTablet = useIsTablet();

  // Definir factores de escala para tableta
  const scaleFactor = isTablet ? 1.5 : 1;

  const containerStyle = {
    ...styles.container,
    width: 150 * scaleFactor,
    height: 100 * scaleFactor,
  };

  const textStyle = {
    ...styles.text,
    fontSize: 10 * scaleFactor,
    lineHeight: 12 * scaleFactor,
  };

  const urlImage =
    state === 'inactive'
      ? require('@/assets/images/Card-1-1.png')
      : require('@/assets/images/Card-2-3.png');

  return (
    <View style={containerStyle}>
      <Image
        source={urlImage}
        style={styles.image}
        cachePolicy='memory-disk'
      />
      <View style={[
        styles.textContainer,
        {
          paddingBottom: isTablet ? 5 * scaleFactor : 0,
        },
      ]}>
        <Text style={[textStyle, { paddingLeft: 5 * scaleFactor }]}>CONVENIA</Text>
        <Text style={[textStyle, { top: 0 }]}>Número de cuenta convenia</Text>
        <Text style={[textStyle, { top: 0 }]}>{numberAccount}</Text>
        <View style={[styles.actions, { gap: 16 * scaleFactor, marginLeft: isTablet ? 6 : 0 }]}>
          <TouchableOpacity onPress={onPressEye}>
            <EyeWhite style={{
              transform: [
                { scale: scaleFactor },
              ],
            }} />
          </TouchableOpacity>
          <TouchableOpacity onPress={onPressCopy}>
            <CopyWhite
              style={{
                transform: [
                  { scale: scaleFactor },
                ],
              }}
            />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: 150,
    height: 100,
  },
  image: {
    objectFit: 'cover',
    backgroundColor: 'transparent',
    borderRadius: 10,
    height: '100%',
    width: '100%',
  },
  textContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
    gap: 4,
    paddingLeft: 7,
    paddingRight: 2,
    paddingVertical: 0,
  },
  text: {
    color: '#F4F5FB', // Adjust color as needed
    fontSize: 10,
    lineHeight: 12,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    gap: 16,
  },
});

export default CardHome;
