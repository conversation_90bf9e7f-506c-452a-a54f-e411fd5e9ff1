import { StyleSheet, View, Animated, Easing } from 'react-native';
import React, { useEffect, useRef } from 'react';
import LoadingIcon from '../icons/LoadingIcon';
import { useNavigation } from '@react-navigation/native';

const LoadingScreen = () => {
  const spinValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(1)).current;
  const opacityValue = useRef(new Animated.Value(0.4)).current;
  const navigation = useNavigation();

  useEffect(() => {
    // Rotación
    Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 10000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();

    // Efecto de pulsación (scaling)
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleValue, {
          toValue: 1.05,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 700,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Efecto de opacidad
    Animated.loop(
      Animated.sequence([
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(opacityValue, {
          toValue: 0.4,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();

  }, [spinValue, scaleValue, opacityValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Bloquear navegación hacia atrás
  useEffect(() => {
    navigation.setOptions({
      gestureEnabled: false,
    });

    // Restaurar navegación al salir de la pantalla
    return () => {
      navigation.setOptions({
        gestureEnabled: true,
      });
    };
  }, [navigation]);

  return (
    <View style={styles.wrapper}>
      <Animated.View
        style={{
          transform: [
            { rotate: spin },
            { scale: scaleValue }
          ],
          opacity: opacityValue
        }}
      >
        <LoadingIcon />
      </Animated.View>
    </View>
  );
};

export default LoadingScreen;

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: 'black',
    position: 'absolute',
    width: '100%',
    top: 0,
    left: 0,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.9,
    zIndex: 999,
  },
  logo: {
    width: 75,
    height: 75,
    objectFit: 'contain',
  },
  loadingText: {
    color: 'white',
    marginTop: 20,
    fontSize: 16,
    fontWeight: '500',
  },
});
