import { StyleSheet, View, Text } from 'react-native';
import React from 'react';
import { ThemedText } from '@/components/ui/ThemedText';
import ChangeItem from '@/components/molecules/transfers/ChangeItem';
import LineGradient from '@/components/atoms/LineGradient';
import { useTransferContext } from '@/context/TransferContext';

type Props = {
  goBackDestinationInfo: () => void;
  goBackAmountInfo: () => void;
};

const ReviewTransferHeader = ({
  goBackDestinationInfo,
  goBackAmountInfo,
}: Props) => {
  const { transferData, shouldShowCommission } = useTransferContext();
  const {
    amount,
    concept,
    name: destination,
    number: numberAccount,
    bank: bankName,
    commission = '0'
  } = transferData || {};
  const paddingHorizontal = 16
  return (
    <View style={styles.wrapper}>
      <ThemedText
        type='text24'
        lightColor='#F4F5FB'
        style={{ paddingHorizontal }}
      >
        Confirmación de información
      </ThemedText>
      <ChangeItem
        onBack={goBackDestinationInfo}
        containerStyle={{ paddingHorizontal }}
      >
        <Text style={styles.label}>Destinatario</Text>
        <Text style={styles.text}>{destination}</Text>
        <Text style={styles.label}>Cuenta {bankName}</Text>
        <Text style={styles.text}>{numberAccount}</Text>
      </ChangeItem>
      <LineGradient />
      <ChangeItem
        onBack={goBackAmountInfo}
        containerStyle={{ paddingHorizontal }}
      >
        <Text style={styles.label}>Importe</Text>
        <ThemedText type='defaultSemiBold' lightColor='#F4F5FB'>
          ${amount}
        </ThemedText>
      </ChangeItem>
      <LineGradient />
      <ChangeItem
        onBack={goBackAmountInfo}
        containerStyle={{ paddingHorizontal }}
      >
        <Text style={styles.label}>Concepto</Text>
        <Text style={styles.text}>{concept}</Text>
      </ChangeItem>
      {shouldShowCommission && (
        <>
          <LineGradient />
          <View style={[styles.content, { paddingHorizontal }]}>
            <Text style={styles.label}>Comisión</Text>
            <ThemedText type='defaultSemiBold' lightColor='#F4F5FB'>
              ${Number(commission).toFixed(2)}
            </ThemedText>
          </View>
        </>
      )}
    </View>
  );
};

export default ReviewTransferHeader;

const styles = StyleSheet.create({
  wrapper: {
    // paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 13,
    gap: 24,
  },
  label: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 16.94,
    color: '#F4F5FB',
  },
  text: {
    fontFamily: 'Inter',
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 14.52,
    color: '#F4F5FB',
  },
  content: {

  },
});
