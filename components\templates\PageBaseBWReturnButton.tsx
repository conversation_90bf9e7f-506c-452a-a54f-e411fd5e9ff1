import { TouchableOpacity, StyleSheet, View, Text, Platform } from 'react-native';
import React, { useState } from 'react';
import PageBaseTwiceBlackWhite from './PageBaseTwiceBlackWhite';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

type Props = {
  topChildren?: React.ReactNode;
  bottomChildren?: React.ReactNode;
  onGoBack?: () => void;
};

const PageBaseBWReturnButton = ({
  topChildren,
  bottomChildren,
  onGoBack,
}: Props) => {
  const [disabled, setDisabled] = useState(false);

  const handleGoBack = () => {
    if (disabled) return;
    setDisabled(true);
    try {
      if (onGoBack) {
        onGoBack();
      } else {
        router.back();
      }
    } finally {
      // Si quieres volver a habilitar el botón después de cierto tiempo, puedes usar setTimeout aquí.
      // Si no, el botón quedará deshabilitado tras el primer press.
    }
  };

  return (
    <PageBaseTwiceBlackWhite
      topChildren={
        <View>
          <View style={styles.containerHeader}>
            <TouchableOpacity
              style={{
                position: 'relative', zIndex: 2, flexDirection: 'row', alignItems: 'center', gap: 8,
              }}
              onPress={handleGoBack}
              disabled={disabled}
            >
              <Ionicons name='arrow-back' size={24} color='#ffffff' />
              {/* Icono de flecha hacia atrás */}
              <Text style={styles.text}>
                Atrás
                {/* Texto del botón de retroceso, por defecto 'Atrás' */}
              </Text>
            </TouchableOpacity>
          </View>

          {topChildren}
        </View>
      }
      bottomChildren={bottomChildren}
    />
  );
};

export default PageBaseBWReturnButton;

const styles = StyleSheet.create({
  text: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 17,
    color: '#ffffff',
  },
  containerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    // paddingVertical: 24,
    paddingTop: Platform.OS === 'ios' ? 0 : 7,
    // backgroundColor: 'red',
    paddingHorizontal: 16,
  },
});
