import { Keyboard, Pressable, StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import Input from '../atoms/Input';
import PrimaryButton from '../atoms/PrimaryButton';
import SecondaryButton from '../atoms/SecondaryButton';
import { ThemedText } from '../ui/ThemedText';
import PageBaseWhite from './PageBaseWithe';
import validatePasswordLength from '@/shared/validatePasswordLength';
import validatePasswordComplexity from '@/shared/validatePasswordComplexity';
import { useInactivity } from '@/context/InactivityContext';

type Props = {
  title: string;
  password: string;
  passwordError: string | null;
  titlePrimaryButton?: string;
  titleSecondaryButton?: string;
  loading?: boolean;
  onContinue: () => void;
  onGoBack: () => void;
  onSetPassword: (value: string) => void;
  onSetPasswordError: (value: string | null) => void;
};

const PageBaseEnterPassword = ({
  title,
  password,
  passwordError,
  titlePrimaryButton,
  titleSecondaryButton,
  loading,
  onContinue,
  onGoBack,
  onSetPassword,
  onSetPasswordError,
}: Props) => {
  const [showPassword, setShowPassword] = useState(false);
  const { resetInactivityTimer } = useInactivity();

  const isPasswordValid =
    validatePasswordLength(password) && validatePasswordComplexity(password) && !passwordError;

  return (
    <PageBaseWhite>
      <Pressable onPress={() => Keyboard.dismiss()} style={styles.wrapper}>
        <View style={styles.content}>
          <View style={styles.contentHeader}>
            <ThemedText type='title' lightColor='#232429'>
              {title}
            </ThemedText>
            <Input
              label='Contraseña'
              placeholder='Ingresa Contraseña'
              value={password}
              secureTextEntry={!showPassword}
              isError={!!passwordError}
              returnKeyType='done'
              icon={
                passwordError
                  ? 'alert-circle-outline'
                  : showPassword
                    ? 'eye-outline'
                    : 'eye-off-outline'
              }
              onPressIcon={() => setShowPassword(!showPassword)}
              onChangeText={text => {
                onSetPassword(text);
                onSetPasswordError(null); // Limpiar el error mientras escribe
                resetInactivityTimer(); // Reiniciar el temporizador de inactividad
              }}
              errorText={passwordError ? passwordError : 'La contraseña no cumple con los requisitos'}
            />
          </View>
        </View>
        <View style={styles.buttons}>
          <PrimaryButton
            title={titlePrimaryButton || 'Continuar'}
            disable={!isPasswordValid || loading} // Deshabilitar el botón si la contraseña no es válida
            onPress={onContinue} // Llamar a la función handleSave
          />
          <SecondaryButton
            title={titleSecondaryButton || 'Cancelar'}
            onPress={onGoBack} // Regresar a la pantalla anterior
            inverted
          />
        </View>
      </Pressable>
    </PageBaseWhite>
  );
};

export default PageBaseEnterPassword;
// Estilos para el componente
const styles = StyleSheet.create({
  safe: {
    flex: 1,
    backgroundColor: '#Ffffff',
  },
  wrapper: {
    flex: 1,
    backgroundColor: '#Ffffff',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
  },
  contentHeader: {
    gap: 24,
  },
  rememberContainer: {
    gap: 8,
  },
  rememberItme: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  rememberText: {
    color: '#606572',
    fontFamily: 'Roboto',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
  content: {
    gap: 24,
  },
  buttons: {
    flexDirection: 'column', // Cambiar a disposición vertical
    justifyContent: 'center', // Centrar los botones verticalmente
    alignItems: 'center', // Centrar horizontalmente (opcional)
    gap: 16, // Espaciado entre botones (alternativa a marginBottom)
    paddingVertical: 16, // Espaciado interno superior/inferior (opcional)
  },
});
