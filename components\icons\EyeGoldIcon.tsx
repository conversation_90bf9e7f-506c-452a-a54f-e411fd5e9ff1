import * as React from "react"
import Svg, {
    SvgProps,
    Path,
    Defs,
    LinearGradient,
    Stop,
} from "react-native-svg"
const EyeGoldIcon = (props: SvgProps) => (
    <Svg
        width={20}
        height={20}
        fill="none"
        {...props}
    >
        <Path
            fill="url(#a)"
            d="M10 5.179C5.284 5.179 1.562 10 1.562 10S5.284 14.822 10 14.822c3.606 0 8.438-4.822 8.438-4.822S13.605 5.179 10 5.179Zm0 7.825A3.008 3.008 0 0 1 6.996 10 3.008 3.008 0 0 1 10 6.996 3.008 3.008 0 0 1 13.004 10 3.008 3.008 0 0 1 10 13.004Zm0-4.758a1.754 1.754 0 1 0-.066 3.507A1.754 1.754 0 0 0 10 8.246Z"
        />
        <Defs>
            <LinearGradient
                id="a"
                x1={1.562}
                x2={18.438}
                y1={10.001}
                y2={10.001}
                gradientUnits="userSpaceOnUse"
            >
                <Stop offset={0.004} stopColor="#75532F" />
                <Stop offset={0.23} stopColor="#8B6539" />
                <Stop offset={0.395} stopColor="#9F7A49" />
                <Stop offset={0.506} stopColor="#AF8B55" />
                <Stop offset={0.741} stopColor="#DCB992" />
                <Stop offset={0.869} stopColor="#DAB890" />
                <Stop offset={0.916} stopColor="#D5B289" />
                <Stop offset={0.949} stopColor="#CCA87C" />
                <Stop offset={0.975} stopColor="#BF9B6B" />
                <Stop offset={0.998} stopColor="#AF8B56" />
                <Stop offset={0.999} stopColor="#AF8B55" />
                <Stop offset={1} stopColor="#E9D6B8" />
            </LinearGradient>
        </Defs>
    </Svg>
)
export default EyeGoldIcon
