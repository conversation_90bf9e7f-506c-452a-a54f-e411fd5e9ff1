import { StyleSheet, View } from 'react-native';
import React from 'react';
import BottomModal from '../BottomModal';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { ThemedText } from '@/components/ui/ThemedText';
import PrimaryButton from '@/components/atoms/PrimaryButton';

type Props = {
  showFingerprintModal: boolean;
  onCloseModalFingerprint: () => void;
};

const FingerBottomModal = ({
  showFingerprintModal,
  onCloseModalFingerprint,
}: Props) => {
  return (
    <BottomModal
      isVisible={showFingerprintModal}
      onClose={onCloseModalFingerprint}
    >
      <View style={styles.wrapper}>
        <MaterialIcons name='fingerprint' size={57} color='#9093A5' />
        <ThemedText type='caption' lightColor='#9093A5' style={styles.text}>
          Toca el sensor para activar el ingreso a la app con tu huella digital
        </ThemedText>
        <PrimaryButton title='Cancelar' onPress={onCloseModalFingerprint} />
      </View>
    </BottomModal>
  );
};

export default FingerBottomModal;

const styles = StyleSheet.create({
  wrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 31.5,
    paddingVertical: 24,
  },
  text: {
    marginTop: 24,
    marginBottom: 32,
    textAlign: 'center',
  },
});
