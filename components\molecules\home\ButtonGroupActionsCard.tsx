import React, { useState } from 'react';
import { View, Text, StyleSheet, Pressable } from 'react-native';
import MaskedView from '@react-native-masked-view/masked-view';
import { LinearGradient } from 'expo-linear-gradient';
import { router, useLocalSearchParams } from 'expo-router';
import BtnCard from '@/components/icons/BtnCard';
import BtnCardSearch from '@/components/icons/BtnCardSearch';
import BtnCardTransfer from '@/components/icons/BtnCardTransfer';

type Props = {
  idAccount: string;
}

const ButtonGroupActionsCard: React.FC<Props> = ({
  idAccount,
}) => {
  const { email } = useLocalSearchParams();
  const [isPressed, setIsPressed] = useState<null | number>(null);
  const [isNavigating, setIsNavigating] = useState(false);

  const handleNavigation = (navigateTo: () => void) => {
    if (isNavigating) return;
    setIsNavigating(true);
    navigateTo();
    // Restablecer el estado después de un breve retraso para asegurar que la navegación haya comenzado
    setTimeout(() => {
      setIsNavigating(false);
    }, 1000);
  };

  const buttons = [
    {
      image: <BtnCard />,
      label: 'Mis tarjetas',
      onPress: () => {
        handleNavigation(() => {
          router.push({
            pathname: '/(homeStack)/myCards',
            params: { email, idAccount },
          });
        });
      },
    },
    {
      image: <BtnCardSearch />,
      label: 'Ver cuenta',
      onPress: () => {
        handleNavigation(() => {
          router.push('/(homeStack)/home/<USER>');
        });
      },
    },
    {
      image: <BtnCardTransfer />,
      label: 'Transferencias',
      onPress: () => {
        handleNavigation(() => {
          router.push('/(homeStack)/transfers');
        });
      },
    },
  ];

  return (
    <View style={styles.buttonContainer}>
      {buttons.map((button, index) => (
        <View key={index} style={styles.buttonWrapper}>
          <Pressable
            style={[
              styles.roundButton,
              isNavigating && styles.disabledButton
            ]}
            onPress={button.onPress}
            onPressIn={() => !isNavigating && setIsPressed(index)}
            onPressOut={() => setIsPressed(null)}
            disabled={isNavigating}
          >
            {isPressed === index ? (
              <LinearGradient
                colors={[
                  '#DCB992',
                  '#DAB890',
                  '#D5B289',
                  '#CCA87C',
                  '#BF9B6B',
                  '#AF8B56',
                  '#AF8B55',
                  '#E9D6B8',
                ]}
                locations={[
                  0.019,
                  0.249,
                  0.459,
                  0.684,
                  0.9751,
                  0.9977,
                  0.9985,
                  1.0,
                ]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.roundButton}
              >
                {button.image}
              </LinearGradient>
            ) : (
              <>
                {button.image}
              </>
            )}
          </Pressable>
          <MaskedView
            style={{
              flexDirection: 'row',
              height: 17,
            }}
            maskElement={
              <View
                style={{
                  backgroundColor: 'transparent',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Text style={styles.subtitle}>{button.label}</Text>
              </View>
            }
          >
            <View style={{ flex: 0.04, height: '100%', backgroundColor: '#75532F' }} />
            <View style={{ flex: 0.19, height: '100%', backgroundColor: '#8B6539' }} />
            <View style={{ flex: 0.16, height: '100%', backgroundColor: '#9F7A49' }} />
            <View style={{ flex: 0.11, height: '100%', backgroundColor: '#AF8B55' }} />
            <View style={{ flex: 0.24, height: '100%', backgroundColor: '#DCB992' }} />
            <View style={{ flex: 0.13, height: '100%', backgroundColor: '#DAB890' }} />
            <View style={{ flex: 0.05, height: '100%', backgroundColor: '#D5B289' }} />
            <View style={{ flex: 0.03, height: '100%', backgroundColor: '#CCA87C' }} />
            <View style={{ flex: 0.03, height: '100%', backgroundColor: '#BF9B6B' }} />
            <View style={{ flex: 0.02, height: '100%', backgroundColor: '#AF8B56' }} />
            <View style={{ flex: 0.01, height: '100%', backgroundColor: '#AF8B55' }} />
            <View style={{ flex: 0.01, height: '100%', backgroundColor: '#E9D6B8' }} />
          </MaskedView>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    height: 100,
    paddingHorizontal: 24,
  },
  buttonWrapper: {
    alignItems: 'center',
    gap: 4,
    height: 100,
    flex: 1,
    justifyContent: 'center',
    maxWidth: 160,
  },
  roundButton: {
    backgroundColor: '#4A4B55',
    width: 52,
    height: 52,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.6,
  },
  subtitle: {
    color: '#F4F5FB',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Inter',
    lineHeight: 16.94,
    textRendering: 'geometricPrecision',
  },
});

export default ButtonGroupActionsCard;
