import React from 'react';
import {
  View,
  StyleSheet,
  Platform,
  ScrollView
} from 'react-native';
import PageBaseWithBackButton from '../../templates/PageBaseWithBackButton';
import PrimaryButton from '../../atoms/PrimaryButton';
import SecondaryButton from '../../atoms/SecondaryButton';
import { ThemedText } from '../../ui/ThemedText';
import VerifyEmailNumbersInputs from '../../molecules/VerifyEmailNumbersInputs';
import { useInactivity } from '@/context/InactivityContext';

// Definición de los tipos de propiedades que el componente acepta
type Props = {
  email: string | string[];
  hasError: boolean;
  number: string;
  onSetNumber: (number: string) => void;
  onContinue: () => void;
  onResendCode: () => void;
};

// Componente principal que maneja la verificación del número de correo electrónico
const VerifyEmailNumberToTransfer = ({
  email,
  number,
  hasError,
  onSetNumber,
  onContinue,
  onResendCode,
}: Props) => {
  // Hook personalizado para manejar la inactividad del usuario
  const { resetInactivityTimer } = useInactivity();
  // Deshabilitar el botón si el número no tiene 4 dígitos o hay un error
  const disableButton = number.length !== 4 || hasError;

  return (
    <PageBaseWithBackButton>
      {/* Dismiss el teclado cuando se presiona fuera de un input */}
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
        keyboardShouldPersistTaps='handled'
        contentContainerStyle={{
          flexGrow: 1,
          justifyContent: 'space-between',
          paddingHorizontal: 24,
        }}
      >
        <View style={styles.wrapper}>
          <View style={styles.body}>
            <View style={styles.contentHeader}>
              <ThemedText
                type='title'
                lightColor='#232429'
                style={styles.title}
              >
                Revisa tu correo
              </ThemedText>
              <ThemedText type='subtitle' lightColor='#232429'>
                Para garantizar la seguridad de tu transferencia, te hemos enviado un correo a: {email}
              </ThemedText>
            </View>
            <View style={styles.contentDescriptionWithNumbers}>
              <ThemedText type='subtitle' lightColor='#232429'>
                Por favor, ingrese el código de verificación que se envió a su
                correo electrónico.
              </ThemedText>
              <VerifyEmailNumbersInputs
                hasError={hasError}
                onNumberChange={number => {
                  onSetNumber(number)
                  // Reiniciar el temporizador de inactividad
                  resetInactivityTimer()
                }}
              />
            </View>
            <PrimaryButton
              title='Continuar'
              disable={disableButton}
              onPress={onContinue}
            />
          </View>
          <View style={styles.footerContainer}>
            <View style={styles.reSendContainer}>
              <ThemedText type='caption' lightColor='#232429'>
                ¿No has recibido el correo?
              </ThemedText>
              <SecondaryButton
                title='Reenviar código'
                inverted
                onPress={onResendCode}
              />
            </View>
            <View style={styles.footer}>
              <ThemedText
                type='caption'
                lightColor='#C3C6D8'
                style={{ textAlign: 'justify' }}
              >
                Si tienes algún problema con tu cuenta y ya intentaste reenviar
                el código, contacta a soporte técnico a este correo electrónico
                &nbsp;
                <ThemedText type='caption' lightColor='#0221BD'>
                  <EMAIL>
                </ThemedText>
              </ThemedText>
            </View>
          </View>
        </View>
      </ScrollView>
    </PageBaseWithBackButton >
  );
};

export default VerifyEmailNumberToTransfer;

// Estilos para el componente
const styles = StyleSheet.create({
  wrapper: {
    justifyContent: 'space-between',
    height: '100%',
  },
  body: {
    gap: 66,
  },
  contentHeader: {
    gap: 8,
  },
  contentDescriptionWithNumbers: {
    gap: 40,
  },
  reSendContainer: {
    gap: 24,
    alignItems: 'center',
  },
  title: {
    maxWidth: 342,
  },
  footer: {
    paddingBottom: Platform.OS === 'ios' ? 1 : 10,
  },
  email: {
    color: '#0221BD',
  },
  footerContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    gap: 30,
    maxHeight: 216,
    marginTop: 30,
  },
});
