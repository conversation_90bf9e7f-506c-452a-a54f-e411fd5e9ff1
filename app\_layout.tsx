import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from 'react';
import 'react-native-reanimated';
import { StatusBar } from 'react-native';
import { AlertProvider } from '../context/AlertContext';
import CustomAlertInactivity from '../components/ui/CustomAlertInactivity';
import { NotificationProvider } from '@/context/NotificationContext';
import * as Notifications from 'expo-notifications';

// notification setup
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});
// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded] = useFonts({
    Inter: require('../assets/fonts/Inter-Regular.ttf'),
    InterSemiBold: require('../assets/fonts/Inter-SemiBold.ttf'),
    PublicSansRegular: require('../assets/fonts/PublicSans-Regular.ttf'),
    PublicSansMedium: require('../assets/fonts/PublicSans-Medium.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <NotificationProvider>
      <AlertProvider>
        <StatusBar backgroundColor='#000000' barStyle='light-content' translucent={false} />
        <CustomAlertInactivity />
        <Stack
          screenOptions={{
            headerShown: false,
            animation: 'ios_from_right',
          }}
        >
        </Stack>
      </AlertProvider>
    </NotificationProvider>
  );
}
