import {
  Keyboard,
  Pressable,
  StyleSheet,
  View,
} from 'react-native';
import React from 'react';
// import useKeyboardVisibility from '@/hooks/useKeyboardVisibility';
import Input from '@/components/atoms/Input';
import PrimaryButton from '@/components/atoms/PrimaryButton';
import SecondaryButton from '@/components/atoms/SecondaryButton';
import InputMoney from '@/components/atoms/InputMoney';

type Props = {
  amountValue: string;
  concept: string;
  disabledNextBtn: boolean;
  errorAmount: string | null;
  errorConcept: string | null;
  onSetAmount: (value: string) => void;
  onSetConcept: (value: string) => void;
  onNext: () => void;
  onCancel: () => void;
};
const NewTransferSetAmountForm = ({
  amountValue,
  concept,
  disabledNextBtn,
  errorAmount,
  errorConcept,
  onSetAmount,
  onSetConcept,
  onCancel,
  onNext,
}: Props) => {

  return (
    <Pressable onPress={() => Keyboard.dismiss()} style={styles.wrapper}>
      <View style={{ gap: 24 }}>
        <InputMoney
          label='Escribe el monto a transferir'
          placeholder=''
          value={amountValue}
          isError={!!errorAmount}
          keyboardType='numeric'
          errorText={errorAmount || ''}
          returnKeyType='done'
          onChangeText={onSetAmount}
        />

        <Input
          label='Concepto'
          placeholder='Escriba el concepto'
          value={concept}
          isError={!!errorConcept}
          errorText={errorConcept || ''}
          returnKeyType='done'
          maxLength={30}
          onChangeText={onSetConcept}
        />
      </View>
      <View style={{ gap: 24 }}>
        <PrimaryButton
          title='Siguiente'
          disable={disabledNextBtn}
          onPress={onNext}
        />
        <SecondaryButton inverted title='Cancelar' onPress={onCancel} />
      </View>
    </Pressable>
  );
};

export default NewTransferSetAmountForm;

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    // backgroundColor: 'red',
    backgroundColor: '#E6E8F4',
    gap: 24,
  },
  centerContent: {
    // backgroundColor: 'red',
    // justifyContent: 'center',
    justifyContent: 'space-between',
  },
  wrapper: {
    flex: 1,
    backgroundColor: '#F4F5FB',
    padding: 24,
    justifyContent: 'space-between',
    gap: 24,
  },
});
