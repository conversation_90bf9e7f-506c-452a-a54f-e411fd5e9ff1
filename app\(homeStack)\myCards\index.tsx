import React, { useState, useCallback } from 'react';
import PageBaseBWReturnButton from '@/components/templates/PageBaseBWReturnButton';
import SeeMyCardsHeader from '@/components/molecules/myCards/SeeMyCardsHeader';
import CreditCardsList from '@/components/molecules/myCards/CreditCardsList';
import getUserCards from '@/services/cards/getUserCards';
import LoadingScreen from '@/components/molecules/LoadingScreen';
import type { Card } from '@/types/services/cards';
import { useLocalSearchParams, useFocusEffect } from 'expo-router';
import { useAppContext } from '../../../context/AppProvider';
import { DecryptAsync } from '@/shared/decrypted';

// Componente funcional SeeAccount
const MyCards = () => {
  // cardRegistered: obtenemos el estado de cardRegistered del contexto
  const { cardsLoading, setCardsLoading } = useAppContext();
  const { idAccount } = useLocalSearchParams();
  // Add a ref to track if this is the first render
  const [cards, setCards] = useState<Card[]>([]);
  // loading cards
  const [loading, setLoading] = useState(true);

  const fetchCards = useCallback(() => {
    getUserCards(idAccount as string).then((res) => {
      if (!res.data || res.data.length === 0) {
        setCards([]);
        setTimeout(() => {
          setLoading(false);
        }, 50);
        return;
      }

      const decryptedCardsPromises = res.data.map(async (card: Card) => {
        const [pan, card_last_4, expiration_date, format_expiration_date] = await Promise.all([
          DecryptAsync(card.pan),
          DecryptAsync(card.card_last_4),
          DecryptAsync(card.expiration_date),
          DecryptAsync(card.format_expiration_date)
        ]);
        return {
          ...card,
          pan,
          card_last_4,
          expiration_date,
          format_expiration_date,
        };
      });

      Promise.all(decryptedCardsPromises)
        .then(decryptedCards => {
          setCards(decryptedCards);
          // calculamos el tiempo que le daremos a cada tarjeta para que se renderice
          const cardsLength = res.data.length;
          // le daremos un tiempo de 50ms por tarjeta
          const time = cardsLength * 50;
          setTimeout(() => {
            setLoading(false);
            setCardsLoading(false);
          }, time);
        })
        .catch(error => {
          console.error("Error decrypting cards:", error);
          setCardsLoading(false);
          setLoading(false);
        });
    }).catch((err) => {
      setCardsLoading(false);
      setLoading(false);
    });
  }, [idAccount, setCardsLoading]);

  // Use useFocusEffect to run fetchCards every time the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (loading || cardsLoading) {
        fetchCards();
      }
    }, [cardsLoading, fetchCards, loading])
  );
  return (
    // Renderiza el componente PageBaseBWReturnButton
    // topChildren: Contenido superior, en este caso un View con un Text
    // bottomChildren: Contenido inferior, en este caso el SeeAccountDetails
    <>
      <PageBaseBWReturnButton
        topChildren={<SeeMyCardsHeader loading={loading} haveCards={cards.length > 0} />}
        bottomChildren={<CreditCardsList cards={cards} />}
      />
      {(loading || cardsLoading) && <LoadingScreen />}
    </>
  );
};

// Exporta el componente MyCards como el valor por defecto
export default MyCards;
