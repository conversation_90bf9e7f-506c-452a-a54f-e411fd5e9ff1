import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import { router } from 'expo-router';
import { Transaction } from '@/shared/types/transaction/Transaction';
import CompanyIcon from '@/components/icons/CompanyIcon';

// Definición del tipo de las propiedades que el componente recibirá
type Props = {
  item: Transaction;
};

// Componente funcional que representa un ítem de transacción
const TransactionItem = ({ item }: Props) => {
  const [isNavigating, setIsNavigating] = useState(false);

  const handlePress = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.push(
      `/(homeStack)/movements/${item.key}?amount=${encodeURIComponent(item.amount)}
      &date=${item.dateFormatted}&description=${item.description}&id=${item.key}
      &movementType=${item.movementType}&operationDate=${item.operationDate}
      &applicationDate=${item.applicationDate}&paymentType=${item.payment_type}&beneficiaryName=${item.beneficiary_name}
      &beneficiaryAccount=${item.beneficiary_account}&bankName=${item.bank_name}`,
    );
    // Resetear el estado después de un tiempo prudente
    setTimeout(() => setIsNavigating(false), 1000);
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      disabled={isNavigating}
    >
      <View style={styles.transactionItem}>
        <View style={styles.transactionDetails}>
          <CompanyIcon
            style={styles.transactionImage}
          />
          <View
            style={{
              maxWidth: '100%',
              flexDirection: 'column',
              flex: 1,
              flexShrink: 1, // Contracción flexible del elemento
            }}
          >
            <Text style={styles.transactionDate}>{item.dateFormatted}</Text>
            {/* Fecha de la transacción */}
            <Text style={styles.transactionDescription} numberOfLines={1} ellipsizeMode="tail"
            >
              {item.description}
            </Text>
            {/* Descripción de la transacción */}
            <Text style={styles.transactionKey} numberOfLines={1} ellipsizeMode="tail"
            >{item.key}</Text>
            {/* Clave única de la transacción */}
          </View>
        </View>
        <Text style={styles.transactionAmount}>{item.amount}</Text>
        {/* Monto de la transacción */}
      </View>
    </TouchableOpacity>
  );
};

export default TransactionItem;

// Estilos para el componente
const styles = StyleSheet.create({
  transactionItem: {
    flexDirection: 'row', // Elementos en fila
    justifyContent: 'space-between', // Espacio entre los elementos
    alignItems: 'center', // Alineación centrada verticalmente
    gap: 8, // Espacio entre los elementos
  },
  transactionImage: {
    width: 40, // Ancho de la imagen
    height: 40, // Altura de la imagen
    borderRadius: 25, // Radio de borde para hacer la imagen circular
  },
  transactionDetails: {
    flex: 1, // Flexibilidad del contenedor
    flexDirection: 'row', // Elementos en fila
    alignItems: 'center', // Alineación centrada verticalmente
    gap: 8, // Espacio entre los elementos
  },
  transactionDate: {
    fontSize: 14, // Tamaño de fuente
    color: '#232429', // Color del texto
    fontWeight: '600', // Grosor de la fuente
    fontFamily: 'Inter', // Familia de la fuente
    lineHeight: 16.94, // Altura de línea
    flexShrink: 1, // Contracción flexible del elemento

  },
  transactionDescription: {
    fontSize: 14, // Tamaño de fuente
    marginTop: 4, // Margen superior
    color: '#232429', // Color del texto
    fontWeight: '400', // Grosor de la fuente
    fontFamily: 'Inter', // Familia de la fuente
    lineHeight: 16.94, // Altura de línea
    flexShrink: 1, // Contracción flexible del elemento
  },
  transactionKey: {
    fontSize: 12, // Tamaño de fuente
    marginTop: 2, // Margen superior
    color: '#9093A5', // Color del texto
    fontWeight: '400', // Grosor de la fuente
    fontFamily: 'Inter', // Familia de la fuente
    lineHeight: 14.52, // Altura de línea
    flexShrink: 1, // Contracción flexible del elemento

  },
  transactionAmount: {
    fontSize: 14, // Tamaño de fuente
    color: '#232429', // Color del texto
    fontWeight: '600', // Grosor de la fuente
    fontFamily: 'Inter', // Familia de la fuente
    lineHeight: 16.94, // Altura de línea
    textRendering: 'geometricPrecision', // Precisión geométrica en el renderizado del texto
    flexShrink: 1, // Contracción flexible del elemento
    minWidth: 50,
  },
});
