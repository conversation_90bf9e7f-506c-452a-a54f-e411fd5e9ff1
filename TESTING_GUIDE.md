# Guía de Pruebas - Generación de PDF para Estados de Cuenta

## 🎯 Objetivo
Esta guía te ayudará a probar la funcionalidad de generación de PDF para estados de cuenta en la aplicación móvil Finberry.

## 🔧 Cambios Realizados

### 1. Archivo `utils/generateStatementPDF.ts`
- ✅ Eliminado código duplicado y reorganizado
- ✅ Agregadas funciones `getLastMonthTransactions` y `getTransactionsByDateRange`
- ✅ Corregida función `generateStatementPDF` y `generateLastPeriodStatementPDF`
- ✅ Implementada función `getHTMLTemplate` para generar las páginas HTML
- ✅ Mejorada función `convertTransactionsToMovements`
- ✅ Corregida función `calculateBalanceFromTransactions`

### 2. Archivo `components/organisms/movements/Historial.tsx`
- ✅ Agregada función `formatCurrency`
- ✅ Corregidos tipos de TypeScript para las transacciones
- ✅ Importado tipo `Transaction` correctamente

### 3. Templates HTML
- ✅ Agregados placeholders para imágenes en `statementTemplate.html`
- ✅ Agregados placeholders para imágenes en `statementTemplateContinuation.html`
- ✅ Implementada carga de imágenes SVG como base64 para compatibilidad móvil
- ✅ Imágenes del header: `header-golden-block.svg` y `header-pattern.svg`

### 4. Archivos de Prueba
- ✅ Creado `utils/testPDFGeneration.ts` con datos de prueba
- ✅ Creado `components/test/PDFTestScreen.tsx` para pruebas interactivas

## 🧪 Cómo Probar

### Opción 1: Usar el Componente Historial Existente
1. Abre la aplicación en Expo Go
2. Navega a la pantalla de Historial de movimientos
3. Selecciona "Último período" o configura un rango de fechas personalizado
4. Presiona "Descargar estado de cuenta"
5. Verifica que se genere y comparta el PDF correctamente

### Opción 2: Usar el Componente de Prueba
1. Importa el componente de prueba en tu navegación:
```tsx
import PDFTestScreen from '@/components/test/PDFTestScreen';
```

2. Agrega una ruta temporal para acceder a las pruebas
3. Ejecuta las tres pruebas disponibles:
   - **Prueba Básica**: PDF con 5 transacciones
   - **Prueba Sin Datos**: PDF sin movimientos
   - **Prueba Paginación**: PDF con 20 transacciones (múltiples páginas)

### Opción 3: Prueba Programática
```tsx
import { testPDFGeneration } from '@/utils/testPDFGeneration';

// En cualquier componente o función
const runTest = async () => {
  try {
    await testPDFGeneration();
    console.log('✅ PDF generado exitosamente');
  } catch (error) {
    console.error('❌ Error:', error);
  }
};
```

## 🔍 Qué Verificar

### Funcionalidad Básica
- [ ] El PDF se genera sin errores
- [ ] El PDF se abre correctamente en el visor
- [ ] El PDF se puede compartir a otras aplicaciones
- [ ] Los datos del usuario aparecen correctamente

### Contenido del PDF
- [ ] Header con imágenes SVG cargadas correctamente
- [ ] Bloque dorado (header-golden-block.svg) visible
- [ ] Patrón de fondo (header-pattern.svg) visible
- [ ] Información general del usuario
- [ ] Información financiera (saldos, depósitos, retiros)
- [ ] Tabla de movimientos con datos correctos
- [ ] Footer con información de la empresa

### Casos Especiales
- [ ] PDF sin movimientos muestra mensaje apropiado
- [ ] PDF con muchos movimientos genera múltiples páginas
- [ ] Paginación funciona correctamente
- [ ] Fechas se formatean correctamente
- [ ] Montos se formatean en pesos mexicanos

### Rendimiento
- [ ] La generación no toma más de 10 segundos
- [ ] No hay memory leaks o crashes
- [ ] La aplicación responde durante la generación

## 🐛 Problemas Conocidos y Soluciones

### Error: "Cannot find module"
- Verifica que todas las importaciones estén correctas
- Ejecuta `npm install` o `yarn install`

### Error: "Template not found"
- Verifica que los archivos HTML estén en `assets/templates/`
- La función `copyTemplateToDocuments` debe ejecutarse correctamente

### PDF en blanco o con errores de formato
- Verifica que los placeholders `{{variable}}` en los templates coincidan
- Revisa que no haya caracteres especiales en los datos

### Imágenes no aparecen
- Las imágenes SVG se cargan automáticamente como base64
- Si las imágenes no aparecen, verifica que los archivos estén en `assets/images/`
- Los archivos requeridos son: `header-golden-block.svg` y `header-pattern.svg`
- Si hay errores de carga, se usa una imagen transparente como fallback

## 📱 Pruebas en Dispositivo

### Android (Expo Go)
1. Escanea el QR code de Expo
2. Navega a la funcionalidad de PDF
3. Verifica que el PDF se genere y comparta correctamente
4. Prueba en diferentes versiones de Android si es posible

### iOS (Expo Go)
1. Abre el link de Expo en iOS
2. Repite las mismas pruebas que en Android
3. Verifica la compatibilidad con el sistema de archivos de iOS

## 📊 Métricas de Éxito
- ✅ PDF se genera en menos de 10 segundos
- ✅ PDF contiene todos los datos esperados
- ✅ PDF se puede compartir sin errores
- ✅ No hay crashes durante la generación
- ✅ Funciona en Android e iOS
- ✅ Paginación funciona con muchos datos
- ✅ Casos edge (sin datos) se manejan correctamente

## 🚀 Próximos Pasos
1. Probar en dispositivos físicos
2. Optimizar rendimiento si es necesario
3. Agregar más opciones de personalización
4. Implementar cache para templates
5. Agregar analytics para monitorear uso

## 📞 Soporte
Si encuentras algún problema durante las pruebas, revisa:
1. Los logs de la consola
2. Los errores específicos en el catch
3. La documentación de expo-print
4. Los templates HTML para errores de sintaxis
