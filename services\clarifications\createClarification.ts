import type {
  ClarificationData,
  ClarificationResponseData,
  RNFile,
} from '@/types/clarificationsTypes';
import api from '../api/api';

export const createClarification = async (
  data: ClarificationData,
): Promise<ClarificationResponseData> => {
  try {
    const response = await api.post(`/clarification/save`, data);
    return response.data?.data;
  } catch (error: any) {
    console.error(
      'Error al crear la aclaración:',
      error?.response?.data || error,
    );
    throw new Error(
      error?.response?.data?.message || 'Error al crear la aclaración',
    );
  }
};

// Save one or multiple files from Clarification
export const saveClarificationFiles = async (
  id: string,
  files: RNFile[] | RNFile,
): Promise<boolean> => {
  try {
    const formData = new FormData();

    const appendFile = (file: RNFile) => {
      if (!file.type?.startsWith('image/')) {
        throw new Error(`Tipo de archivo no permitido: ${file.type}`);
      }

      formData.append('files', {
        uri: file.uri,
        name: file.fileName,
        type: file.type,
      } as any);
    };

    const fileArray = Array.isArray(files) ? files : [files];

    if (fileArray.length > 5) {
      throw new Error('Solo puedes subir hasta 5 archivos');
    }

    fileArray.forEach(appendFile);

    const response = await api.post(`/clarification/${id}/files`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response.status < 200 || response.status >= 300) {
      throw new Error('Error al guardar archivo(s) de la aclaración');
    }

    return true;
  } catch (error) {
    console.error('❌ Error en saveClarificationFiles:', error);
    throw new Error('No se pudo subir archivo(s) de la aclaración');
  }
};
