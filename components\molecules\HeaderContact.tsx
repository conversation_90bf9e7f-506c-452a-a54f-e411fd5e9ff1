import { StyleSheet, Text, View } from 'react-native';
import { ThemedText } from '../ui/ThemedText';

const HeaderContact = () => {
  return (
    <View style={styles.topContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>Contacto</Text>
        <ThemedText type='defaultSemiBold' lightColor='#F4F5FB'>
          Elige la manera en que deseas contactarnos
        </ThemedText>
      </View>
    </View>
  );
};

export default HeaderContact;

const styles = StyleSheet.create({
  topContainer: {
    backgroundColor: '#000',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    color: '#F4F5FB',
    fontFamily: 'Inter',
    lineHeight: 29.05,
    textRendering: 'geometricPrecision',
    fontWeight: '400',
    marginBottom: 24,
    marginTop: 24,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
});
