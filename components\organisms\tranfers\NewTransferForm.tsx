import { View, StyleSheet } from 'react-native';
import React, { useEffect, useState } from 'react';
import Input from '@/components/atoms/Input';
import { FormData } from '@/components/organisms/tranfers/NewTransferContent';
import validateOnlyNumbers from '@/shared/validateOnlyNumbers';
import { ThemedText } from '@/components/ui/ThemedText';
import GoldCheckBox from '@/components/atoms/GoldCheckBox';
import { useBankContext } from '@/context/BankContext';
import { useInactivity } from '@/context/InactivityContext';
import { useUserContext } from '@/context/UserContext';
import { getContactsToTransfer } from '@/services/userService';

type Props = {
  errorNumber: string | undefined;
  formData: FormData;
  saveContact: boolean;
  onSetFormData: (data: FormData) => void;
  onSetSaveContact: (save: boolean) => void;
  setErrorNumber: (error: string | undefined) => void;
  errorName: string | undefined;
  setErrorName: (error: string | undefined) => void;
};

const NewTransferForm = ({
  errorNumber,
  formData,
  saveContact,
  onSetFormData,
  onSetSaveContact,
  setErrorNumber,
  errorName,
  setErrorName,
}: Props) => {
  const { banks } = useBankContext();
  const { user } = useUserContext();
  const [isNotDetectedBank, setIsNotDetectedBank] = useState(false);
  const [isSavedContact, setIsSavedContact] = useState(false);
  const [inputMaxLength, setInputMaxLength] = useState(18);
  const [contacts, setContacts] = useState<any[]>([]);
  const { resetInactivityTimer } = useInactivity();

  const detectBankByNumber = (number: string) => {
    if (number.startsWith('527') || number.startsWith('221')) return { name: 'CONVENIA' }; // Caso especial
    const code = number.slice(0, 3);

    const matchedBank = banks.find(bank => bank.legalCode === code);

    if (code.length >= 3) {
      if (matchedBank) {
        return matchedBank;
      } else {
        // Si no se encuentra un banco coincidente, retorna Banco no detectado
        return 'Banco no detectado';
      }
    }
    return '';
  };

  const handleSetNumber = (text: string) => {
    resetInactivityTimer();
    setIsSavedContact(false);
    const numericText = validateOnlyNumbers(text);

    // Set maxLength basado en el prefijo
    if (numericText.startsWith('221')) {
      setInputMaxLength(11);
    } else if (numericText.startsWith('527')) {
      setInputMaxLength(16);
    } else {
      setInputMaxLength(18); // CLABE
    }

    const isValid = isValidCardOrClabe(numericText);
    const detectedBank = detectBankByNumber(numericText);

    onSetFormData({
      ...formData,
      number: numericText,
      bank: typeof detectedBank === 'object' && detectedBank !== null && 'name' in detectedBank ? detectedBank.name : '',
      beneficiaryBank: typeof detectedBank === 'object' && detectedBank !== null && 'code' in detectedBank ? detectedBank.code : '',
    });

    if (isValid) {
      setErrorNumber(undefined);
      if (detectedBank === 'Banco no detectado') {
        setIsNotDetectedBank(true);
      } else {
        setIsNotDetectedBank(false);
      }
    } else {
      setIsNotDetectedBank(false);
      setErrorNumber('Escribe un número válido.');
    }

    if (saveContact && isNumberAlreadyInContacts(numericText)) {
      setIsSavedContact(true);
    }
  };

  const isValidCardOrClabe = (text: string) => {
    // if (text.length < 16) {
    //   return true;
    // }
    const cardRegex = text.startsWith('221') ? /^[0-9]{11}$/ : /^[0-9]{16}$/; // Example for credit card (16 digits)
    const clabeRegex = /^[0-9]{18}$/; // Example for CLABE (18 digits)
    return cardRegex.test(text) || clabeRegex.test(text);
  };

  const isNumberAlreadyInContacts = (number: string) => {
    return contacts?.some(contact => contact.num_clabe === number);
  };

  const handleCheckSaveContact = () => {
    const alreadyExists = isNumberAlreadyInContacts(formData.number || '');
    if (alreadyExists) {
      onSetSaveContact(false); // desactiva el checkbox automáticamente
      setIsSavedContact(true);
    } else {
      setErrorNumber(undefined);
    }
    onSetSaveContact(!saveContact); // desactiva el checkbox automáticamente
  };

  const handleSetName = (value: string) => {
    resetInactivityTimer();
    onSetFormData({ ...formData, name: value });
    if (value.length === 0) {
      setErrorName('El nombre es obligatorio');
      return;
    }
    // No permitir la letra ñ o Ñ en cualquier parte
    if (/[ñÑ]/.test(value)) {
      setErrorName('No se permite la letra ñ');
      return;
    }
    // No permitir acentos
    const accentRegex = /[áéíóúÁÉÍÓÚüÜ]/;
    if (accentRegex.test(value)) {
      setErrorName('No se permiten acentos');
      return;
    }
    // Validar caracteres especiales o números
    const regex = /^[a-zA-Z\s]+$/;
    if (!regex.test(value)) {
      setErrorName('El nombre solo puede contener letras y espacios');
      return;
    }
    if (value.length < 1 || value.length > 30) {
      setErrorName('El nombre debe tener máximo 30 caracteres');
      return;
    }
    setErrorName(undefined);
  };

  useEffect(() => {
    const number = formData.number || '';
    const alreadyExists = isNumberAlreadyInContacts(number);

    if (saveContact && alreadyExists) {
      // Si ya estaba marcado y luego se detecta un número repetido
      onSetSaveContact(false); // desmarcar
      setIsSavedContact(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.number, saveContact]);

  useEffect(() => {
    const fetchContacts = async () => {
      const { data } = await getContactsToTransfer(user?.id!);
      setContacts(data);
    };
    fetchContacts();
  }, [user?.id]);

  return (
    <View style={{ gap: 24 }}>
      <Input
        label='Nombre del destinatario'
        placeholder='Destinatario'
        value={formData.name || ''}
        isError={!!errorName}
        errorText={errorName}
        maxLength={30}
        onChangeText={handleSetName}
      />
      <Input
        label='Escribe el número de tarjeta / CLABE'
        placeholder='Ingresa número de tarjeta'
        value={formData.number || ''}
        isError={!!errorNumber}
        errorText={errorNumber}
        keyboardType='numeric'
        maxLength={inputMaxLength}
        returnKeyType='done'
        onChangeText={handleSetNumber}
      />
      {isSavedContact && (
        <ThemedText type='caption' lightColor='#4B9EF2'>
          Este destinatario ya está registrado en la lista de contactos.
        </ThemedText>
      )}
      {isNotDetectedBank && (
        <ThemedText type='caption' lightColor='#b6b6b6'>
          No se puede validar el banco con el número de tarjeta / CLABE
        </ThemedText>
      )}
      <View style={{ gap: 6 }}>
        <Input
          label='Entidad bancaria'
          placeholder=''
          value={formData.bank || ''}
          isError={false}
          onChangeText={text => {
            onSetFormData({ ...formData, bank: formData.bank })
            resetInactivityTimer(); // Reiniciar el temporizador de inactividad
          }}
          disabled
        />
      </View>
      <View style={styles.saveContactContainer}>
        <GoldCheckBox
          checked={saveContact}
          onPress={() => {
            handleCheckSaveContact();
          }}
        />
        <ThemedText type='caption' lightColor='#4A4B55' style={{ width: 342 }}>
          ¿Quieres guardar en tus contactos este destinatario?
        </ThemedText>
      </View>
    </View>
  );
};

export default NewTransferForm;

const styles = StyleSheet.create({
  saveContactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    width: '100%',
    height: 'auto',
  },
});
