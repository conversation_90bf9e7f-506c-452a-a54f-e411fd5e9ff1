import { Bank, getBanks } from '@/services/transfersService';
import React, { createContext, useState, useContext, ReactNode, useCallback } from 'react';

// Tipo del contexto: incluye el array de bancos y la función para actualizarlo
interface BankContextType {
  banks: Bank[];
  setBanks: (banks: Bank[]) => void;
  fetchBanks: () => Promise<void>;
}

// Crear el contexto con valor inicial `undefined`
const BankContext = createContext<BankContextType | undefined>(undefined);

// Provider para envolver la app o secciones que lo usen
export const BankProvider = ({ children }: { children: ReactNode }) => {
  const [banks, setBanksState] = useState<Bank[]>([]);

  const setBanks = (newBanks: Bank[]) => {
    setBanksState(newBanks);
  };

  const fetchBanks = useCallback(async () => {
    if (banks.length) return
    try {
      const response = await getBanks();
      setBanks(response.banks);
    } catch (error) {
      console.error('Error al obtener los bancos:', error);
    }
  }, [banks])

  return (
    <BankContext.Provider value={{ banks, setBanks, fetchBanks }}>
      {children}
    </BankContext.Provider>
  );
};

// Hook personalizado para consumir el contexto
export const useBankContext = () => {
  const context = useContext(BankContext);
  if (!context) {
    throw new Error('useBankContext debe usarse dentro de un BankProvider');
  }
  return context;
};
