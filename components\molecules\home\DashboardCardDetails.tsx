import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { ThemedText } from '@/components/ui/ThemedText';
import CardHome from './CardHome';
import { formatCurrency } from '@/shared/utils/formatCurrency';
// import EyeGoldIcon from '@/components/icons/EyeGoldIcon';
import CopyGoldIcon from '@/components/icons/CopyGoldIcon';
import useIsTablet from '@/hooks/useIsTablet';
import SkeletonLoader from '@/components/atoms/SkeletonLoader';
import * as Clipboard from "expo-clipboard";

// Definición de los tipos de propiedades que acepta el componente
type Props = {
  mode: 'light' | 'dark';
  isActiveAccount: boolean;
  onSetShowCopyCLABEAlert: () => void;
  onSetShowCopyAccountAlert: () => void;
  availableResource: number;
  membership_number?: string;
  idAccount: string;
  // clabe: string;
  loading?: boolean;
};

// Componente principal que muestra los detalles de la tarjeta del dashboard
const DashboardCardDetails = ({
  mode,
  isActiveAccount,
  onSetShowCopyCLABEAlert,
  onSetShowCopyAccountAlert,
  availableResource,
  membership_number,
  idAccount,
  // clabe,
  loading,
}: Props) => {
  const isTablet = useIsTablet();
  // Definición del color del texto basado en el modo (claro u oscuro)
  const colorTexts = mode === 'light' ? '#F4F5FB' : '#000';

  // Estado para mostrar u ocultar el número de Clabe
  // const [isClabeVisible, setIsClabeVisible] = useState(false);

  // estado apra mostrar u ocultar el numero de cuenta
  const [isAccountVisible, setIsAccountVisible] = useState(false);

  // estado para guardar el numero cuenta convenia
  const [accountNumber, setAccountNumber] = useState(idAccount);
  // Mostrar solo la parte antes del primer guion
  const formattedAccountNumber = accountNumber;

  // estado para guardar la clabe transfer
  const [clabeTransfer, setClabeTransfer] = useState(membership_number);

  // Sincronizar el estado `accountNumber` con la prop `idAccount`
  useEffect(() => {
    setAccountNumber(idAccount);
  }, [idAccount]);
  // Sincronizar el estado `clabeTransfer` con la prop `clabe`
  useEffect(() => {
    setClabeTransfer(membership_number);
  }, [membership_number]);

  // Obtener los últimos 3 dígitos del número de cuenta
  const lastThreeDigits = accountNumber ? formattedAccountNumber.slice(-3) : '***';

  // Obtener los últimos 4 dígitos de la Clabe
  // const lastFourDigitsClabe = clabeTransfer ? clabeTransfer.slice(-4) : '0123';

  // Número de Clabe completo (deberías reemplazarlo con el valor real de tu app)
  const clabeNumber = clabeTransfer;

  // Función para copiar la Clabe al portapapeles
  const copyToClipboard = () => {
    if (!clabeNumber) return
    Clipboard.setStringAsync(clabeNumber);
    onSetShowCopyCLABEAlert();
  };

  // funcion para copiar el numero de cuenta al portapapeles
  const copyAccountToClipboard = () => {
    Clipboard.setStringAsync(formattedAccountNumber);
    onSetShowCopyAccountAlert();
  };

  // Color del skeleton basado en el modo
  const skeletonColor = mode === 'light' ? '#434343' : '#********';

  return (
    // Contenedor principal del componente
    <View style={styles.wrapper}>
      <View
        style={{
          gap: 18,
        }}
      >
        <View
          style={{
            gap: 6,
          }}
        >
          {/* Texto temático que muestra "Saldo actual" */}
          <ThemedText lightColor={colorTexts} type='caption'>
            Saldo actual
          </ThemedText>
          {/* Texto que muestra el monto del saldo */}
          {loading ? (
            <SkeletonLoader width={150} height={26} style={{ backgroundColor: skeletonColor }} />
          ) : (
            <Text
              style={[
                styles.amount,
                {
                  color: colorTexts,
                },
              ]}
            >
              {formatCurrency(availableResource)}
            </Text>
          )}
        </View>
        <View
          style={{
            gap: 6,
          }}
        >
          {/* Texto temático que muestra "Número de Clabe" */}
          <ThemedText type='caption' lightColor={colorTexts}>
            Número de afiliación
          </ThemedText>
          {/* Texto temático que muestra el número de Clabe */}
          {loading ? (
            <SkeletonLoader width={150} height={16} style={{ backgroundColor: skeletonColor }} />
          ) : (
            <ThemedText type='caption' lightColor={colorTexts}>
              {/* {isClabeVisible ? clabeNumber : `**** **** **** ${lastFourDigitsClabe}`} */}
              {membership_number ? membership_number : '-'}
            </ThemedText>
          )}
          {/* Contenedor de las acciones de la tarjeta */}
          <View style={styles.containerActions}>
            {/* Botón para mostrar el icono del ojo */}
            {/* <TouchableOpacity
              onPress={() => setIsClabeVisible(!isClabeVisible)}
            >
              <EyeGoldIcon />
            </TouchableOpacity> */}
            {/* Botón para mostrar el icono de copiar */}
            <TouchableOpacity onPress={copyToClipboard}>
              <CopyGoldIcon />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      {/* Contenedor de la imagen de la tarjeta */}
      <View style={[
        styles.cardContainer,
        {
          paddingBottom: isTablet ? 20 : 0, // Ajusta el margen inferior según el tamaño de la pantalla
        }
      ]}>
        {
          !loading && (
            <CardHome
              numberAccount={isAccountVisible ? formattedAccountNumber : `***${lastThreeDigits}`}
              state={isActiveAccount ? 'active' : 'inactive'}
              onPressEye={() => setIsAccountVisible(!isAccountVisible)}
              onPressCopy={copyAccountToClipboard}
            />
          )
        }
      </View>
    </View>
  );
};

export default DashboardCardDetails;

// Definición de los estilos del componente
const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subtitle: {
    color: '#F4F5FB',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Inter',
    lineHeight: 16.94,
  },
  amount: {
    fontSize: 22,
    color: '#F4F5FB',
    fontFamily: 'Inter',
    lineHeight: 26.63,
    textRendering: 'geometricPrecision',
    fontWeight: '600',
  },
  containerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  cardContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
