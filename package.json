{"name": "finberry<PERSON>p", "main": "expo-router/entry", "version": "2.4.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/native": "^7.1.6", "@stablelib/aes": "^2.0.1", "@stablelib/base64": "^2.0.1", "@stablelib/gcm": "^2.0.1", "axios": "^1.9.0", "expo": "53.0.11", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.0", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.2.0", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "^7.1.5", "expo-local-authentication": "~16.0.4", "expo-modules-core": "^2.4.2", "expo-notifications": "~0.31.4", "expo-router": "~5.0.6", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-updates": "~0.28.12", "libphonenumber-js": "^1.12.9", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-html-to-pdf": "^0.12.0", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "15.11.2", "react-native-view-shot": "~4.0.3", "react-native-web": "^0.20.0", "expo-print": "~14.1.4", "expo-file-system": "~18.1.11", "expo-asset": "~11.1.7"}, "devDependencies": {"@babel/core": "^7.27.1", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "eslint": "^9.26.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}