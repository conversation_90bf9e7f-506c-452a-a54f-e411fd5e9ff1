import { useState, useEffect } from 'react';
import { useNavigation } from 'expo-router';

export const usePreventDoubleClick = () => {
  const [isDisabled, setIsDisabled] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      // Re-enable buttons when screen comes into focus
      setIsDisabled(false);
    });

    return unsubscribe;
  }, [navigation]);

  const handlePress = (callback: () => void) => {
    if (!isDisabled) {
      setIsDisabled(true);
      callback();
    }
  };

  return { isDisabled, handlePress };
};
