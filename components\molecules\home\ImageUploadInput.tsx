import { View, StyleSheet, Pressable, TouchableOpacity } from 'react-native';
import GoldGradientText from '@/components/atoms/GoldGradientText';
import UploadImage from '@/components/icons/UploadImage';
import UploadImageDone from '@/components/icons/UploadImageDone';

type Props = {
    imageName?: string;
    removeImage: () => void;
    onPress?: () => void;
}

const ImageUploadInput = ({
    imageName,
    removeImage,
    onPress,
}: Props) => {
    const formatImageName = (name: string | undefined) => {
        if (!name) return 'Cargar imagen';

        const lastDotIndex = name.lastIndexOf('.');
        if (lastDotIndex === -1) return name.slice(0, 5);

        const nameWithoutExt = name.slice(0, lastDotIndex);
        const extension = name.slice(lastDotIndex);
        return `${nameWithoutExt.slice(0, 5)}${extension}`;
    };

    return (
        <View>
            <Pressable onPress={onPress}>
                <View style={[
                    styles.wrapper,
                    {
                        backgroundColor:
                            imageName ? '#000000' : '#ffffff'
                    }
                ]}>
                    {imageName ? <UploadImageDone /> : <UploadImage />}
                    <GoldGradientText
                        text={formatImageName(imageName)}
                        style={{ fontSize: 13 }}
                    />
                </View>
            </Pressable>
            <TouchableOpacity
                style={{
                    marginTop: 7,
                }}
                onPress={removeImage}
            >
                <GoldGradientText
                    text='Eliminar archivo'
                    widthWrapper={100}
                    style={{
                        fontSize: 12,
                    }}
                />
            </TouchableOpacity>
        </View>
    );
};

export default ImageUploadInput;

const styles = StyleSheet.create({
    wrapper: {
        borderColor: '#000000',
        borderWidth: 1,
        borderRadius: 8,
        borderStyle: 'dashed',
        maxWidth: 100,
        width: '100%',
        height: 100,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#ffffff',
        gap: 2
    },
});