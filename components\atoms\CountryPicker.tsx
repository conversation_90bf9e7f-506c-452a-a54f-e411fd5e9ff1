import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
} from 'react-native';
import { getCountries, getCountryCallingCode } from 'libphonenumber-js';

type Country = {
  code: string;
  emoji: string;
  name: string;
  callingCode: string;
};

type Props = {
  selectedCountry: string;
  onSelect: (country: Country) => void;
};

const CountryPicker = ({ selectedCountry, onSelect }: Props) => {
  const [modalVisible, setModalVisible] = useState(false);

  const countries: Country[] = getCountries().map(code => ({
    code,
    emoji: getEmojiFlag(code),
    name: getCountryName(code),
    callingCode: `+${getCountryCallingCode(code)}`,
  }));

  const handleSelect = (country: Country) => {
    onSelect(country);
    setModalVisible(false);
  };

  const selected = countries.find(c => c.code === selectedCountry);

  return (
    <View>
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setModalVisible(true)}
      >
        <Text>{selected?.emoji} {selected?.callingCode}</Text>
      </TouchableOpacity>

      <Modal visible={modalVisible} animationType="slide">
        <View style={styles.modalContainer}>
          <TouchableOpacity
            onPress={() => setModalVisible(false)}
            style={styles.closeIcon}
          >
            <Text style={styles.closeText}>×</Text>
          </TouchableOpacity>

          <FlatList
            data={countries}
            keyExtractor={(item) => item.code}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.item}
                onPress={() => handleSelect(item)}
              >
                <Text>{item.emoji} {item.name} ({item.callingCode})</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>
    </View>
  );
};

// Función para obtener nombre del país (puedes reemplazar por i18n si prefieres)
const getCountryName = (code: string) => {
  try {
    return new Intl.DisplayNames(['es'], { type: 'region' }).of(code) || code;
  } catch {
    return code;
  }
};

// Emoji de la bandera 🇲🇽
const getEmojiFlag = (countryCode: string) =>
  countryCode
    .toUpperCase()
    .replace(/./g, char =>
      String.fromCodePoint(127397 + char.charCodeAt(0))
    );

const styles = StyleSheet.create({
  selector: {
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 0, // ← quita el espacio innecesario
    height: 48, // ← mismo alto que el contenedor padre
  },
  item: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  closeButton: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ddd',
  },
  modalContainer: {
    flex: 1,
    paddingTop: 40,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },

  closeIcon: {
    position: 'absolute',
    top: 10,
    right: 16,
    zIndex: 1,
    padding: 10,
  },

  closeText: {
    fontSize: 32,
    fontWeight: '600',
  },

});

export default CountryPicker;
