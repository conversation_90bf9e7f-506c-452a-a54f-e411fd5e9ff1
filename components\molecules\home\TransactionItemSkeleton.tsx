import SkeletonLoader from '@/components/atoms/SkeletonLoader';
import React from 'react';
import { View, StyleSheet } from 'react-native'

const TransactionItemSkeleton = () => {
  return (
    <View style={styles.transactionItem}>
      <View style={styles.transactionDetails}>
        <SkeletonLoader width={40} height={40} style={styles.transactionImage} />
        <View style={{ gap: 4 }}>
          <SkeletonLoader width={100} height={14} />
          <SkeletonLoader width={140} height={14} />
          <SkeletonLoader width={80} height={12} />
        </View>
      </View>
      <SkeletonLoader width={60} height={14} />
    </View>
  );
};

export default TransactionItemSkeleton;

const styles = StyleSheet.create({
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  transactionDetails: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  transactionImage: {
    borderRadius: 20,
  },
});
