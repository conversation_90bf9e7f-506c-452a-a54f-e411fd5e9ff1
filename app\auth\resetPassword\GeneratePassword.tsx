import CreatePassword from '@/components/organisms/login/CreatePassword';
import { resetPassword } from '@/services/userService';
import { router, useLocalSearchParams } from 'expo-router';
import { useState } from 'react';

// Componente principal para generar una contraseña
const GeneratePassword = () => {
  // Obtiene el parámetro de búsqueda local 'email'
  const { email } = useLocalSearchParams();
  // Estado para manejar el estado de carga
  const [loading, setLoading] = useState(false);
  // Estado para manejar los errores
  const [error, setError] = useState<string | null>(null);

  // Función para manejar el guardado de la contraseña
  const hadleSave = async (password: string, confirmPassword: string) => {
    // Reiniciar el error
    setError(null);
    // Iniciar el estado de carga
    setLoading(true);

    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden')
      return;
    }

    const response = await resetPassword(Array.isArray(email) ? email[0] : email, password);
    if (response.statusCode === 200 && password === confirmPassword) {
      // Terminar el estado de carga
      setLoading(false);
      // Redirigir a la página principal
      router.replace('/auth/resetPassword/SuccessRecoveryPassword');
    }
  };

  // Renderizar el componente CreatePassword con las propiedades necesarias
  return (
    <CreatePassword
      title='Restablecer contraseña'
      subTitle='Su nueva contraseña debe ser diferente de las utilizadas anteriormente.'
      textButton='Restablecer contraseña'
      loading={loading}
      error={error}
      onPress={hadleSave}
      setError={setError}
    />
  );
};

export default GeneratePassword;
