import {
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useState } from 'react';
import TransactionItem from './TransactionItem';
import { router } from 'expo-router';
import { useTransactionContext } from '@/context/TransactionContext';
import TransactionItemSkeleton from './TransactionItemSkeleton';

// Componente principal que muestra la lista de transacciones
const TransactionList = () => {
  const { transactions, isLoading } = useTransactionContext();
  const [isNavigating, setIsNavigating] = useState(false);

  const handleNavigation = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.push({
      pathname: '/(homeStack)/movements',
    });
    // Reestablecemos el estado después de un breve delay para asegurar que la navegación ha comenzado
    setTimeout(() => {
      setIsNavigating(false);
    }, 1000);
  };

  return (
    // Contenedor principal de la lista
    <View style={styles.bottomContainer}>
      <View style={{ paddingHorizontal: 24 }}>
        {/* Componente de cabecera de la lista */}
        <View style={styles.titleListContainer}>
          <Text style={styles.titleList}>Movimientos</Text>
          <TouchableOpacity
            style={[styles.whiteButton, isNavigating && styles.disabledButton]}
            onPress={handleNavigation}
            disabled={isNavigating}
          >
            <Text style={[styles.whiteButtonText, isNavigating && styles.disabledText]}>Ver todos</Text>
          </TouchableOpacity>
        </View>

        <View style={{ gap: 16 }}>
          {transactions.slice(0, 5).map((item) => (
            <React.Fragment key={item.id}>
              {isLoading ? <TransactionItemSkeleton /> : <TransactionItem item={item} />}
            </React.Fragment>
          ))}
        </View>
      </View>
    </View>
  );
};

export default TransactionList;

// Estilos para los componentes
const styles = StyleSheet.create({
  bottomContainer: {
    backgroundColor: '#E6E8F4',
    flex: 1,
    paddingBottom: Platform.OS === 'ios' ? 0 : 10,
  },
  titleListContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 24,
    paddingBottom: 8,
  },
  titleList: {
    fontSize: 22,
    color: '#000',
    fontFamily: 'Inter',
    fontWeight: '600',
    lineHeight: 26.63,
  },
  whiteButton: {
    backgroundColor: '#E6E8F4',
    borderColor: '#000',
    borderWidth: 1,
    paddingVertical: 10.5,
    paddingHorizontal: 22.5,
    borderRadius: 8,
    height: 40,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  blackButton: {
    backgroundColor: '#4A4B55',
    borderWidth: 1,
    paddingVertical: 10.5,
    paddingHorizontal: 22.5,
    borderRadius: 8,
    height: 40,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  whiteButtonText: {
    color: '#000',
    fontSize: 16,
    fontFamily: 'Inter',
    fontWeight: '600',
    lineHeight: 19.36,
  },
  disabledButton: {
    opacity: 0.5,
  },
  disabledText: {
    opacity: 0.5,
  },
  blackButtonText: {
    color: '#E6E8F4',
    fontSize: 16,
    fontFamily: 'Inter',
    fontWeight: '600',
    lineHeight: 19.36,
  },
});
