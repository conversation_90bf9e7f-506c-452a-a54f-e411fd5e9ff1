import React, { useState } from 'react';
import PageBaseEnterPassword from '@/components/templates/PageBaseEnterPassword';
import { router, useLocalSearchParams } from 'expo-router';
import { validatePasswordToTransfer } from '@/services/transfersService';
import { useUserContext } from '@/context/UserContext';
import { StatusBar } from 'react-native'

const PasswordSeeNip = () => {
  const { cardId } = useLocalSearchParams();
  const [password, setPassword] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { user } = useUserContext();

  const handleNext = async () => {
    setError(null);
    if (password.length === 0) {
      setError('Ingresa tu contraseña');
      return;
    }

    try {
      setLoading(true);
      const isValid = await validatePasswordToTransfer(user?.email!, password);
      if (isValid) {
        router.replace({
          pathname: '/(homeStack)/myCards/SeeNip',
          params: {
            cardId: cardId,
          }
        });
      } else {
        setError('Contraseña incorrecta, intenta de nuevo');
      }
      setLoading(false);
      // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    } catch (err: unknown) {
      setError('Error al validar la contraseña');
      setLoading(false);
    }
  };
  const handleBack = () => router.back();

  return (
    <>
      <StatusBar backgroundColor='#ffffff' barStyle='dark-content' translucent={false} />
      <PageBaseEnterPassword
        title='Ingresa tu contraseña'
        password={password}
        passwordError={error}
        loading={loading}
        onContinue={handleNext}
        onGoBack={handleBack}
        onSetPassword={setPassword}
        onSetPasswordError={setError}
      />
    </>
  );
};

export default PasswordSeeNip;
